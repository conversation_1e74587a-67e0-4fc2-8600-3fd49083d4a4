{"D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\AppScope\\app.json5": {"hashValue": "b272ae09c35f24ae3fab922faad24c82", "name": "app.json5", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\AppScope\\app.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 883, "lastModifiedTime": 1749022453838}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\module.json5": {"hashValue": "8a89638127253b3d548cd28f1b96f6c1", "name": "module.json5", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\module.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2563, "lastModifiedTime": 1749024082080}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\build-profile.json5": {"hashValue": "5bed5bfd4578bc5ef06553618921ac62", "name": "build-profile.json5", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\build-profile.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1951, "lastModifiedTime": 1749025881252}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build-profile.json5": {"hashValue": "a6f543b6c877af84066d4050145db38e", "name": "build-profile.json5", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build-profile.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1015, "lastModifiedTime": 1749024082077}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\profile\\main_pages.json": {"hashValue": "5ddc969a7aad41d4c4c22edd772abc8a", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 42, "lastModifiedTime": 1749022453893}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\oh-package.json5": {"hashValue": "dfebcc02ee1adc714cb3f7a31f3f6448", "name": "oh-package.json5", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\oh-package.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 248, "lastModifiedTime": 1749022453845}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets": {"hashValue": "14ab0c335794ae813476b6447da6c7ad", "name": "BuildProfile.ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 371, "lastModifiedTime": 1749027219630}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json": {"hashValue": "fdb71b4c4f07970139f6d8e6bc7a8607", "name": "loader.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 788, "lastModifiedTime": 1749027219657}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json": {"hashValue": "97597e197d102ea2f5bf978d62adefe0", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1992, "lastModifiedTime": 1749027219670}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json": {"hashValue": "e1b36ffc50fcd60e2df07c11b63152d1", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2078, "lastModifiedTime": 1749027219863}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources": {"hashValue": "31cbb6f14ce0b66d6da5b0c3c2b0f1f8", "name": "resources", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "4f9d140339e81b34f5e3d07a5a5994f5", "name": "base", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "8375a0c8f7e0cd7ea2c936983c993ade", "name": "element", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "045277200a69951ccd3352446594fc69", "name": "color.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\element\\color.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 241, "lastModifiedTime": 1749022453863}}, {"hashValue": "0e847e049e12144d3f7ffc34796d1fbc", "name": "string.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\element\\string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 272, "lastModifiedTime": 1749022453863}}]}, {"hashValue": "5ded41793140ea643198cebd4ba59fb8", "name": "media", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6f84b90d6a6f23540cb85f54a38f45c5", "name": "camera_pause_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\camera_pause_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10632, "lastModifiedTime": 1749022453864}}, {"hashValue": "387593473c128ab070274fbcc65a7bd4", "name": "camera_switch_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\camera_switch_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6926, "lastModifiedTime": 1749022453864}}, {"hashValue": "cd9e2c9fa9a4a73403e54b0c86ddfa5a", "name": "camera_take_photo_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\camera_take_photo_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14016, "lastModifiedTime": 1749022453865}}, {"hashValue": "69e1c567cd44f6edc7ee1108440ac0e0", "name": "camera_take_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\camera_take_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 15819, "lastModifiedTime": 1749022453865}}, {"hashValue": "7789281643ea3f6b4a227a7e12b4d39a", "name": "camera_thumbnail_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\camera_thumbnail_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7152, "lastModifiedTime": 1749022453866}}, {"hashValue": "63b028c7353011ee8adfa63c3a967fb0", "name": "flash_always_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\flash_always_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1898, "lastModifiedTime": 1749022453866}}, {"hashValue": "90f460eaae837069a6b51717992eee7c", "name": "ic_camera_ic_camera_radio_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_ic_camera_radio_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 795, "lastModifiedTime": 1749022453867}}, {"hashValue": "93c1941a2022ca17176f3981b58d735b", "name": "ic_camera_pad.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_pad.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1073, "lastModifiedTime": 1749022453868}}, {"hashValue": "84ffec0a6189cfd2de43aaca147901d0", "name": "ic_camera_phone.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_phone.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1339, "lastModifiedTime": 1749022453868}}, {"hashValue": "3843a663a62b92f83897b2b627e5d8f0", "name": "ic_camera_progressBar_circle.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_progressBar_circle.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1091, "lastModifiedTime": 1749022453868}}, {"hashValue": "2b4559aff0a3e5a95b8ce3cf9c9f91c4", "name": "ic_camera_public_flash_auto_white.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_public_flash_auto_white.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "8fe205be5fdbe2c82d370016417c2ff3", "name": "ic_camera_public_flash_auto.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_public_flash_auto.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "c863a453524f35a4d3371b7f3bc11897", "name": "ic_camera_public_flash_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_public_flash_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2091, "lastModifiedTime": 1749022453870}}, {"hashValue": "8c5cbaa8ba0a62653b6cc2b11400b670", "name": "ic_camera_public_flash_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_public_flash_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1487, "lastModifiedTime": 1749022453870}}, {"hashValue": "7d2c5d39f46581e2607ec9c0a31e0543", "name": "ic_camera_radio_open.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_radio_open.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1762, "lastModifiedTime": 1749022453871}}, {"hashValue": "b3a59d24aa0a41c1bc430bbdb5e4d471", "name": "ic_camera_set__antishake.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set__antishake.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5218, "lastModifiedTime": 1749022453871}}, {"hashValue": "da4cb2c6f607f8ac528071d4c0a30d16", "name": "ic_camera_set__mirror.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set__mirror.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1627, "lastModifiedTime": 1749022453871}}, {"hashValue": "d59ac73d4ec951681a0203852b9155e2", "name": "ic_camera_set_af.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_af.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 916, "lastModifiedTime": 1749022453872}}, {"hashValue": "d3a93fe8d0efec060a6005c202d3107c", "name": "ic_camera_set_arrow.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_arrow.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1422, "lastModifiedTime": 1749022453872}}, {"hashValue": "a61b4feb69e03c3d958607666710a04e", "name": "ic_camera_set_checked.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_checked.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1400, "lastModifiedTime": 1749022453873}}, {"hashValue": "5ac22f0067e1d99287875612e013f929", "name": "ic_camera_set_class.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_class.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1090, "lastModifiedTime": 1749022453873}}, {"hashValue": "e80ef7c03ad20ec772bbce88000cd2d8", "name": "ic_camera_set_exposure.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_exposure.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2866, "lastModifiedTime": 1749022453873}}, {"hashValue": "ecc027f6a0e0c3eae8748fbfad1d5e89", "name": "ic_camera_set_focus.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_focus.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 519, "lastModifiedTime": 1749022453874}}, {"hashValue": "fbd4ea5b70c71cd78d16d314a4d2ec35", "name": "ic_camera_set_format.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_format.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4020, "lastModifiedTime": 1749022453874}}, {"hashValue": "6b1c2f6b0b3d2df44e34c54ae2f6009f", "name": "ic_camera_set_line.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_line.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1402, "lastModifiedTime": 1749022453875}}, {"hashValue": "8cc5166d9dda3639bbbf727e06e86379", "name": "ic_camera_set_location.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_location.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2508, "lastModifiedTime": 1749022453875}}, {"hashValue": "3517f4d041c08a7472d14e1bbd53fc34", "name": "ic_camera_set_pic_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_pic_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453876}}, {"hashValue": "530b5f9e2e6345b7f1f7e755565fc9ca", "name": "ic_camera_set_quality.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_quality.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13855, "lastModifiedTime": 1749022453876}}, {"hashValue": "1f7f4dda7b345ec5840d5c5ceb86b8cc", "name": "ic_camera_set_video_rate.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_video_rate.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1235, "lastModifiedTime": 1749022453876}}, {"hashValue": "9f57de8ee52701eb4f8d4bd2f4fa06fc", "name": "ic_camera_set_video_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_video_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1485, "lastModifiedTime": 1749022453878}}, {"hashValue": "bef04f114418568acf0698a4597eb05a", "name": "ic_camera_switch_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_switch_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1354, "lastModifiedTime": 1749022453878}}, {"hashValue": "2f057ea2d566c1e0599014c2c5811837", "name": "ic_camera_switch_off2.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_switch_off2.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 974, "lastModifiedTime": 1749022453879}}, {"hashValue": "2cc3922cf58c19c1889223ea018b80db", "name": "ic_camera_video_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_video_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 441, "lastModifiedTime": 1749022453879}}, {"hashValue": "f5b428f1a8e5eedbd02a34e3fa7cb2b8", "name": "ic_camera_video_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_video_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453879}}, {"hashValue": "58a400a0045d317e7adf29ae8c037190", "name": "ic_camera_video_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_video_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 913, "lastModifiedTime": 1749022453880}}, {"hashValue": "b3fad9c09dce8787b994d81631d29879", "name": "ic_public_brightness_filled.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_public_brightness_filled.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2685, "lastModifiedTime": 1749022453881}}, {"hashValue": "9693142322f4eafa66f416622e0f0280", "name": "ic_public_brightness.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_public_brightness.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2862, "lastModifiedTime": 1749022453880}}, {"hashValue": "d3b999e99f9c08f171ebe7ab09188297", "name": "icon_camera_setting_timer_on_balk.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\icon_camera_setting_timer_on_balk.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453883}}, {"hashValue": "cb13e3152b25a046c7dd36edef7aa479", "name": "icon_camera_setting_timer_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\icon_camera_setting_timer_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453882}}, {"hashValue": "e9b0ad9dbf060377c7132d87af8c93b4", "name": "icon_camera_setting_timer.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\icon_camera_setting_timer.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3664, "lastModifiedTime": 1749022453882}}, {"hashValue": "f081cce0a4620543cff83f556e7aa7bd", "name": "icon_camera_setting.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\icon_camera_setting.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3573, "lastModifiedTime": 1749022453882}}, {"hashValue": "d1044eb267800fc10fb896e22a11dd41", "name": "icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453881}}, {"hashValue": "be67575a938820711272da8fdfb4a840", "name": "pic_avatar_radio01.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_avatar_radio01.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11660, "lastModifiedTime": 1749022453883}}, {"hashValue": "58f3bcfc0169fd61ce206066c6f9794d", "name": "pic_avatar_radio02.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_avatar_radio02.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 12092, "lastModifiedTime": 1749022453884}}, {"hashValue": "a12dac4cf6f64a5967872296c6169422", "name": "pic_avatar_regular.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_avatar_regular.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14162, "lastModifiedTime": 1749022453884}}, {"hashValue": "e1277770a0627be49b7ee1ed38f0c410", "name": "pic_camera_line.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_camera_line.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 243206, "lastModifiedTime": 1749022453886}}, {"hashValue": "2f590cb280d1d441b49a19b1003b5bd6", "name": "pic_camera_mirror.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_camera_mirror.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 111725, "lastModifiedTime": 1749022453888}}, {"hashValue": "528da4db4b0c74b4ab1f781b58d0b90f", "name": "pic_popup_left.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_popup_left.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 486207, "lastModifiedTime": 1749022453890}}, {"hashValue": "55e8b4f62ca7ff5d41d4ebd02f96b6ec", "name": "pic_popup_right.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_popup_right.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 424653, "lastModifiedTime": 1749022453892}}, {"hashValue": "854908744dd8a55925c5fa3bfe59b7e4", "name": "switch_camera.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\media\\switch_camera.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2350, "lastModifiedTime": 1749022453892}}]}, {"hashValue": "1752577f66bd1a3d28dca9eee8d5043f", "name": "profile", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5ddc969a7aad41d4c4c22edd772abc8a", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 42, "lastModifiedTime": 1749022453893}}]}]}, {"hashValue": "fb73190687b4b3400e7a2b50b517f5c7", "name": "en_US", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\en_US", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "a4bac92b2c6e07a678f2adcdbb4c8489", "name": "element", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\en_US\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "f55483b907c93ff2167cd50c4eab5a30", "name": "string.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\en_US\\element\\string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 272, "lastModifiedTime": 1749022453894}}]}]}, {"hashValue": "d678701bc92e3c634d339130a904bca2", "name": "zh_CN", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\zh_CN", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "eafc7f9552b4fd1a785b2c4d4a9361ba", "name": "element", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\zh_CN\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c631cc9d37ee8466504a4d24c382d0e2", "name": "string.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\resources\\zh_CN\\element\\string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 266, "lastModifiedTime": 1749022453894}}]}]}]}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default": {"hashValue": "d8e96164ade7aa48e1af524308b64e56", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "97207bd9f137deb52f6c319de5dc54c1", "name": "app_compiled", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "7fa8ce4c17bb36d497db811837d31752", "name": "15025379605101891041", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled\\15025379605101891041", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 312, "lastModifiedTime": 1749027219988}}, {"hashValue": "156996f98432d14ca9b5fe05a2d39f21", "name": "7448502331763171465", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled\\7448502331763171465", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10206, "lastModifiedTime": 1749027219987}}]}, {"hashValue": "2c0a7d828dcbdfe4c8cfc9be3c65ddae", "name": "ids_map", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "1bbb5a90752847497b19f2b19f446804", "name": "id_defined.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map\\id_defined.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13907, "lastModifiedTime": 1749027220182}}]}, {"hashValue": "d5c8efb35bf408ccac09f7800cc8ead4", "name": "module_compiled", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "65ce0a3937fd88d717ef8d44f118e9b1", "name": "10031437644502420183", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\10031437644502420183", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 360, "lastModifiedTime": 1749027220061}}, {"hashValue": "591839fd413c36bff65d3bc01045be8e", "name": "10269858998317723741", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\10269858998317723741", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 345, "lastModifiedTime": 1749027220058}}, {"hashValue": "b12d7e938c1abe607c42a6cb5a8bebe9", "name": "10658854651895764811", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\10658854651895764811", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 363, "lastModifiedTime": 1749027220062}}, {"hashValue": "d2f0a549382e357c813520b414932192", "name": "1075241210794996715", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\1075241210794996715", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 657, "lastModifiedTime": 1749027220077}}, {"hashValue": "1c696b5622ec5d0c655664057c01775b", "name": "10780049855247985285", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\10780049855247985285", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 337, "lastModifiedTime": 1749027220076}}, {"hashValue": "d8cd1a12f11ec52307e9bf92ed4131d3", "name": "11313913877203597306", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\11313913877203597306", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 354, "lastModifiedTime": 1749027220065}}, {"hashValue": "5bdd2fb5aae68190591799ea059f39e1", "name": "11384370409794078740", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\11384370409794078740", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 360, "lastModifiedTime": 1749027220072}}, {"hashValue": "6ef319fb376eb62449f2df9b9c99ea02", "name": "11502735563912405315", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\11502735563912405315", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 339, "lastModifiedTime": 1749027220075}}, {"hashValue": "8ea2eb27d4d749e3b6c22cb66332af70", "name": "11906529480151008240", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\11906529480151008240", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 363, "lastModifiedTime": 1749027220067}}, {"hashValue": "9cb1a62ed1d875242e874fd3ae2cdb3d", "name": "11978108995089152345", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\11978108995089152345", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 345, "lastModifiedTime": 1749027220055}}, {"hashValue": "88e00841eb099e817f0a0a14a0aebe13", "name": "12342656588954889358", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\12342656588954889358", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 363, "lastModifiedTime": 1749027220070}}, {"hashValue": "a0007cb8ab1bc0ec7d47106096ad28d9", "name": "12364364958894755073", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\12364364958894755073", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 363, "lastModifiedTime": 1749027220069}}, {"hashValue": "b9b439cef889b7a41a1f0fb323d00119", "name": "12394229562769414870", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\12394229562769414870", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 378, "lastModifiedTime": 1749027220059}}, {"hashValue": "492881437934d28e7c456e44debd14a1", "name": "12430216755797936125", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\12430216755797936125", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 348, "lastModifiedTime": 1749027220052}}, {"hashValue": "0f965e62b1f841b84706ab2587e19cfc", "name": "13103159322643272503", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\13103159322643272503", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 339, "lastModifiedTime": 1749027220057}}, {"hashValue": "c6c749dd40466bbf05c67034c30beb2d", "name": "13266998365199988860", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\13266998365199988860", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 354, "lastModifiedTime": 1749027220073}}, {"hashValue": "ccd5519a17a0ad54d034bd56b85504df", "name": "13467799927927032199", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\13467799927927032199", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 363, "lastModifiedTime": 1749027220052}}, {"hashValue": "ebcf49e8766187b773401d5a2b13241f", "name": "14397557164271203105", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\14397557164271203105", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 393, "lastModifiedTime": 1749027220057}}, {"hashValue": "a11e2df8a429236ad816b37999a95f35", "name": "145259091582652561", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\145259091582652561", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 363, "lastModifiedTime": 1749027220070}}, {"hashValue": "33cd73a586b05ccb1f6581b3aa3634a2", "name": "14744271280936981551", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\14744271280936981551", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 603, "lastModifiedTime": 1749027220047}}, {"hashValue": "157f029ce185877c1c1685b38bfbd096", "name": "14867924586150211468", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\14867924586150211468", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 357, "lastModifiedTime": 1749027220063}}, {"hashValue": "dd712f74ff6bcfc765b2eb32d2bec213", "name": "15046281155440809034", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\15046281155440809034", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 360, "lastModifiedTime": 1749027220070}}, {"hashValue": "f004170db408cf29bea3f95273d2a22a", "name": "15264969414732454920", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\15264969414732454920", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 360, "lastModifiedTime": 1749027220053}}, {"hashValue": "af425c078410e6be4a7df35b5ce51e60", "name": "15438567061278225935", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\15438567061278225935", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 354, "lastModifiedTime": 1749027220072}}, {"hashValue": "dfdeb6bd1fbdc7bee3b7bb78cbb42ddc", "name": "15634775335660710878", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\15634775335660710878", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 375, "lastModifiedTime": 1749027220056}}, {"hashValue": "5d1f31c1e459241b9985dea22082840a", "name": "15670602383386666168", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\15670602383386666168", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 354, "lastModifiedTime": 1749027220074}}, {"hashValue": "e3eb2747b9f3f8eed72b2aa0347c1beb", "name": "1568107650453127798", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\1568107650453127798", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 357, "lastModifiedTime": 1749027220064}}, {"hashValue": "b616fb39669272bdd2669b707ad13966", "name": "15835733561879573150", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\15835733561879573150", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 366, "lastModifiedTime": 1749027220063}}, {"hashValue": "2c7d296e1affc5900c08456234340e94", "name": "16473632012964833166", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\16473632012964833166", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 651, "lastModifiedTime": 1749027220078}}, {"hashValue": "922f7f756282f976a7f8c783c68c9a05", "name": "17061847513100344105", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\17061847513100344105", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 366, "lastModifiedTime": 1749027220065}}, {"hashValue": "1153b09706e8b41d41a8576b0205fd70", "name": "17197309034156340542", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\17197309034156340542", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 342, "lastModifiedTime": 1749027220075}}, {"hashValue": "ba0eef93c5953dbc04d0013ae93d8766", "name": "17205324991703395406", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\17205324991703395406", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 357, "lastModifiedTime": 1749027220071}}, {"hashValue": "550d8b67a5885313b17be10f9d7fbddb", "name": "1961021758867584600", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\1961021758867584600", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 384, "lastModifiedTime": 1749027220065}}, {"hashValue": "f57516b23bf6c107d4337428e5331c62", "name": "2347858293043610987", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\2347858293043610987", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 372, "lastModifiedTime": 1749027220068}}, {"hashValue": "ab9722bc98386eafe0da2915d13cac57", "name": "2628552536997826605", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\2628552536997826605", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 375, "lastModifiedTime": 1749027220060}}, {"hashValue": "e7a3e89572666d0324a5e6928d62ba02", "name": "3823906692346872586", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\3823906692346872586", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 372, "lastModifiedTime": 1749027220069}}, {"hashValue": "be6ee8981c01b060e3ca283ae74057bb", "name": "3860862889297986241", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\3860862889297986241", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 345, "lastModifiedTime": 1749027220075}}, {"hashValue": "1fb943b1c40ecfb4b524f1c43782acf3", "name": "4021520167800436305", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\4021520167800436305", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 399, "lastModifiedTime": 1749027220059}}, {"hashValue": "09d66522dc734c23b07e9163c2353ccc", "name": "4973594358323121561", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\4973594358323121561", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 357, "lastModifiedTime": 1749027220056}}, {"hashValue": "3b98ddb7bbbefb75d6e0ad7db9685c56", "name": "5115690245306365096", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\5115690245306365096", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 384, "lastModifiedTime": 1749027220058}}, {"hashValue": "ad6c6fe01efe339c86c7e4e281433312", "name": "5189703278350837105", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\5189703278350837105", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 381, "lastModifiedTime": 1749027220072}}, {"hashValue": "30965b7c91d5b62fb9c5fabb0b7c8fc4", "name": "5237871127027007686", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\5237871127027007686", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 360, "lastModifiedTime": 1749027220053}}, {"hashValue": "e7306fe4011caab034a7211563f32a93", "name": "5399592284125130409", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\5399592284125130409", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 357, "lastModifiedTime": 1749027220054}}, {"hashValue": "6716a66922f2bd58cdbb8df4f39731cc", "name": "6064767548712873951", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\6064767548712873951", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 354, "lastModifiedTime": 1749027220073}}, {"hashValue": "f165f989928fe4397bbe2f285a84f33a", "name": "6400271443241752340", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\6400271443241752340", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 360, "lastModifiedTime": 1749027220064}}, {"hashValue": "18a746c1313f6c25fe1ed72bfffcc378", "name": "6938118905363459045", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\6938118905363459045", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 384, "lastModifiedTime": 1749027220056}}, {"hashValue": "3c3a16c834437606a16ea37691c7364e", "name": "7349263278527294437", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\7349263278527294437", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 351, "lastModifiedTime": 1749027220074}}, {"hashValue": "0e780e4be9bde74e4e602f42aa93e8ef", "name": "7915847747355033935", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\7915847747355033935", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 381, "lastModifiedTime": 1749027220059}}, {"hashValue": "6cadcec12127b7e126ab2964dc719224", "name": "8146303230095879141", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\8146303230095879141", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 390, "lastModifiedTime": 1749027220068}}, {"hashValue": "ead2e1700267c7a2e1d5ce6ebfa409ed", "name": "8481957445231687548", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\8481957445231687548", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 570, "lastModifiedTime": 1749027220046}}, {"hashValue": "30b858f3e8ad732f420628cb0ce9500e", "name": "8816870167652543466", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\8816870167652543466", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 312, "lastModifiedTime": 1749027220055}}, {"hashValue": "4182a60e855314f97ec5bdfedd182344", "name": "8970096297051964748", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\8970096297051964748", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 345, "lastModifiedTime": 1749027220074}}, {"hashValue": "38fcff92d99ec6396220ffd0bc899497", "name": "9077978179120765516", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\9077978179120765516", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 399, "lastModifiedTime": 1749027220057}}, {"hashValue": "d7530c53f0afecccf01f89e4a5062460", "name": "9553027442121064059", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\9553027442121064059", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 348, "lastModifiedTime": 1749027220061}}, {"hashValue": "cf27c41b5699eb7dfd0545b4726b29c3", "name": "9794762257412612576", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\9794762257412612576", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 357, "lastModifiedTime": 1749027220061}}]}, {"hashValue": "5006d7b889d422ff1ae5fc456d2d59d0", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3800, "lastModifiedTime": 1749027220183}}, {"hashValue": "937a75ae983b82180943445ff6f5c64d", "name": "resources", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "48f4204413dfea225ad5bce2205cf7c1", "name": "base", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0fff28345df057f171166d47bb7c8ce3", "name": "profile", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "72cb42c7a06c4e43f92bf0ec2d8321b1", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 24, "lastModifiedTime": 1749027220213}}]}]}]}, {"hashValue": "b57ff2fbecbb069945fd9d40e9492983", "name": "resources.index", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources.index", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14267, "lastModifiedTime": 1749027220181}}, {"hashValue": "b9c2712f57b6272959117082d6e85fa3", "name": "ResourceTable.txt", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3976, "lastModifiedTime": 1749027220185}}]}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\generated\\r\\default": {"hashValue": "61025b4a0dd890b4fa153f9cf9e4a3c7", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\generated\\r\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "11e850d11ca67309ce1d0d4340d327c9", "name": "ResourceTable.h", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6552, "lastModifiedTime": 1749027220184}}]}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\config\\buildConfig.json": {"hashValue": "0aed946fbd6589ed09d126e16e3de8c2", "name": "buildConfig.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\config\\buildConfig.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2603, "lastModifiedTime": 1749029265574}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module.json": {"hashValue": "5006d7b889d422ff1ae5fc456d2d59d0", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3800, "lastModifiedTime": 1749027220183}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json": {"hashValue": "79e6a82aebdf609fb89ba9601b70e2b4", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 24, "lastModifiedTime": 1749029267846}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\loader\\default": {"hashValue": "4342b46651802522af4a09dd72a9934f", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\loader\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "fdb71b4c4f07970139f6d8e6bc7a8607", "name": "loader.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 788, "lastModifiedTime": 1749027219657}}]}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt": {"hashValue": "b9c2712f57b6272959117082d6e85fa3", "name": "ResourceTable.txt", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3976, "lastModifiedTime": 1749027220185}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile": {"hashValue": "f78d0dfaf14afb71807d8ac1b4689319", "name": "profile", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "79e6a82aebdf609fb89ba9601b70e2b4", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 24, "lastModifiedTime": 1749029267846}}]}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets": {"hashValue": "e975d13e36b82c96027e38615371a226", "name": "ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "210e0c050b75f1a5b617344717096c88", "name": "common", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\common", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "2fa07f50c1c40e9f2da16e0be769ad39", "name": "Constants.ts", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\common\\Constants.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2352, "lastModifiedTime": 1749022453851}}, {"hashValue": "726cfaeed739814f125de17b312a4dec", "name": "DisplayCalculator.ts", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\common\\DisplayCalculator.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1797, "lastModifiedTime": 1749022453851}}, {"hashValue": "ebfbe2c26e338083ae725beafd6ca345", "name": "SettingItem.ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\common\\SettingItem.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4340, "lastModifiedTime": 1749022453852}}, {"hashValue": "614e88984a4b6d11c8fbb1ada155501d", "name": "SettingPublicLayout.ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\common\\SettingPublicLayout.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3575, "lastModifiedTime": 1749022453852}}, {"hashValue": "f67bd173635ebfaec6c113f9f803cde0", "name": "SettingRightLayout.ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\common\\SettingRightLayout.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6234, "lastModifiedTime": 1749022453853}}]}, {"hashValue": "d823d5d0172d34fa1662c9331c64f1b9", "name": "Dialog", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\Dialog", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "a31963b78e2f77c76bb4443d0cff6084", "name": "MainDialog.ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\Dialog\\MainDialog.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2977, "lastModifiedTime": 1749022453850}}, {"hashValue": "ad0ad06a44cd971cf2765aa35030ad1c", "name": "SettingDialog.ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\Dialog\\SettingDialog.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7728, "lastModifiedTime": 1749022453850}}]}, {"hashValue": "d24c9916b230ea26067a94699aa21881", "name": "entryability", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\entryability", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "a30db74181c55d3f533ec7fd54252883", "name": "EntryAbility.ts", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\entryability\\EntryAbility.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2368, "lastModifiedTime": 1749022453853}}]}, {"hashValue": "a07cbc9dc2af3dc47dfbc9a697878474", "name": "model", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\model", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ddf863ab55b13ec8adaed4f1847b3f00", "name": "DateTimeUtil.ts", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\model\\DateTimeUtil.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2062, "lastModifiedTime": 1749022453854}}, {"hashValue": "7b99e20512aba2ff6fd9ee45f98d0e0e", "name": "Logger.ts", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\model\\Logger.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1312, "lastModifiedTime": 1749022453854}}, {"hashValue": "af6f5a81029b1c079b6616dbd7dca9cd", "name": "MediaUtils.ts", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\model\\MediaUtils.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3458, "lastModifiedTime": 1749022453855}}]}, {"hashValue": "861d41a7b95078e8ddf1b0086c609709", "name": "pages", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\pages", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "7e9006753b4da2ad164eacb21b5c4f12", "name": "Index.ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\pages\\Index.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7948, "lastModifiedTime": 1749022453855}}]}, {"hashValue": "98bbdcbf4bb7ea8ec16ce098ca5504d2", "name": "views", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\views", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5c2ab13cd5e50160be16ca7fa35df0e3", "name": "CountdownPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\views\\CountdownPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3166, "lastModifiedTime": 1749022453856}}, {"hashValue": "3892e004ba5eb99c243ef912ba5fe010", "name": "DividerPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\views\\DividerPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1150, "lastModifiedTime": 1749022453856}}, {"hashValue": "72a84af30d3bc4a5cd0d8ca43e89c7a6", "name": "FlashingLightPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\views\\FlashingLightPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3914, "lastModifiedTime": 1749022453857}}, {"hashValue": "0562df559ccfdde6befb95b0d1914d0d", "name": "FocusAreaPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\views\\FocusAreaPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3362, "lastModifiedTime": 1749022453857}}, {"hashValue": "553ac0d07c4c6127f9f79c5a8d9d75ac", "name": "FocusPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\views\\FocusPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4414, "lastModifiedTime": 1749022453858}}, {"hashValue": "7c65c6b53549f1b3c9e5ebb1b43ede8e", "name": "ModeSwitchPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\views\\ModeSwitchPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 16947, "lastModifiedTime": 1749024082079}}, {"hashValue": "a885876ccb165b0c0b76514cf6b93e29", "name": "SlidePage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\src\\main\\ets\\views\\SlidePage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2740, "lastModifiedTime": 1749022453861}}]}]}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json": {"hashValue": "9f1c5c6d3b251c0cf0111ca08e4e03e9", "name": "output_metadata.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 95, "lastModifiedTime": 1749025840741}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets": {"hashValue": "506db3611a6648272a3ec0e9b494ed58", "name": "BuildProfile.ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 371, "lastModifiedTime": 1749025840752}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\loader\\default\\loader.json": {"hashValue": "b839822c4454f591a3e53d2ed90adbef", "name": "loader.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 782, "lastModifiedTime": 1749025840778}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json": {"hashValue": "3a0caa8775869741b70024e98e0056ce", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1992, "lastModifiedTime": 1749025840787}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\outputs\\default\\pack.info": {"hashValue": "ec40e1ccb43a6c5327aba8d8cf1f8fb2", "name": "pack.info", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\outputs\\default\\pack.info", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 556, "lastModifiedTime": 1749025840806}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json": {"hashValue": "e3fdfeaf1357bd333ea66c52b976838d", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2078, "lastModifiedTime": 1749025840986}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json": {"hashValue": "5cf5e923859dc73fac7f91a5f7059b12", "name": "resConfig.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1305, "lastModifiedTime": 1749025840999}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\AppScope\\resources": {"hashValue": "7bc75c4a9d7c24ed0ab0acdf64a9bd55", "name": "resources", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\AppScope\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "563d683e2ba4327fdb3be313d2b08fd9", "name": "base", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\AppScope\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c5e816eee4bff7277f64b88b80c46bd4", "name": "element", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\AppScope\\resources\\base\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "69dcc072c81241b9f0f10acda170c627", "name": "string.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\AppScope\\resources\\base\\element\\string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4405, "lastModifiedTime": 1749022453839}}]}, {"hashValue": "e21a9b0a678508e904265d0756c5a3b0", "name": "media", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\AppScope\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0b478312ebe80ab6e93b97a4ce5c0448", "name": "app_icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\AppScope\\resources\\base\\media\\app_icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453840}}]}]}]}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default": {"hashValue": "05a602fecae1c1cf2cd7bdbe6f7aeba2", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "b8acdf1fc75b976753c347dc044b9a60", "name": "ids_map", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ids_map", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "17a5aaded626fbab306e22e8ba6b9c7b", "name": "id_defined.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ids_map\\id_defined.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13907, "lastModifiedTime": 1749025841270}}]}, {"hashValue": "0e03c32f7872afdd6fe45ebb54a495e7", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3800, "lastModifiedTime": 1749025841268}}, {"hashValue": "5cf5e923859dc73fac7f91a5f7059b12", "name": "resConfig.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1305, "lastModifiedTime": 1749025840999}}, {"hashValue": "7c74b7971302f5ae70c88097521871b6", "name": "resources", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6dcaabd12ffee1d3c9a6d6bc203af712", "name": "base", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "627e3a54a0c7d5cee1e431efa84443a3", "name": "media", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0b478312ebe80ab6e93b97a4ce5c0448", "name": "app_icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\app_icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453840}}, {"hashValue": "6f84b90d6a6f23540cb85f54a38f45c5", "name": "camera_pause_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_pause_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10632, "lastModifiedTime": 1749022453864}}, {"hashValue": "387593473c128ab070274fbcc65a7bd4", "name": "camera_switch_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_switch_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6926, "lastModifiedTime": 1749022453864}}, {"hashValue": "cd9e2c9fa9a4a73403e54b0c86ddfa5a", "name": "camera_take_photo_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_take_photo_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14016, "lastModifiedTime": 1749022453865}}, {"hashValue": "69e1c567cd44f6edc7ee1108440ac0e0", "name": "camera_take_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_take_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 15819, "lastModifiedTime": 1749022453865}}, {"hashValue": "7789281643ea3f6b4a227a7e12b4d39a", "name": "camera_thumbnail_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_thumbnail_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7152, "lastModifiedTime": 1749022453866}}, {"hashValue": "63b028c7353011ee8adfa63c3a967fb0", "name": "flash_always_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\flash_always_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1898, "lastModifiedTime": 1749022453866}}, {"hashValue": "90f460eaae837069a6b51717992eee7c", "name": "ic_camera_ic_camera_radio_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_ic_camera_radio_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 795, "lastModifiedTime": 1749022453867}}, {"hashValue": "93c1941a2022ca17176f3981b58d735b", "name": "ic_camera_pad.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_pad.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1073, "lastModifiedTime": 1749022453868}}, {"hashValue": "84ffec0a6189cfd2de43aaca147901d0", "name": "ic_camera_phone.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_phone.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1339, "lastModifiedTime": 1749022453868}}, {"hashValue": "3843a663a62b92f83897b2b627e5d8f0", "name": "ic_camera_progressBar_circle.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_progressBar_circle.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1091, "lastModifiedTime": 1749022453868}}, {"hashValue": "2b4559aff0a3e5a95b8ce3cf9c9f91c4", "name": "ic_camera_public_flash_auto_white.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_auto_white.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "8fe205be5fdbe2c82d370016417c2ff3", "name": "ic_camera_public_flash_auto.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_auto.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "c863a453524f35a4d3371b7f3bc11897", "name": "ic_camera_public_flash_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2091, "lastModifiedTime": 1749022453870}}, {"hashValue": "8c5cbaa8ba0a62653b6cc2b11400b670", "name": "ic_camera_public_flash_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1487, "lastModifiedTime": 1749022453870}}, {"hashValue": "7d2c5d39f46581e2607ec9c0a31e0543", "name": "ic_camera_radio_open.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_radio_open.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1762, "lastModifiedTime": 1749022453871}}, {"hashValue": "b3a59d24aa0a41c1bc430bbdb5e4d471", "name": "ic_camera_set__antishake.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set__antishake.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5218, "lastModifiedTime": 1749022453871}}, {"hashValue": "da4cb2c6f607f8ac528071d4c0a30d16", "name": "ic_camera_set__mirror.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set__mirror.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1627, "lastModifiedTime": 1749022453871}}, {"hashValue": "d59ac73d4ec951681a0203852b9155e2", "name": "ic_camera_set_af.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_af.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 916, "lastModifiedTime": 1749022453872}}, {"hashValue": "d3a93fe8d0efec060a6005c202d3107c", "name": "ic_camera_set_arrow.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_arrow.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1422, "lastModifiedTime": 1749022453872}}, {"hashValue": "a61b4feb69e03c3d958607666710a04e", "name": "ic_camera_set_checked.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_checked.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1400, "lastModifiedTime": 1749022453873}}, {"hashValue": "5ac22f0067e1d99287875612e013f929", "name": "ic_camera_set_class.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_class.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1090, "lastModifiedTime": 1749022453873}}, {"hashValue": "e80ef7c03ad20ec772bbce88000cd2d8", "name": "ic_camera_set_exposure.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_exposure.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2866, "lastModifiedTime": 1749022453873}}, {"hashValue": "ecc027f6a0e0c3eae8748fbfad1d5e89", "name": "ic_camera_set_focus.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_focus.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 519, "lastModifiedTime": 1749022453874}}, {"hashValue": "fbd4ea5b70c71cd78d16d314a4d2ec35", "name": "ic_camera_set_format.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_format.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4020, "lastModifiedTime": 1749022453874}}, {"hashValue": "6b1c2f6b0b3d2df44e34c54ae2f6009f", "name": "ic_camera_set_line.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_line.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1402, "lastModifiedTime": 1749022453875}}, {"hashValue": "8cc5166d9dda3639bbbf727e06e86379", "name": "ic_camera_set_location.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_location.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2508, "lastModifiedTime": 1749022453875}}, {"hashValue": "3517f4d041c08a7472d14e1bbd53fc34", "name": "ic_camera_set_pic_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_pic_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453876}}, {"hashValue": "530b5f9e2e6345b7f1f7e755565fc9ca", "name": "ic_camera_set_quality.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_quality.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13855, "lastModifiedTime": 1749022453876}}, {"hashValue": "1f7f4dda7b345ec5840d5c5ceb86b8cc", "name": "ic_camera_set_video_rate.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_video_rate.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1235, "lastModifiedTime": 1749022453876}}, {"hashValue": "9f57de8ee52701eb4f8d4bd2f4fa06fc", "name": "ic_camera_set_video_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_video_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1485, "lastModifiedTime": 1749022453878}}, {"hashValue": "bef04f114418568acf0698a4597eb05a", "name": "ic_camera_switch_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_switch_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1354, "lastModifiedTime": 1749022453878}}, {"hashValue": "2f057ea2d566c1e0599014c2c5811837", "name": "ic_camera_switch_off2.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_switch_off2.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 974, "lastModifiedTime": 1749022453879}}, {"hashValue": "2cc3922cf58c19c1889223ea018b80db", "name": "ic_camera_video_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 441, "lastModifiedTime": 1749022453879}}, {"hashValue": "f5b428f1a8e5eedbd02a34e3fa7cb2b8", "name": "ic_camera_video_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453879}}, {"hashValue": "58a400a0045d317e7adf29ae8c037190", "name": "ic_camera_video_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 913, "lastModifiedTime": 1749022453880}}, {"hashValue": "b3fad9c09dce8787b994d81631d29879", "name": "ic_public_brightness_filled.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_public_brightness_filled.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2685, "lastModifiedTime": 1749022453881}}, {"hashValue": "9693142322f4eafa66f416622e0f0280", "name": "ic_public_brightness.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_public_brightness.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2862, "lastModifiedTime": 1749022453880}}, {"hashValue": "d3b999e99f9c08f171ebe7ab09188297", "name": "icon_camera_setting_timer_on_balk.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer_on_balk.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453883}}, {"hashValue": "cb13e3152b25a046c7dd36edef7aa479", "name": "icon_camera_setting_timer_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453882}}, {"hashValue": "e9b0ad9dbf060377c7132d87af8c93b4", "name": "icon_camera_setting_timer.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3664, "lastModifiedTime": 1749022453882}}, {"hashValue": "f081cce0a4620543cff83f556e7aa7bd", "name": "icon_camera_setting.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3573, "lastModifiedTime": 1749022453882}}, {"hashValue": "d1044eb267800fc10fb896e22a11dd41", "name": "icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453881}}, {"hashValue": "be67575a938820711272da8fdfb4a840", "name": "pic_avatar_radio01.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_radio01.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11660, "lastModifiedTime": 1749022453883}}, {"hashValue": "58f3bcfc0169fd61ce206066c6f9794d", "name": "pic_avatar_radio02.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_radio02.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 12092, "lastModifiedTime": 1749022453884}}, {"hashValue": "a12dac4cf6f64a5967872296c6169422", "name": "pic_avatar_regular.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_regular.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14162, "lastModifiedTime": 1749022453884}}, {"hashValue": "e1277770a0627be49b7ee1ed38f0c410", "name": "pic_camera_line.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_camera_line.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 243206, "lastModifiedTime": 1749022453886}}, {"hashValue": "2f590cb280d1d441b49a19b1003b5bd6", "name": "pic_camera_mirror.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_camera_mirror.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 111725, "lastModifiedTime": 1749022453888}}, {"hashValue": "528da4db4b0c74b4ab1f781b58d0b90f", "name": "pic_popup_left.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_popup_left.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 486207, "lastModifiedTime": 1749022453890}}, {"hashValue": "55e8b4f62ca7ff5d41d4ebd02f96b6ec", "name": "pic_popup_right.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_popup_right.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 424653, "lastModifiedTime": 1749022453892}}, {"hashValue": "854908744dd8a55925c5fa3bfe59b7e4", "name": "switch_camera.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\switch_camera.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2350, "lastModifiedTime": 1749022453892}}]}, {"hashValue": "1752577f66bd1a3d28dca9eee8d5043f", "name": "profile", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5ddc969a7aad41d4c4c22edd772abc8a", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 42, "lastModifiedTime": 1749022453893}}]}]}]}, {"hashValue": "ad7debb02aaa0898fed9e95fd9a46682", "name": "resources.index", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources.index", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8917, "lastModifiedTime": 1749025841269}}, {"hashValue": "0d336987c1f12ab111cc5ac740620536", "name": "ResourceTable.txt", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3976, "lastModifiedTime": 1749025841266}}]}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h": {"hashValue": "6e6c4c2ef6d4669180ce6e417d5e1cd2", "name": "ResourceTable.h", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6552, "lastModifiedTime": 1749025841264}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\generated\\r\\default": {"hashValue": "9367db5b7b3d116a99ccbf74be6631fc", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\generated\\r\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6e6c4c2ef6d4669180ce6e417d5e1cd2", "name": "ResourceTable.h", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6552, "lastModifiedTime": 1749025841264}}]}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\loader\\default": {"hashValue": "a754b761bb2a4231bec32a28aa6485ad", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\loader\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "b839822c4454f591a3e53d2ed90adbef", "name": "loader.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 782, "lastModifiedTime": 1749025840778}}]}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt": {"hashValue": "0d336987c1f12ab111cc5ac740620536", "name": "ResourceTable.txt", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3976, "lastModifiedTime": 1749025841266}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\module.json": {"hashValue": "0e03c32f7872afdd6fe45ebb54a495e7", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3800, "lastModifiedTime": 1749025841268}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile": {"hashValue": "1752577f66bd1a3d28dca9eee8d5043f", "name": "profile", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5ddc969a7aad41d4c4c22edd772abc8a", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 42, "lastModifiedTime": 1749022453893}}]}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\ets": {"hashValue": "b90b56599d6ddc78a02c4ac40e4b916c", "name": "ets", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\ets", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "dbffa651787a2b844a4b6cf4b6cd4e49", "name": "modules.abc", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\modules.abc", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 502756, "lastModifiedTime": 1749025858175}}, {"hashValue": "4832dde6e0776b1bece41443f0136408", "name": "sourceMaps.map", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 85377, "lastModifiedTime": 1749025858058}}]}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\libs\\default": {"hashValue": "422a78e517bc1647ec25d3862a64039a", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\libs\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "2171e4f7bce81a969db11bc7afcf3d70", "name": "arm64-v8a", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\arm64-v8a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bcbb5e7ec8be835eebf55ade0e3a01cf", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\arm64-v8a\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1260824, "lastModifiedTime": 978278400000}}, {"hashValue": "dd78a8b040e1e7878c04e2e4af41cb59", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\arm64-v8a\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 215088, "lastModifiedTime": 1749025846118}}]}, {"hashValue": "7b45fb2f2fe54a224f50eed2bae177c6", "name": "armeabi-v7a", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\armeabi-v7a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "7a315e2f9a7b1223fa1be93f092b81cf", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\armeabi-v7a\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1109100, "lastModifiedTime": 978278400000}}, {"hashValue": "abacae0a5416f6f6d1c46a5e511c172b", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\armeabi-v7a\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 192616, "lastModifiedTime": 1749025846135}}]}, {"hashValue": "53935167cf1982d200745dfa95518a5c", "name": "x86_64", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\x86_64", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c28a4178127d9b2514c6555ece74fb4c", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\x86_64\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1293080, "lastModifiedTime": 978278400000}}, {"hashValue": "01d2fcbeda0d398c30f11f17518e9e80", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\x86_64\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 205256, "lastModifiedTime": 1749025847902}}]}]}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default": {"hashValue": "422a78e517bc1647ec25d3862a64039a", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "2171e4f7bce81a969db11bc7afcf3d70", "name": "arm64-v8a", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\arm64-v8a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bcbb5e7ec8be835eebf55ade0e3a01cf", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\arm64-v8a\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1260824, "lastModifiedTime": 978278400000}}, {"hashValue": "dd78a8b040e1e7878c04e2e4af41cb59", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\arm64-v8a\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 215088, "lastModifiedTime": 1749025846118}}]}, {"hashValue": "7b45fb2f2fe54a224f50eed2bae177c6", "name": "armeabi-v7a", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\armeabi-v7a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "7a315e2f9a7b1223fa1be93f092b81cf", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\armeabi-v7a\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1109100, "lastModifiedTime": 978278400000}}, {"hashValue": "abacae0a5416f6f6d1c46a5e511c172b", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\armeabi-v7a\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 192616, "lastModifiedTime": 1749025846135}}]}, {"hashValue": "53935167cf1982d200745dfa95518a5c", "name": "x86_64", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\x86_64", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c28a4178127d9b2514c6555ece74fb4c", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\x86_64\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1293080, "lastModifiedTime": 978278400000}}, {"hashValue": "01d2fcbeda0d398c30f11f17518e9e80", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\x86_64\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 205256, "lastModifiedTime": 1749025847902}}]}]}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\patch\\default\\base_native_libs.json": {"hashValue": "c5f53a00968ca6945489e5e92a1e8bc0", "name": "base_native_libs.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\patch\\default\\base_native_libs.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2737, "lastModifiedTime": 1749025848092}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources": {"hashValue": "7c74b7971302f5ae70c88097521871b6", "name": "resources", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6dcaabd12ffee1d3c9a6d6bc203af712", "name": "base", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "627e3a54a0c7d5cee1e431efa84443a3", "name": "media", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0b478312ebe80ab6e93b97a4ce5c0448", "name": "app_icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\app_icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453840}}, {"hashValue": "6f84b90d6a6f23540cb85f54a38f45c5", "name": "camera_pause_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_pause_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10632, "lastModifiedTime": 1749022453864}}, {"hashValue": "387593473c128ab070274fbcc65a7bd4", "name": "camera_switch_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_switch_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6926, "lastModifiedTime": 1749022453864}}, {"hashValue": "cd9e2c9fa9a4a73403e54b0c86ddfa5a", "name": "camera_take_photo_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_take_photo_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14016, "lastModifiedTime": 1749022453865}}, {"hashValue": "69e1c567cd44f6edc7ee1108440ac0e0", "name": "camera_take_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_take_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 15819, "lastModifiedTime": 1749022453865}}, {"hashValue": "7789281643ea3f6b4a227a7e12b4d39a", "name": "camera_thumbnail_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_thumbnail_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7152, "lastModifiedTime": 1749022453866}}, {"hashValue": "63b028c7353011ee8adfa63c3a967fb0", "name": "flash_always_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\flash_always_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1898, "lastModifiedTime": 1749022453866}}, {"hashValue": "90f460eaae837069a6b51717992eee7c", "name": "ic_camera_ic_camera_radio_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_ic_camera_radio_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 795, "lastModifiedTime": 1749022453867}}, {"hashValue": "93c1941a2022ca17176f3981b58d735b", "name": "ic_camera_pad.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_pad.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1073, "lastModifiedTime": 1749022453868}}, {"hashValue": "84ffec0a6189cfd2de43aaca147901d0", "name": "ic_camera_phone.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_phone.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1339, "lastModifiedTime": 1749022453868}}, {"hashValue": "3843a663a62b92f83897b2b627e5d8f0", "name": "ic_camera_progressBar_circle.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_progressBar_circle.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1091, "lastModifiedTime": 1749022453868}}, {"hashValue": "2b4559aff0a3e5a95b8ce3cf9c9f91c4", "name": "ic_camera_public_flash_auto_white.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_auto_white.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "8fe205be5fdbe2c82d370016417c2ff3", "name": "ic_camera_public_flash_auto.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_auto.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "c863a453524f35a4d3371b7f3bc11897", "name": "ic_camera_public_flash_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2091, "lastModifiedTime": 1749022453870}}, {"hashValue": "8c5cbaa8ba0a62653b6cc2b11400b670", "name": "ic_camera_public_flash_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1487, "lastModifiedTime": 1749022453870}}, {"hashValue": "7d2c5d39f46581e2607ec9c0a31e0543", "name": "ic_camera_radio_open.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_radio_open.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1762, "lastModifiedTime": 1749022453871}}, {"hashValue": "b3a59d24aa0a41c1bc430bbdb5e4d471", "name": "ic_camera_set__antishake.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set__antishake.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5218, "lastModifiedTime": 1749022453871}}, {"hashValue": "da4cb2c6f607f8ac528071d4c0a30d16", "name": "ic_camera_set__mirror.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set__mirror.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1627, "lastModifiedTime": 1749022453871}}, {"hashValue": "d59ac73d4ec951681a0203852b9155e2", "name": "ic_camera_set_af.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_af.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 916, "lastModifiedTime": 1749022453872}}, {"hashValue": "d3a93fe8d0efec060a6005c202d3107c", "name": "ic_camera_set_arrow.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_arrow.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1422, "lastModifiedTime": 1749022453872}}, {"hashValue": "a61b4feb69e03c3d958607666710a04e", "name": "ic_camera_set_checked.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_checked.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1400, "lastModifiedTime": 1749022453873}}, {"hashValue": "5ac22f0067e1d99287875612e013f929", "name": "ic_camera_set_class.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_class.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1090, "lastModifiedTime": 1749022453873}}, {"hashValue": "e80ef7c03ad20ec772bbce88000cd2d8", "name": "ic_camera_set_exposure.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_exposure.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2866, "lastModifiedTime": 1749022453873}}, {"hashValue": "ecc027f6a0e0c3eae8748fbfad1d5e89", "name": "ic_camera_set_focus.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_focus.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 519, "lastModifiedTime": 1749022453874}}, {"hashValue": "fbd4ea5b70c71cd78d16d314a4d2ec35", "name": "ic_camera_set_format.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_format.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4020, "lastModifiedTime": 1749022453874}}, {"hashValue": "6b1c2f6b0b3d2df44e34c54ae2f6009f", "name": "ic_camera_set_line.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_line.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1402, "lastModifiedTime": 1749022453875}}, {"hashValue": "8cc5166d9dda3639bbbf727e06e86379", "name": "ic_camera_set_location.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_location.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2508, "lastModifiedTime": 1749022453875}}, {"hashValue": "3517f4d041c08a7472d14e1bbd53fc34", "name": "ic_camera_set_pic_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_pic_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453876}}, {"hashValue": "530b5f9e2e6345b7f1f7e755565fc9ca", "name": "ic_camera_set_quality.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_quality.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13855, "lastModifiedTime": 1749022453876}}, {"hashValue": "1f7f4dda7b345ec5840d5c5ceb86b8cc", "name": "ic_camera_set_video_rate.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_video_rate.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1235, "lastModifiedTime": 1749022453876}}, {"hashValue": "9f57de8ee52701eb4f8d4bd2f4fa06fc", "name": "ic_camera_set_video_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_video_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1485, "lastModifiedTime": 1749022453878}}, {"hashValue": "bef04f114418568acf0698a4597eb05a", "name": "ic_camera_switch_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_switch_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1354, "lastModifiedTime": 1749022453878}}, {"hashValue": "2f057ea2d566c1e0599014c2c5811837", "name": "ic_camera_switch_off2.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_switch_off2.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 974, "lastModifiedTime": 1749022453879}}, {"hashValue": "2cc3922cf58c19c1889223ea018b80db", "name": "ic_camera_video_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 441, "lastModifiedTime": 1749022453879}}, {"hashValue": "f5b428f1a8e5eedbd02a34e3fa7cb2b8", "name": "ic_camera_video_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453879}}, {"hashValue": "58a400a0045d317e7adf29ae8c037190", "name": "ic_camera_video_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 913, "lastModifiedTime": 1749022453880}}, {"hashValue": "b3fad9c09dce8787b994d81631d29879", "name": "ic_public_brightness_filled.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_public_brightness_filled.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2685, "lastModifiedTime": 1749022453881}}, {"hashValue": "9693142322f4eafa66f416622e0f0280", "name": "ic_public_brightness.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_public_brightness.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2862, "lastModifiedTime": 1749022453880}}, {"hashValue": "d3b999e99f9c08f171ebe7ab09188297", "name": "icon_camera_setting_timer_on_balk.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer_on_balk.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453883}}, {"hashValue": "cb13e3152b25a046c7dd36edef7aa479", "name": "icon_camera_setting_timer_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453882}}, {"hashValue": "e9b0ad9dbf060377c7132d87af8c93b4", "name": "icon_camera_setting_timer.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3664, "lastModifiedTime": 1749022453882}}, {"hashValue": "f081cce0a4620543cff83f556e7aa7bd", "name": "icon_camera_setting.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3573, "lastModifiedTime": 1749022453882}}, {"hashValue": "d1044eb267800fc10fb896e22a11dd41", "name": "icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453881}}, {"hashValue": "be67575a938820711272da8fdfb4a840", "name": "pic_avatar_radio01.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_radio01.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11660, "lastModifiedTime": 1749022453883}}, {"hashValue": "58f3bcfc0169fd61ce206066c6f9794d", "name": "pic_avatar_radio02.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_radio02.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 12092, "lastModifiedTime": 1749022453884}}, {"hashValue": "a12dac4cf6f64a5967872296c6169422", "name": "pic_avatar_regular.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_regular.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14162, "lastModifiedTime": 1749022453884}}, {"hashValue": "e1277770a0627be49b7ee1ed38f0c410", "name": "pic_camera_line.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_camera_line.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 243206, "lastModifiedTime": 1749022453886}}, {"hashValue": "2f590cb280d1d441b49a19b1003b5bd6", "name": "pic_camera_mirror.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_camera_mirror.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 111725, "lastModifiedTime": 1749022453888}}, {"hashValue": "528da4db4b0c74b4ab1f781b58d0b90f", "name": "pic_popup_left.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_popup_left.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 486207, "lastModifiedTime": 1749022453890}}, {"hashValue": "55e8b4f62ca7ff5d41d4ebd02f96b6ec", "name": "pic_popup_right.png", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_popup_right.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 424653, "lastModifiedTime": 1749022453892}}, {"hashValue": "854908744dd8a55925c5fa3bfe59b7e4", "name": "switch_camera.svg", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\switch_camera.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2350, "lastModifiedTime": 1749022453892}}]}, {"hashValue": "1752577f66bd1a3d28dca9eee8d5043f", "name": "profile", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5ddc969a7aad41d4c4c22edd772abc8a", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 42, "lastModifiedTime": 1749022453893}}]}]}]}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources.index": {"hashValue": "ad7debb02aaa0898fed9e95fd9a46682", "name": "resources.index", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources.index", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8917, "lastModifiedTime": 1749025841269}}, "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap": {"hashValue": "368cbd7356d2c93a1802dca39c786b2e", "name": "entry-default-unsigned.hap", "path": "D:\\harmonyforwork\\aboutCamera\\history\\applications_app_samples\\code\\BasicFeature\\Media\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6337832, "lastModifiedTime": 1749025858888}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\AppScope\\app.json5": {"hashValue": "b272ae09c35f24ae3fab922faad24c82", "name": "app.json5", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\AppScope\\app.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 883, "lastModifiedTime": 1749022453838}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\module.json5": {"hashValue": "8a89638127253b3d548cd28f1b96f6c1", "name": "module.json5", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\module.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2563, "lastModifiedTime": 1749024082080}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\build-profile.json5": {"hashValue": "e8693f92c7bebe783cb0e9adbf3a5eca", "name": "build-profile.json5", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\build-profile.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1951, "lastModifiedTime": 1749097438783}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build-profile.json5": {"hashValue": "a6f543b6c877af84066d4050145db38e", "name": "build-profile.json5", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build-profile.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1015, "lastModifiedTime": 1749024082077}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\profile\\main_pages.json": {"hashValue": "5ddc969a7aad41d4c4c22edd772abc8a", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 42, "lastModifiedTime": 1749022453893}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\oh-package.json5": {"hashValue": "dfebcc02ee1adc714cb3f7a31f3f6448", "name": "oh-package.json5", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\oh-package.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 248, "lastModifiedTime": 1749022453845}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets": {"hashValue": "9abe1f2dd5998dac8f16afca5c1784bb", "name": "BuildProfile.ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 371, "lastModifiedTime": 1749088488714}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json": {"hashValue": "64690e2c1b17d7eadb9a865c6e85cd04", "name": "loader.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 588, "lastModifiedTime": 1749088488754}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json": {"hashValue": "d3b5238702f6297fda22b46f7b382117", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1992, "lastModifiedTime": 1749088488770}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json": {"hashValue": "c06ecdb2f330b6230926bf871b22ba66", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2078, "lastModifiedTime": 1749088489016}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources": {"hashValue": "31cbb6f14ce0b66d6da5b0c3c2b0f1f8", "name": "resources", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "4f9d140339e81b34f5e3d07a5a5994f5", "name": "base", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "8375a0c8f7e0cd7ea2c936983c993ade", "name": "element", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "045277200a69951ccd3352446594fc69", "name": "color.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\element\\color.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 241, "lastModifiedTime": 1749022453863}}, {"hashValue": "0e847e049e12144d3f7ffc34796d1fbc", "name": "string.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\element\\string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 272, "lastModifiedTime": 1749022453863}}]}, {"hashValue": "5ded41793140ea643198cebd4ba59fb8", "name": "media", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6f84b90d6a6f23540cb85f54a38f45c5", "name": "camera_pause_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\camera_pause_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10632, "lastModifiedTime": 1749022453864}}, {"hashValue": "387593473c128ab070274fbcc65a7bd4", "name": "camera_switch_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\camera_switch_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6926, "lastModifiedTime": 1749022453864}}, {"hashValue": "cd9e2c9fa9a4a73403e54b0c86ddfa5a", "name": "camera_take_photo_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\camera_take_photo_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14016, "lastModifiedTime": 1749022453865}}, {"hashValue": "69e1c567cd44f6edc7ee1108440ac0e0", "name": "camera_take_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\camera_take_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 15819, "lastModifiedTime": 1749022453865}}, {"hashValue": "7789281643ea3f6b4a227a7e12b4d39a", "name": "camera_thumbnail_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\camera_thumbnail_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7152, "lastModifiedTime": 1749022453866}}, {"hashValue": "63b028c7353011ee8adfa63c3a967fb0", "name": "flash_always_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\flash_always_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1898, "lastModifiedTime": 1749022453866}}, {"hashValue": "90f460eaae837069a6b51717992eee7c", "name": "ic_camera_ic_camera_radio_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_ic_camera_radio_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 795, "lastModifiedTime": 1749022453867}}, {"hashValue": "93c1941a2022ca17176f3981b58d735b", "name": "ic_camera_pad.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_pad.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1073, "lastModifiedTime": 1749022453868}}, {"hashValue": "84ffec0a6189cfd2de43aaca147901d0", "name": "ic_camera_phone.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_phone.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1339, "lastModifiedTime": 1749022453868}}, {"hashValue": "3843a663a62b92f83897b2b627e5d8f0", "name": "ic_camera_progressBar_circle.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_progressBar_circle.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1091, "lastModifiedTime": 1749022453868}}, {"hashValue": "2b4559aff0a3e5a95b8ce3cf9c9f91c4", "name": "ic_camera_public_flash_auto_white.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_public_flash_auto_white.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "8fe205be5fdbe2c82d370016417c2ff3", "name": "ic_camera_public_flash_auto.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_public_flash_auto.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "c863a453524f35a4d3371b7f3bc11897", "name": "ic_camera_public_flash_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_public_flash_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2091, "lastModifiedTime": 1749022453870}}, {"hashValue": "8c5cbaa8ba0a62653b6cc2b11400b670", "name": "ic_camera_public_flash_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_public_flash_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1487, "lastModifiedTime": 1749022453870}}, {"hashValue": "7d2c5d39f46581e2607ec9c0a31e0543", "name": "ic_camera_radio_open.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_radio_open.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1762, "lastModifiedTime": 1749022453871}}, {"hashValue": "b3a59d24aa0a41c1bc430bbdb5e4d471", "name": "ic_camera_set__antishake.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set__antishake.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5218, "lastModifiedTime": 1749022453871}}, {"hashValue": "da4cb2c6f607f8ac528071d4c0a30d16", "name": "ic_camera_set__mirror.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set__mirror.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1627, "lastModifiedTime": 1749022453871}}, {"hashValue": "d59ac73d4ec951681a0203852b9155e2", "name": "ic_camera_set_af.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_af.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 916, "lastModifiedTime": 1749022453872}}, {"hashValue": "d3a93fe8d0efec060a6005c202d3107c", "name": "ic_camera_set_arrow.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_arrow.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1422, "lastModifiedTime": 1749022453872}}, {"hashValue": "a61b4feb69e03c3d958607666710a04e", "name": "ic_camera_set_checked.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_checked.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1400, "lastModifiedTime": 1749022453873}}, {"hashValue": "5ac22f0067e1d99287875612e013f929", "name": "ic_camera_set_class.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_class.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1090, "lastModifiedTime": 1749022453873}}, {"hashValue": "e80ef7c03ad20ec772bbce88000cd2d8", "name": "ic_camera_set_exposure.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_exposure.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2866, "lastModifiedTime": 1749022453873}}, {"hashValue": "ecc027f6a0e0c3eae8748fbfad1d5e89", "name": "ic_camera_set_focus.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_focus.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 519, "lastModifiedTime": 1749022453874}}, {"hashValue": "fbd4ea5b70c71cd78d16d314a4d2ec35", "name": "ic_camera_set_format.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_format.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4020, "lastModifiedTime": 1749022453874}}, {"hashValue": "6b1c2f6b0b3d2df44e34c54ae2f6009f", "name": "ic_camera_set_line.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_line.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1402, "lastModifiedTime": 1749022453875}}, {"hashValue": "8cc5166d9dda3639bbbf727e06e86379", "name": "ic_camera_set_location.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_location.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2508, "lastModifiedTime": 1749022453875}}, {"hashValue": "3517f4d041c08a7472d14e1bbd53fc34", "name": "ic_camera_set_pic_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_pic_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453876}}, {"hashValue": "530b5f9e2e6345b7f1f7e755565fc9ca", "name": "ic_camera_set_quality.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_quality.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13855, "lastModifiedTime": 1749022453876}}, {"hashValue": "1f7f4dda7b345ec5840d5c5ceb86b8cc", "name": "ic_camera_set_video_rate.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_video_rate.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1235, "lastModifiedTime": 1749022453876}}, {"hashValue": "9f57de8ee52701eb4f8d4bd2f4fa06fc", "name": "ic_camera_set_video_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_video_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1485, "lastModifiedTime": 1749022453878}}, {"hashValue": "bef04f114418568acf0698a4597eb05a", "name": "ic_camera_switch_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_switch_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1354, "lastModifiedTime": 1749022453878}}, {"hashValue": "2f057ea2d566c1e0599014c2c5811837", "name": "ic_camera_switch_off2.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_switch_off2.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 974, "lastModifiedTime": 1749022453879}}, {"hashValue": "2cc3922cf58c19c1889223ea018b80db", "name": "ic_camera_video_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_video_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 441, "lastModifiedTime": 1749022453879}}, {"hashValue": "f5b428f1a8e5eedbd02a34e3fa7cb2b8", "name": "ic_camera_video_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_video_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453879}}, {"hashValue": "58a400a0045d317e7adf29ae8c037190", "name": "ic_camera_video_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_video_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 913, "lastModifiedTime": 1749022453880}}, {"hashValue": "b3fad9c09dce8787b994d81631d29879", "name": "ic_public_brightness_filled.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_public_brightness_filled.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2685, "lastModifiedTime": 1749022453881}}, {"hashValue": "9693142322f4eafa66f416622e0f0280", "name": "ic_public_brightness.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_public_brightness.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2862, "lastModifiedTime": 1749022453880}}, {"hashValue": "d3b999e99f9c08f171ebe7ab09188297", "name": "icon_camera_setting_timer_on_balk.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\icon_camera_setting_timer_on_balk.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453883}}, {"hashValue": "cb13e3152b25a046c7dd36edef7aa479", "name": "icon_camera_setting_timer_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\icon_camera_setting_timer_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453882}}, {"hashValue": "e9b0ad9dbf060377c7132d87af8c93b4", "name": "icon_camera_setting_timer.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\icon_camera_setting_timer.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3664, "lastModifiedTime": 1749022453882}}, {"hashValue": "f081cce0a4620543cff83f556e7aa7bd", "name": "icon_camera_setting.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\icon_camera_setting.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3573, "lastModifiedTime": 1749022453882}}, {"hashValue": "d1044eb267800fc10fb896e22a11dd41", "name": "icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453881}}, {"hashValue": "be67575a938820711272da8fdfb4a840", "name": "pic_avatar_radio01.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_avatar_radio01.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11660, "lastModifiedTime": 1749022453883}}, {"hashValue": "58f3bcfc0169fd61ce206066c6f9794d", "name": "pic_avatar_radio02.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_avatar_radio02.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 12092, "lastModifiedTime": 1749022453884}}, {"hashValue": "a12dac4cf6f64a5967872296c6169422", "name": "pic_avatar_regular.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_avatar_regular.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14162, "lastModifiedTime": 1749022453884}}, {"hashValue": "e1277770a0627be49b7ee1ed38f0c410", "name": "pic_camera_line.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_camera_line.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 243206, "lastModifiedTime": 1749022453886}}, {"hashValue": "2f590cb280d1d441b49a19b1003b5bd6", "name": "pic_camera_mirror.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_camera_mirror.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 111725, "lastModifiedTime": 1749022453888}}, {"hashValue": "528da4db4b0c74b4ab1f781b58d0b90f", "name": "pic_popup_left.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_popup_left.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 486207, "lastModifiedTime": 1749022453890}}, {"hashValue": "55e8b4f62ca7ff5d41d4ebd02f96b6ec", "name": "pic_popup_right.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_popup_right.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 424653, "lastModifiedTime": 1749022453892}}, {"hashValue": "854908744dd8a55925c5fa3bfe59b7e4", "name": "switch_camera.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\media\\switch_camera.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2350, "lastModifiedTime": 1749022453892}}]}, {"hashValue": "1752577f66bd1a3d28dca9eee8d5043f", "name": "profile", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5ddc969a7aad41d4c4c22edd772abc8a", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 42, "lastModifiedTime": 1749022453893}}]}]}, {"hashValue": "fb73190687b4b3400e7a2b50b517f5c7", "name": "en_US", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\en_US", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "a4bac92b2c6e07a678f2adcdbb4c8489", "name": "element", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\en_US\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "f55483b907c93ff2167cd50c4eab5a30", "name": "string.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\en_US\\element\\string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 272, "lastModifiedTime": 1749022453894}}]}]}, {"hashValue": "d678701bc92e3c634d339130a904bca2", "name": "zh_CN", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\zh_CN", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "eafc7f9552b4fd1a785b2c4d4a9361ba", "name": "element", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\zh_CN\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c631cc9d37ee8466504a4d24c382d0e2", "name": "string.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources\\zh_CN\\element\\string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 266, "lastModifiedTime": 1749022453894}}]}]}]}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default": {"hashValue": "fcd7eda9c7575d23c809c1a6b1212a94", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "e50d947cc9f517e48167f19a2cc08c6d", "name": "app_compiled", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "9a4199b6ef27884777e18efbf2c1cc80", "name": "17911829729377985978", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled\\17911829729377985978", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 220, "lastModifiedTime": 1749088489237}}, {"hashValue": "b76f73302c3db15687922ee9b657385d", "name": "18264244028622245898", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled\\18264244028622245898", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7814, "lastModifiedTime": 1749088489236}}]}, {"hashValue": "e5e29557b582e14d120987f27cf8d03b", "name": "ids_map", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "4f3d671a2e47ae13a1d96fbd11c264e1", "name": "id_defined.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map\\id_defined.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13907, "lastModifiedTime": 1749088489410}}]}, {"hashValue": "daf3d4de1962aa9ad7e4ddc5a81f54f0", "name": "module_compiled", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "fcb799d0e6014de0e4675a413393ffce", "name": "10607473704131119740", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\10607473704131119740", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 262, "lastModifiedTime": 1749088489305}}, {"hashValue": "d0d17918f35afdcbdc9744665bdd7cef", "name": "10847366063526044615", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\10847366063526044615", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 220, "lastModifiedTime": 1749088489293}}, {"hashValue": "812414b766436d5a76283ded9a4fa91b", "name": "11116069213194664590", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\11116069213194664590", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 519, "lastModifiedTime": 1749088489308}}, {"hashValue": "4cf251866802ce7f9c9d60c9c64ff2ab", "name": "1117576168881487398", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\1117576168881487398", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 292, "lastModifiedTime": 1749088489300}}, {"hashValue": "5d05b3818dd0e708de4bbf9cae01eaeb", "name": "11420867861436245217", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\11420867861436245217", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 292, "lastModifiedTime": 1749088489294}}, {"hashValue": "916009364c75fcf30271bca3129e8098", "name": "11514941744043335159", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\11514941744043335159", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 262, "lastModifiedTime": 1749088489305}}, {"hashValue": "9ceb3e0d2a952e7d5650bc7e8d064756", "name": "11543230165515203881", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\11543230165515203881", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 265, "lastModifiedTime": 1749088489298}}, {"hashValue": "858f3a01aba539e4ea4d2f8994804a19", "name": "12335639055871009617", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\12335639055871009617", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 250, "lastModifiedTime": 1749088489306}}, {"hashValue": "3150e771241db66931733dc60354dac0", "name": "12343167957530014491", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\12343167957530014491", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 271, "lastModifiedTime": 1749088489302}}, {"hashValue": "437448bbba501a99015e33d8cf3f7d16", "name": "12377623765744680568", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\12377623765744680568", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 286, "lastModifiedTime": 1749088489296}}, {"hashValue": "7d09423af964979320788c654a647edc", "name": "12387742468790905073", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\12387742468790905073", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 256, "lastModifiedTime": 1749088489297}}, {"hashValue": "9a33a149c0683ed0af897839b9d20c52", "name": "12517070942789025716", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\12517070942789025716", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 253, "lastModifiedTime": 1749088489306}}, {"hashValue": "b55912794c7a3ed345f32eb105277e0d", "name": "1265445206858932905", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\1265445206858932905", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 271, "lastModifiedTime": 1749088489290}}, {"hashValue": "12cbc08f8f8db1a137421f76de1325fe", "name": "13137751813665165559", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\13137751813665165559", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 245, "lastModifiedTime": 1749088489307}}, {"hashValue": "083c50e3fa202ae62e40889cf1e8998e", "name": "1350032591664329264", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\1350032591664329264", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 265, "lastModifiedTime": 1749088489292}}, {"hashValue": "1a84a2e2946591567f5ff47a883b43de", "name": "13552529535057162621", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\13552529535057162621", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 280, "lastModifiedTime": 1749088489302}}, {"hashValue": "a4e64a36c98cedca38f2741bf803465d", "name": "1408355860087596776", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\1408355860087596776", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 283, "lastModifiedTime": 1749088489296}}, {"hashValue": "6095685c9ce29fd65cb65f7c70993cff", "name": "14150571178363231727", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\14150571178363231727", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 268, "lastModifiedTime": 1749088489297}}, {"hashValue": "0c3f867c2dea6d6db3daa484a94330f8", "name": "14271371400160152137", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\14271371400160152137", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 253, "lastModifiedTime": 1749088489295}}, {"hashValue": "dbcaa792d4bfe88ff57614df408639df", "name": "14686400811584036211", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\14686400811584036211", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 268, "lastModifiedTime": 1749088489299}}, {"hashValue": "e0545a8987079d1eb672edea592b8fb0", "name": "14916350860609271267", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\14916350860609271267", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 307, "lastModifiedTime": 1749088489296}}, {"hashValue": "083df729e5d4c7c6da57c2718e67f5df", "name": "15167255332973254967", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\15167255332973254967", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 274, "lastModifiedTime": 1749088489300}}, {"hashValue": "5972aacfa0a71b6ab28a378ae19e4eee", "name": "162296582220703885", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\162296582220703885", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 262, "lastModifiedTime": 1749088489299}}, {"hashValue": "8a483a549879d2615195c732ef409d8f", "name": "16416138664000640872", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\16416138664000640872", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 513, "lastModifiedTime": 1749088489309}}, {"hashValue": "448bb0ac0171f5f5e173a1d397fb1da7", "name": "16448994640272929567", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\16448994640272929567", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 280, "lastModifiedTime": 1749088489300}}, {"hashValue": "327fd639b7c85672d8668938552d1757", "name": "16874963683956006731", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\16874963683956006731", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 271, "lastModifiedTime": 1749088489302}}, {"hashValue": "d8f32dc0052f6a372dc128593f2fe42f", "name": "16991375130624896612", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\16991375130624896612", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 265, "lastModifiedTime": 1749088489303}}, {"hashValue": "1331077d7b14c32c86b11721eeeedd88", "name": "17492342682089113388", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\17492342682089113388", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 265, "lastModifiedTime": 1749088489299}}, {"hashValue": "0d53c22f0af524720e040e49c0027c73", "name": "1884814598312674785", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\1884814598312674785", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 307, "lastModifiedTime": 1749088489294}}, {"hashValue": "bd04ec41d7e196491de4fe768f6e537d", "name": "192749712032863331", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\192749712032863331", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 265, "lastModifiedTime": 1749088489298}}, {"hashValue": "2aecdae88be9249415d02bf37eeb92f1", "name": "339953919362331862", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\339953919362331862", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 256, "lastModifiedTime": 1749088489290}}, {"hashValue": "219ec3d81cd3c1690c4c6a2f0a900aa7", "name": "4220207968849177922", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\4220207968849177922", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 298, "lastModifiedTime": 1749088489301}}, {"hashValue": "c5500b39fc7dd49f3fa6ecda20bba686", "name": "4461785568714359146", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\4461785568714359146", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 465, "lastModifiedTime": 1749088489286}}, {"hashValue": "7e42e101c75cb4af9bd16ee8d3197559", "name": "495286905885102411", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\495286905885102411", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 268, "lastModifiedTime": 1749088489304}}, {"hashValue": "1dbb1c7ecddd22100e34fd2d0d0e75bc", "name": "4992431950657936271", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\4992431950657936271", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 301, "lastModifiedTime": 1749088489294}}, {"hashValue": "d5c71175619a189fb66065b450b14850", "name": "5110507445178191608", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\5110507445178191608", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 253, "lastModifiedTime": 1749088489292}}, {"hashValue": "0198ba0ac4335d4bb3eea9cce855a3d3", "name": "5117113076307392037", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\5117113076307392037", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 283, "lastModifiedTime": 1749088489294}}, {"hashValue": "db56780aafb23e799c4af53add71c5b2", "name": "5309321809784510124", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\5309321809784510124", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 247, "lastModifiedTime": 1749088489307}}, {"hashValue": "e664e2ad2ad2d53b89033fc123fdc283", "name": "560005166105608852", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\560005166105608852", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 271, "lastModifiedTime": 1749088489303}}, {"hashValue": "5a2b1b6d4bc1c6cb769169a0e0fb06dd", "name": "5819191708869886524", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\5819191708869886524", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 262, "lastModifiedTime": 1749088489305}}, {"hashValue": "8f8acee2cae4e8def0a8e08af9f3feda", "name": "6164574425083792997", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\6164574425083792997", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 289, "lastModifiedTime": 1749088489296}}, {"hashValue": "2f66834bc16177fd9adfbbd08fb02f15", "name": "6446349196253748033", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\6446349196253748033", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 271, "lastModifiedTime": 1749088489298}}, {"hashValue": "a655a2c588247b9029c2f27b1c82e6e9", "name": "6523700001934434705", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\6523700001934434705", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 247, "lastModifiedTime": 1749088489295}}, {"hashValue": "bf6882bc842d9793a93bd7d66fbe00dd", "name": "655573587407008197", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\655573587407008197", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 262, "lastModifiedTime": 1749088489304}}, {"hashValue": "054fe79e1b46576d7d77eb94aaa38840", "name": "6960873292405506446", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\6960873292405506446", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 271, "lastModifiedTime": 1749088489300}}, {"hashValue": "50f2bb731c69c434c510d28eb7e1356f", "name": "7233653457547122671", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\7233653457547122671", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 265, "lastModifiedTime": 1749088489294}}, {"hashValue": "d2cc91ac5bed346c00221151903d0954", "name": "7349471202307487686", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\7349471202307487686", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 292, "lastModifiedTime": 1749088489295}}, {"hashValue": "c1750f29c55ca4b0b36ce8a2409d452c", "name": "7523119568303560987", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\7523119568303560987", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 268, "lastModifiedTime": 1749088489291}}, {"hashValue": "8f473a8f6a26273a8b12b9a3611c79d6", "name": "7901022163002210457", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\7901022163002210457", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 274, "lastModifiedTime": 1749088489299}}, {"hashValue": "49e75d29bab963eb2cddf7ac5fd1b16b", "name": "8001598352264873813", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\8001598352264873813", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 268, "lastModifiedTime": 1749088489292}}, {"hashValue": "4fc6c769306dae41a0490e57b788143f", "name": "8032062545955106742", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\8032062545955106742", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 259, "lastModifiedTime": 1749088489305}}, {"hashValue": "0579eb6e29cfd9fe70049518d8cab7f9", "name": "8058854412007188534", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\8058854412007188534", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 253, "lastModifiedTime": 1749088489305}}, {"hashValue": "bc0b7a1cad7423ff2770f99b1faf4813", "name": "9443466194965258145", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\9443466194965258145", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 432, "lastModifiedTime": 1749088489285}}, {"hashValue": "fc3a40925acba9e6b227f5dd25aa8220", "name": "9544550848285298368", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\9544550848285298368", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 268, "lastModifiedTime": 1749088489302}}, {"hashValue": "792c3ce58c3f825560e27421ce9f7d0c", "name": "9762505192538864533", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled\\9762505192538864533", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 289, "lastModifiedTime": 1749088489304}}]}, {"hashValue": "1e2da8770f6341edc3e77fab2ca62655", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3800, "lastModifiedTime": 1749088489410}}, {"hashValue": "23272b8ca8352afa19fb86524a818ad3", "name": "resources", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "be74aa302d8a4791d3e5aeeae220286a", "name": "base", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "4982958e641819be87a264e525a6e604", "name": "profile", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "e3b8f3c482b1fc54071fa2ed0d229d6b", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 24, "lastModifiedTime": 1749088489460}}]}]}]}, {"hashValue": "1d658d479d7436579d6041f4e10e13c8", "name": "resources.index", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources.index", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11875, "lastModifiedTime": 1749088489403}}, {"hashValue": "7da17948d3901ed9b5b8c8b6368b42e8", "name": "ResourceTable.txt", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3976, "lastModifiedTime": 1749088489412}}]}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\generated\\r\\default": {"hashValue": "6838303a38187532ed235ac13335945d", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\generated\\r\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "af43162dcc8b396626b9d76067b56cdb", "name": "ResourceTable.h", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6552, "lastModifiedTime": 1749088489411}}]}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\config\\buildConfig.json": {"hashValue": "fb0836601e0e0cba6a282404152bcc1c", "name": "buildConfig.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\config\\buildConfig.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1953, "lastModifiedTime": 1749088487365}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module.json": {"hashValue": "1e2da8770f6341edc3e77fab2ca62655", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3800, "lastModifiedTime": 1749088489410}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json": {"hashValue": "e3b8f3c482b1fc54071fa2ed0d229d6b", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 24, "lastModifiedTime": 1749088489460}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\loader\\default": {"hashValue": "9363a83a27fea780cb88b313bbaac6ac", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\loader\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "64690e2c1b17d7eadb9a865c6e85cd04", "name": "loader.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 588, "lastModifiedTime": 1749088488754}}]}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt": {"hashValue": "7da17948d3901ed9b5b8c8b6368b42e8", "name": "ResourceTable.txt", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3976, "lastModifiedTime": 1749088489412}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile": {"hashValue": "4982958e641819be87a264e525a6e604", "name": "profile", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "e3b8f3c482b1fc54071fa2ed0d229d6b", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 24, "lastModifiedTime": 1749088489460}}]}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets": {"hashValue": "e975d13e36b82c96027e38615371a226", "name": "ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "210e0c050b75f1a5b617344717096c88", "name": "common", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\common", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "2fa07f50c1c40e9f2da16e0be769ad39", "name": "Constants.ts", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\common\\Constants.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2352, "lastModifiedTime": 1749022453851}}, {"hashValue": "726cfaeed739814f125de17b312a4dec", "name": "DisplayCalculator.ts", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\common\\DisplayCalculator.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1797, "lastModifiedTime": 1749022453851}}, {"hashValue": "ebfbe2c26e338083ae725beafd6ca345", "name": "SettingItem.ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\common\\SettingItem.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4340, "lastModifiedTime": 1749022453852}}, {"hashValue": "614e88984a4b6d11c8fbb1ada155501d", "name": "SettingPublicLayout.ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\common\\SettingPublicLayout.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3575, "lastModifiedTime": 1749022453852}}, {"hashValue": "f67bd173635ebfaec6c113f9f803cde0", "name": "SettingRightLayout.ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\common\\SettingRightLayout.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6234, "lastModifiedTime": 1749022453853}}]}, {"hashValue": "d823d5d0172d34fa1662c9331c64f1b9", "name": "Dialog", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\Dialog", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "a31963b78e2f77c76bb4443d0cff6084", "name": "MainDialog.ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\Dialog\\MainDialog.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2977, "lastModifiedTime": 1749022453850}}, {"hashValue": "ad0ad06a44cd971cf2765aa35030ad1c", "name": "SettingDialog.ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\Dialog\\SettingDialog.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7728, "lastModifiedTime": 1749022453850}}]}, {"hashValue": "d24c9916b230ea26067a94699aa21881", "name": "entryability", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\entryability", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "a30db74181c55d3f533ec7fd54252883", "name": "EntryAbility.ts", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\entryability\\EntryAbility.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2368, "lastModifiedTime": 1749022453853}}]}, {"hashValue": "a07cbc9dc2af3dc47dfbc9a697878474", "name": "model", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\model", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ddf863ab55b13ec8adaed4f1847b3f00", "name": "DateTimeUtil.ts", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\model\\DateTimeUtil.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2062, "lastModifiedTime": 1749022453854}}, {"hashValue": "7b99e20512aba2ff6fd9ee45f98d0e0e", "name": "Logger.ts", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\model\\Logger.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1312, "lastModifiedTime": 1749022453854}}, {"hashValue": "af6f5a81029b1c079b6616dbd7dca9cd", "name": "MediaUtils.ts", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\model\\MediaUtils.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3458, "lastModifiedTime": 1749022453855}}]}, {"hashValue": "861d41a7b95078e8ddf1b0086c609709", "name": "pages", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\pages", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "7e9006753b4da2ad164eacb21b5c4f12", "name": "Index.ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\pages\\Index.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7948, "lastModifiedTime": 1749022453855}}]}, {"hashValue": "98bbdcbf4bb7ea8ec16ce098ca5504d2", "name": "views", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\views", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5c2ab13cd5e50160be16ca7fa35df0e3", "name": "CountdownPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\views\\CountdownPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3166, "lastModifiedTime": 1749022453856}}, {"hashValue": "3892e004ba5eb99c243ef912ba5fe010", "name": "DividerPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\views\\DividerPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1150, "lastModifiedTime": 1749022453856}}, {"hashValue": "72a84af30d3bc4a5cd0d8ca43e89c7a6", "name": "FlashingLightPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\views\\FlashingLightPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3914, "lastModifiedTime": 1749022453857}}, {"hashValue": "0562df559ccfdde6befb95b0d1914d0d", "name": "FocusAreaPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\views\\FocusAreaPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3362, "lastModifiedTime": 1749022453857}}, {"hashValue": "553ac0d07c4c6127f9f79c5a8d9d75ac", "name": "FocusPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\views\\FocusPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4414, "lastModifiedTime": 1749022453858}}, {"hashValue": "7c65c6b53549f1b3c9e5ebb1b43ede8e", "name": "ModeSwitchPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\views\\ModeSwitchPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 16947, "lastModifiedTime": 1749024082079}}, {"hashValue": "a885876ccb165b0c0b76514cf6b93e29", "name": "SlidePage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets\\views\\SlidePage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2740, "lastModifiedTime": 1749022453861}}]}]}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json": {"hashValue": "f75731f6a767ecb0e83a8ee7ba8fd0d6", "name": "output_metadata.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 92, "lastModifiedTime": 1749097572216}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets": {"hashValue": "168618d50c26fcc68462030e94b4178b", "name": "BuildProfile.ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 371, "lastModifiedTime": 1749097572228}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\loader\\default\\loader.json": {"hashValue": "1f5a7c382e3e66af30e5252386679553", "name": "loader.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 582, "lastModifiedTime": 1749090143858}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json": {"hashValue": "54f075e53a5940883d0b77e1e24ff454", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1992, "lastModifiedTime": 1749097572261}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\outputs\\default\\pack.info": {"hashValue": "d9a8b42c3b9dc3930ea54ac4a012b3c4", "name": "pack.info", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\outputs\\default\\pack.info", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 556, "lastModifiedTime": 1749097572299}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json": {"hashValue": "fd0fd2c5a4e1ae86bcf741e0a27b76d7", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2078, "lastModifiedTime": 1749097572593}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json": {"hashValue": "e62e1cbcd7da0809c166ad02da4079c4", "name": "resConfig.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 955, "lastModifiedTime": 1749032265419}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\AppScope\\resources": {"hashValue": "7bc75c4a9d7c24ed0ab0acdf64a9bd55", "name": "resources", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\AppScope\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "563d683e2ba4327fdb3be313d2b08fd9", "name": "base", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\AppScope\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c5e816eee4bff7277f64b88b80c46bd4", "name": "element", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\AppScope\\resources\\base\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "69dcc072c81241b9f0f10acda170c627", "name": "string.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\AppScope\\resources\\base\\element\\string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4405, "lastModifiedTime": 1749022453839}}]}, {"hashValue": "e21a9b0a678508e904265d0756c5a3b0", "name": "media", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\AppScope\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0b478312ebe80ab6e93b97a4ce5c0448", "name": "app_icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\AppScope\\resources\\base\\media\\app_icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453840}}]}]}]}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default": {"hashValue": "40e6983f7fda34bd0e1ceeadb0b72e2a", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ad7242cb0f020e23136fb9fdd54029d3", "name": "ids_map", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ids_map", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "30f5a1fb673f2bbc1a8fe47c3745dbed", "name": "id_defined.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ids_map\\id_defined.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13907, "lastModifiedTime": 1749097572982}}]}, {"hashValue": "15187ca0a3503bf697de701ea314557b", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3800, "lastModifiedTime": 1749097572978}}, {"hashValue": "e62e1cbcd7da0809c166ad02da4079c4", "name": "resConfig.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 955, "lastModifiedTime": 1749032265419}}, {"hashValue": "7c74b7971302f5ae70c88097521871b6", "name": "resources", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6dcaabd12ffee1d3c9a6d6bc203af712", "name": "base", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "627e3a54a0c7d5cee1e431efa84443a3", "name": "media", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0b478312ebe80ab6e93b97a4ce5c0448", "name": "app_icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\app_icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453840}}, {"hashValue": "6f84b90d6a6f23540cb85f54a38f45c5", "name": "camera_pause_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_pause_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10632, "lastModifiedTime": 1749022453864}}, {"hashValue": "387593473c128ab070274fbcc65a7bd4", "name": "camera_switch_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_switch_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6926, "lastModifiedTime": 1749022453864}}, {"hashValue": "cd9e2c9fa9a4a73403e54b0c86ddfa5a", "name": "camera_take_photo_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_take_photo_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14016, "lastModifiedTime": 1749022453865}}, {"hashValue": "69e1c567cd44f6edc7ee1108440ac0e0", "name": "camera_take_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_take_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 15819, "lastModifiedTime": 1749022453865}}, {"hashValue": "7789281643ea3f6b4a227a7e12b4d39a", "name": "camera_thumbnail_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_thumbnail_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7152, "lastModifiedTime": 1749022453866}}, {"hashValue": "63b028c7353011ee8adfa63c3a967fb0", "name": "flash_always_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\flash_always_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1898, "lastModifiedTime": 1749022453866}}, {"hashValue": "90f460eaae837069a6b51717992eee7c", "name": "ic_camera_ic_camera_radio_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_ic_camera_radio_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 795, "lastModifiedTime": 1749022453867}}, {"hashValue": "93c1941a2022ca17176f3981b58d735b", "name": "ic_camera_pad.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_pad.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1073, "lastModifiedTime": 1749022453868}}, {"hashValue": "84ffec0a6189cfd2de43aaca147901d0", "name": "ic_camera_phone.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_phone.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1339, "lastModifiedTime": 1749022453868}}, {"hashValue": "3843a663a62b92f83897b2b627e5d8f0", "name": "ic_camera_progressBar_circle.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_progressBar_circle.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1091, "lastModifiedTime": 1749022453868}}, {"hashValue": "2b4559aff0a3e5a95b8ce3cf9c9f91c4", "name": "ic_camera_public_flash_auto_white.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_auto_white.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "8fe205be5fdbe2c82d370016417c2ff3", "name": "ic_camera_public_flash_auto.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_auto.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "c863a453524f35a4d3371b7f3bc11897", "name": "ic_camera_public_flash_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2091, "lastModifiedTime": 1749022453870}}, {"hashValue": "8c5cbaa8ba0a62653b6cc2b11400b670", "name": "ic_camera_public_flash_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1487, "lastModifiedTime": 1749022453870}}, {"hashValue": "7d2c5d39f46581e2607ec9c0a31e0543", "name": "ic_camera_radio_open.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_radio_open.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1762, "lastModifiedTime": 1749022453871}}, {"hashValue": "b3a59d24aa0a41c1bc430bbdb5e4d471", "name": "ic_camera_set__antishake.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set__antishake.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5218, "lastModifiedTime": 1749022453871}}, {"hashValue": "da4cb2c6f607f8ac528071d4c0a30d16", "name": "ic_camera_set__mirror.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set__mirror.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1627, "lastModifiedTime": 1749022453871}}, {"hashValue": "d59ac73d4ec951681a0203852b9155e2", "name": "ic_camera_set_af.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_af.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 916, "lastModifiedTime": 1749022453872}}, {"hashValue": "d3a93fe8d0efec060a6005c202d3107c", "name": "ic_camera_set_arrow.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_arrow.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1422, "lastModifiedTime": 1749022453872}}, {"hashValue": "a61b4feb69e03c3d958607666710a04e", "name": "ic_camera_set_checked.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_checked.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1400, "lastModifiedTime": 1749022453873}}, {"hashValue": "5ac22f0067e1d99287875612e013f929", "name": "ic_camera_set_class.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_class.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1090, "lastModifiedTime": 1749022453873}}, {"hashValue": "e80ef7c03ad20ec772bbce88000cd2d8", "name": "ic_camera_set_exposure.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_exposure.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2866, "lastModifiedTime": 1749022453873}}, {"hashValue": "ecc027f6a0e0c3eae8748fbfad1d5e89", "name": "ic_camera_set_focus.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_focus.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 519, "lastModifiedTime": 1749022453874}}, {"hashValue": "fbd4ea5b70c71cd78d16d314a4d2ec35", "name": "ic_camera_set_format.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_format.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4020, "lastModifiedTime": 1749022453874}}, {"hashValue": "6b1c2f6b0b3d2df44e34c54ae2f6009f", "name": "ic_camera_set_line.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_line.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1402, "lastModifiedTime": 1749022453875}}, {"hashValue": "8cc5166d9dda3639bbbf727e06e86379", "name": "ic_camera_set_location.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_location.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2508, "lastModifiedTime": 1749022453875}}, {"hashValue": "3517f4d041c08a7472d14e1bbd53fc34", "name": "ic_camera_set_pic_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_pic_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453876}}, {"hashValue": "530b5f9e2e6345b7f1f7e755565fc9ca", "name": "ic_camera_set_quality.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_quality.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13855, "lastModifiedTime": 1749022453876}}, {"hashValue": "1f7f4dda7b345ec5840d5c5ceb86b8cc", "name": "ic_camera_set_video_rate.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_video_rate.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1235, "lastModifiedTime": 1749022453876}}, {"hashValue": "9f57de8ee52701eb4f8d4bd2f4fa06fc", "name": "ic_camera_set_video_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_video_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1485, "lastModifiedTime": 1749022453878}}, {"hashValue": "bef04f114418568acf0698a4597eb05a", "name": "ic_camera_switch_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_switch_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1354, "lastModifiedTime": 1749022453878}}, {"hashValue": "2f057ea2d566c1e0599014c2c5811837", "name": "ic_camera_switch_off2.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_switch_off2.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 974, "lastModifiedTime": 1749022453879}}, {"hashValue": "2cc3922cf58c19c1889223ea018b80db", "name": "ic_camera_video_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 441, "lastModifiedTime": 1749022453879}}, {"hashValue": "f5b428f1a8e5eedbd02a34e3fa7cb2b8", "name": "ic_camera_video_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453879}}, {"hashValue": "58a400a0045d317e7adf29ae8c037190", "name": "ic_camera_video_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 913, "lastModifiedTime": 1749022453880}}, {"hashValue": "b3fad9c09dce8787b994d81631d29879", "name": "ic_public_brightness_filled.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_public_brightness_filled.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2685, "lastModifiedTime": 1749022453881}}, {"hashValue": "9693142322f4eafa66f416622e0f0280", "name": "ic_public_brightness.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_public_brightness.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2862, "lastModifiedTime": 1749022453880}}, {"hashValue": "d3b999e99f9c08f171ebe7ab09188297", "name": "icon_camera_setting_timer_on_balk.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer_on_balk.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453883}}, {"hashValue": "cb13e3152b25a046c7dd36edef7aa479", "name": "icon_camera_setting_timer_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453882}}, {"hashValue": "e9b0ad9dbf060377c7132d87af8c93b4", "name": "icon_camera_setting_timer.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3664, "lastModifiedTime": 1749022453882}}, {"hashValue": "f081cce0a4620543cff83f556e7aa7bd", "name": "icon_camera_setting.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3573, "lastModifiedTime": 1749022453882}}, {"hashValue": "d1044eb267800fc10fb896e22a11dd41", "name": "icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453881}}, {"hashValue": "be67575a938820711272da8fdfb4a840", "name": "pic_avatar_radio01.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_radio01.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11660, "lastModifiedTime": 1749022453883}}, {"hashValue": "58f3bcfc0169fd61ce206066c6f9794d", "name": "pic_avatar_radio02.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_radio02.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 12092, "lastModifiedTime": 1749022453884}}, {"hashValue": "a12dac4cf6f64a5967872296c6169422", "name": "pic_avatar_regular.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_regular.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14162, "lastModifiedTime": 1749022453884}}, {"hashValue": "e1277770a0627be49b7ee1ed38f0c410", "name": "pic_camera_line.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_camera_line.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 243206, "lastModifiedTime": 1749022453886}}, {"hashValue": "2f590cb280d1d441b49a19b1003b5bd6", "name": "pic_camera_mirror.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_camera_mirror.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 111725, "lastModifiedTime": 1749022453888}}, {"hashValue": "528da4db4b0c74b4ab1f781b58d0b90f", "name": "pic_popup_left.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_popup_left.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 486207, "lastModifiedTime": 1749022453890}}, {"hashValue": "55e8b4f62ca7ff5d41d4ebd02f96b6ec", "name": "pic_popup_right.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_popup_right.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 424653, "lastModifiedTime": 1749022453892}}, {"hashValue": "854908744dd8a55925c5fa3bfe59b7e4", "name": "switch_camera.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\switch_camera.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2350, "lastModifiedTime": 1749022453892}}]}, {"hashValue": "1752577f66bd1a3d28dca9eee8d5043f", "name": "profile", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5ddc969a7aad41d4c4c22edd772abc8a", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 42, "lastModifiedTime": 1749022453893}}]}]}]}, {"hashValue": "1a8f655caaa4d4bf7ef4cf8e6e23c596", "name": "resources.index", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources.index", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8917, "lastModifiedTime": 1749097572980}}, {"hashValue": "85d0c0d8928369dd3ed4fccdc5e1e318", "name": "ResourceTable.txt", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3976, "lastModifiedTime": 1749097572976}}]}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h": {"hashValue": "e6caef77277c64ccf284a136e2569a35", "name": "ResourceTable.h", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6552, "lastModifiedTime": 1749097572974}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\generated\\r\\default": {"hashValue": "644fc9fea04bace8c4d3092d26de8a4b", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\generated\\r\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "e6caef77277c64ccf284a136e2569a35", "name": "ResourceTable.h", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6552, "lastModifiedTime": 1749097572974}}]}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\loader\\default": {"hashValue": "d5bd41008650146af65305aa2396fbe4", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\loader\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "1f5a7c382e3e66af30e5252386679553", "name": "loader.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 582, "lastModifiedTime": 1749090143858}}]}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt": {"hashValue": "85d0c0d8928369dd3ed4fccdc5e1e318", "name": "ResourceTable.txt", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3976, "lastModifiedTime": 1749097572976}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\module.json": {"hashValue": "15187ca0a3503bf697de701ea314557b", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3800, "lastModifiedTime": 1749097572978}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile": {"hashValue": "1752577f66bd1a3d28dca9eee8d5043f", "name": "profile", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5ddc969a7aad41d4c4c22edd772abc8a", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 42, "lastModifiedTime": 1749022453893}}]}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\ets": {"hashValue": "579f7308889fd00b342e3980552d4ca4", "name": "ets", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\ets", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "4ffde3878be83c1f61eb0280bca31315", "name": "modules.abc", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\modules.abc", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 502756, "lastModifiedTime": 1749097589173}}, {"hashValue": "c4ac0aff4a31a444f1d04c4aba3ee572", "name": "sourceMaps.map", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 85377, "lastModifiedTime": 1749097588714}}]}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\libs\\default": {"hashValue": "8b52dc96198def0bfb55144cefaa1b20", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\libs\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "1fb4aaef56baa647cbdf880e08f4da31", "name": "arm64-v8a", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\arm64-v8a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bcbb5e7ec8be835eebf55ade0e3a01cf", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\arm64-v8a\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1260824, "lastModifiedTime": 978278400000}}, {"hashValue": "bdee882b2ed43b3859c4f99d0dc3eb10", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\arm64-v8a\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 214768, "lastModifiedTime": 1749032270417}}]}, {"hashValue": "994a7929456f6af94a65ebbae458365c", "name": "armeabi-v7a", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\armeabi-v7a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "7a315e2f9a7b1223fa1be93f092b81cf", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\armeabi-v7a\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1109100, "lastModifiedTime": 978278400000}}, {"hashValue": "4346c9099e6259337f2068a9436272e2", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\armeabi-v7a\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 192296, "lastModifiedTime": 1749032270431}}]}, {"hashValue": "1d30a2d26fcb058fd57f0313d443de7f", "name": "x86_64", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\x86_64", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c28a4178127d9b2514c6555ece74fb4c", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\x86_64\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1293080, "lastModifiedTime": 978278400000}}, {"hashValue": "f221c44cd785875e3186acc04087ae0e", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\x86_64\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 204928, "lastModifiedTime": 1749032277766}}]}]}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default": {"hashValue": "8b52dc96198def0bfb55144cefaa1b20", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "1fb4aaef56baa647cbdf880e08f4da31", "name": "arm64-v8a", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\arm64-v8a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bcbb5e7ec8be835eebf55ade0e3a01cf", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\arm64-v8a\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1260824, "lastModifiedTime": 978278400000}}, {"hashValue": "bdee882b2ed43b3859c4f99d0dc3eb10", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\arm64-v8a\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 214768, "lastModifiedTime": 1749032270417}}]}, {"hashValue": "994a7929456f6af94a65ebbae458365c", "name": "armeabi-v7a", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\armeabi-v7a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "7a315e2f9a7b1223fa1be93f092b81cf", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\armeabi-v7a\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1109100, "lastModifiedTime": 978278400000}}, {"hashValue": "4346c9099e6259337f2068a9436272e2", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\armeabi-v7a\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 192296, "lastModifiedTime": 1749032270431}}]}, {"hashValue": "1d30a2d26fcb058fd57f0313d443de7f", "name": "x86_64", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\x86_64", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c28a4178127d9b2514c6555ece74fb4c", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\x86_64\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1293080, "lastModifiedTime": 978278400000}}, {"hashValue": "f221c44cd785875e3186acc04087ae0e", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\x86_64\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 204928, "lastModifiedTime": 1749032277766}}]}]}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\patch\\default\\base_native_libs.json": {"hashValue": "b27d95b7e609a1335b7ee19143a184e4", "name": "base_native_libs.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\patch\\default\\base_native_libs.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2137, "lastModifiedTime": 1749032277972}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources": {"hashValue": "7c74b7971302f5ae70c88097521871b6", "name": "resources", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6dcaabd12ffee1d3c9a6d6bc203af712", "name": "base", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "627e3a54a0c7d5cee1e431efa84443a3", "name": "media", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0b478312ebe80ab6e93b97a4ce5c0448", "name": "app_icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\app_icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453840}}, {"hashValue": "6f84b90d6a6f23540cb85f54a38f45c5", "name": "camera_pause_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_pause_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10632, "lastModifiedTime": 1749022453864}}, {"hashValue": "387593473c128ab070274fbcc65a7bd4", "name": "camera_switch_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_switch_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6926, "lastModifiedTime": 1749022453864}}, {"hashValue": "cd9e2c9fa9a4a73403e54b0c86ddfa5a", "name": "camera_take_photo_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_take_photo_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14016, "lastModifiedTime": 1749022453865}}, {"hashValue": "69e1c567cd44f6edc7ee1108440ac0e0", "name": "camera_take_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_take_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 15819, "lastModifiedTime": 1749022453865}}, {"hashValue": "7789281643ea3f6b4a227a7e12b4d39a", "name": "camera_thumbnail_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_thumbnail_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7152, "lastModifiedTime": 1749022453866}}, {"hashValue": "63b028c7353011ee8adfa63c3a967fb0", "name": "flash_always_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\flash_always_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1898, "lastModifiedTime": 1749022453866}}, {"hashValue": "90f460eaae837069a6b51717992eee7c", "name": "ic_camera_ic_camera_radio_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_ic_camera_radio_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 795, "lastModifiedTime": 1749022453867}}, {"hashValue": "93c1941a2022ca17176f3981b58d735b", "name": "ic_camera_pad.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_pad.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1073, "lastModifiedTime": 1749022453868}}, {"hashValue": "84ffec0a6189cfd2de43aaca147901d0", "name": "ic_camera_phone.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_phone.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1339, "lastModifiedTime": 1749022453868}}, {"hashValue": "3843a663a62b92f83897b2b627e5d8f0", "name": "ic_camera_progressBar_circle.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_progressBar_circle.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1091, "lastModifiedTime": 1749022453868}}, {"hashValue": "2b4559aff0a3e5a95b8ce3cf9c9f91c4", "name": "ic_camera_public_flash_auto_white.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_auto_white.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "8fe205be5fdbe2c82d370016417c2ff3", "name": "ic_camera_public_flash_auto.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_auto.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "c863a453524f35a4d3371b7f3bc11897", "name": "ic_camera_public_flash_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2091, "lastModifiedTime": 1749022453870}}, {"hashValue": "8c5cbaa8ba0a62653b6cc2b11400b670", "name": "ic_camera_public_flash_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1487, "lastModifiedTime": 1749022453870}}, {"hashValue": "7d2c5d39f46581e2607ec9c0a31e0543", "name": "ic_camera_radio_open.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_radio_open.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1762, "lastModifiedTime": 1749022453871}}, {"hashValue": "b3a59d24aa0a41c1bc430bbdb5e4d471", "name": "ic_camera_set__antishake.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set__antishake.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5218, "lastModifiedTime": 1749022453871}}, {"hashValue": "da4cb2c6f607f8ac528071d4c0a30d16", "name": "ic_camera_set__mirror.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set__mirror.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1627, "lastModifiedTime": 1749022453871}}, {"hashValue": "d59ac73d4ec951681a0203852b9155e2", "name": "ic_camera_set_af.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_af.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 916, "lastModifiedTime": 1749022453872}}, {"hashValue": "d3a93fe8d0efec060a6005c202d3107c", "name": "ic_camera_set_arrow.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_arrow.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1422, "lastModifiedTime": 1749022453872}}, {"hashValue": "a61b4feb69e03c3d958607666710a04e", "name": "ic_camera_set_checked.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_checked.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1400, "lastModifiedTime": 1749022453873}}, {"hashValue": "5ac22f0067e1d99287875612e013f929", "name": "ic_camera_set_class.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_class.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1090, "lastModifiedTime": 1749022453873}}, {"hashValue": "e80ef7c03ad20ec772bbce88000cd2d8", "name": "ic_camera_set_exposure.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_exposure.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2866, "lastModifiedTime": 1749022453873}}, {"hashValue": "ecc027f6a0e0c3eae8748fbfad1d5e89", "name": "ic_camera_set_focus.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_focus.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 519, "lastModifiedTime": 1749022453874}}, {"hashValue": "fbd4ea5b70c71cd78d16d314a4d2ec35", "name": "ic_camera_set_format.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_format.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4020, "lastModifiedTime": 1749022453874}}, {"hashValue": "6b1c2f6b0b3d2df44e34c54ae2f6009f", "name": "ic_camera_set_line.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_line.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1402, "lastModifiedTime": 1749022453875}}, {"hashValue": "8cc5166d9dda3639bbbf727e06e86379", "name": "ic_camera_set_location.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_location.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2508, "lastModifiedTime": 1749022453875}}, {"hashValue": "3517f4d041c08a7472d14e1bbd53fc34", "name": "ic_camera_set_pic_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_pic_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453876}}, {"hashValue": "530b5f9e2e6345b7f1f7e755565fc9ca", "name": "ic_camera_set_quality.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_quality.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13855, "lastModifiedTime": 1749022453876}}, {"hashValue": "1f7f4dda7b345ec5840d5c5ceb86b8cc", "name": "ic_camera_set_video_rate.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_video_rate.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1235, "lastModifiedTime": 1749022453876}}, {"hashValue": "9f57de8ee52701eb4f8d4bd2f4fa06fc", "name": "ic_camera_set_video_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_video_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1485, "lastModifiedTime": 1749022453878}}, {"hashValue": "bef04f114418568acf0698a4597eb05a", "name": "ic_camera_switch_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_switch_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1354, "lastModifiedTime": 1749022453878}}, {"hashValue": "2f057ea2d566c1e0599014c2c5811837", "name": "ic_camera_switch_off2.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_switch_off2.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 974, "lastModifiedTime": 1749022453879}}, {"hashValue": "2cc3922cf58c19c1889223ea018b80db", "name": "ic_camera_video_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 441, "lastModifiedTime": 1749022453879}}, {"hashValue": "f5b428f1a8e5eedbd02a34e3fa7cb2b8", "name": "ic_camera_video_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453879}}, {"hashValue": "58a400a0045d317e7adf29ae8c037190", "name": "ic_camera_video_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 913, "lastModifiedTime": 1749022453880}}, {"hashValue": "b3fad9c09dce8787b994d81631d29879", "name": "ic_public_brightness_filled.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_public_brightness_filled.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2685, "lastModifiedTime": 1749022453881}}, {"hashValue": "9693142322f4eafa66f416622e0f0280", "name": "ic_public_brightness.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_public_brightness.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2862, "lastModifiedTime": 1749022453880}}, {"hashValue": "d3b999e99f9c08f171ebe7ab09188297", "name": "icon_camera_setting_timer_on_balk.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer_on_balk.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453883}}, {"hashValue": "cb13e3152b25a046c7dd36edef7aa479", "name": "icon_camera_setting_timer_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453882}}, {"hashValue": "e9b0ad9dbf060377c7132d87af8c93b4", "name": "icon_camera_setting_timer.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3664, "lastModifiedTime": 1749022453882}}, {"hashValue": "f081cce0a4620543cff83f556e7aa7bd", "name": "icon_camera_setting.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3573, "lastModifiedTime": 1749022453882}}, {"hashValue": "d1044eb267800fc10fb896e22a11dd41", "name": "icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453881}}, {"hashValue": "be67575a938820711272da8fdfb4a840", "name": "pic_avatar_radio01.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_radio01.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11660, "lastModifiedTime": 1749022453883}}, {"hashValue": "58f3bcfc0169fd61ce206066c6f9794d", "name": "pic_avatar_radio02.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_radio02.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 12092, "lastModifiedTime": 1749022453884}}, {"hashValue": "a12dac4cf6f64a5967872296c6169422", "name": "pic_avatar_regular.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_regular.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14162, "lastModifiedTime": 1749022453884}}, {"hashValue": "e1277770a0627be49b7ee1ed38f0c410", "name": "pic_camera_line.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_camera_line.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 243206, "lastModifiedTime": 1749022453886}}, {"hashValue": "2f590cb280d1d441b49a19b1003b5bd6", "name": "pic_camera_mirror.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_camera_mirror.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 111725, "lastModifiedTime": 1749022453888}}, {"hashValue": "528da4db4b0c74b4ab1f781b58d0b90f", "name": "pic_popup_left.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_popup_left.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 486207, "lastModifiedTime": 1749022453890}}, {"hashValue": "55e8b4f62ca7ff5d41d4ebd02f96b6ec", "name": "pic_popup_right.png", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_popup_right.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 424653, "lastModifiedTime": 1749022453892}}, {"hashValue": "854908744dd8a55925c5fa3bfe59b7e4", "name": "switch_camera.svg", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\switch_camera.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2350, "lastModifiedTime": 1749022453892}}]}, {"hashValue": "1752577f66bd1a3d28dca9eee8d5043f", "name": "profile", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5ddc969a7aad41d4c4c22edd772abc8a", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 42, "lastModifiedTime": 1749022453893}}]}]}]}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources.index": {"hashValue": "1a8f655caaa4d4bf7ef4cf8e6e23c596", "name": "resources.index", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources.index", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8917, "lastModifiedTime": 1749097572980}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap": {"hashValue": "c089080b5943fefc9741694ff2fd04a4", "name": "entry-default-unsigned.hap", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6336864, "lastModifiedTime": 1749097590695}}, "C:/Users/<USER>/.ohos/config/openharmony/default_Camera_EPfVXiznkGjAYY-iP3pDfPAAWzDBtm6ICBWPCJTE0UI=.cer": {"hashValue": "2b1da359a82fcb49686f1eb0b6704e0c", "name": "default_Camera_EPfVXiznkGjAYY-iP3pDfPAAWzDBtm6ICBWPCJTE0UI=.cer", "path": "C:/Users/<USER>/.ohos/config/openharmony/default_Camera_EPfVXiznkGjAYY-iP3pDfPAAWzDBtm6ICBWPCJTE0UI=.cer", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1698, "lastModifiedTime": 1749025874252}}, "C:/Users/<USER>/.ohos/config/openharmony/default_Camera_EPfVXiznkGjAYY-iP3pDfPAAWzDBtm6ICBWPCJTE0UI=.p7b": {"hashValue": "d949e3c191967c75ae43877a99b2d201", "name": "default_Camera_EPfVXiznkGjAYY-iP3pDfPAAWzDBtm6ICBWPCJTE0UI=.p7b", "path": "C:/Users/<USER>/.ohos/config/openharmony/default_Camera_EPfVXiznkGjAYY-iP3pDfPAAWzDBtm6ICBWPCJTE0UI=.p7b", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3426, "lastModifiedTime": 1749025877497}}, "C:/Users/<USER>/.ohos/config/openharmony/default_Camera_EPfVXiznkGjAYY-iP3pDfPAAWzDBtm6ICBWPCJTE0UI=.p12": {"hashValue": "410037c6171bf44a9180637b5c8052dc", "name": "default_Camera_EPfVXiznkGjAYY-iP3pDfPAAWzDBtm6ICBWPCJTE0UI=.p12", "path": "C:/Users/<USER>/.ohos/config/openharmony/default_Camera_EPfVXiznkGjAYY-iP3pDfPAAWzDBtm6ICBWPCJTE0UI=.p12", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1128, "lastModifiedTime": 1749025872303}}, "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap": {"hashValue": "acaee7098269749182bda4a9df08224d", "name": "entry-default-signed.hap", "path": "D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6430967, "lastModifiedTime": 1749097597791}}, "C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.cer": {"hashValue": "0ef1eb36b070f41cfe25bf0771e7a6eb", "name": "default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.cer", "path": "C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.cer", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1698, "lastModifiedTime": 1749097432655}}, "C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.p7b": {"hashValue": "b0e807b170dfb621afc8dc41c1da3122", "name": "default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.p7b", "path": "C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.p7b", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3425, "lastModifiedTime": 1749097434721}}, "C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.p12": {"hashValue": "2a536c636f6190c40adfedadfc94d7c5", "name": "default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.p12", "path": "C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.p12", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1128, "lastModifiedTime": 1749097430630}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\app.json5": {"hashValue": "b272ae09c35f24ae3fab922faad24c82", "name": "app.json5", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\app.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 883, "lastModifiedTime": 1749022453838}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\module.json5": {"hashValue": "8a89638127253b3d548cd28f1b96f6c1", "name": "module.json5", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\module.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2563, "lastModifiedTime": 1749024082080}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\build-profile.json5": {"hashValue": "e8693f92c7bebe783cb0e9adbf3a5eca", "name": "build-profile.json5", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\build-profile.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1951, "lastModifiedTime": 1749097438783}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build-profile.json5": {"hashValue": "a6f543b6c877af84066d4050145db38e", "name": "build-profile.json5", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build-profile.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1015, "lastModifiedTime": 1749024082077}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\profile\\main_pages.json": {"hashValue": "5ddc969a7aad41d4c4c22edd772abc8a", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 42, "lastModifiedTime": 1749022453893}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\oh-package.json5": {"hashValue": "dfebcc02ee1adc714cb3f7a31f3f6448", "name": "oh-package.json5", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\oh-package.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 248, "lastModifiedTime": 1749022453845}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json": {"hashValue": "02a996a9476e07f52e44efd9850c8cb0", "name": "output_metadata.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 92, "lastModifiedTime": 1750683451145}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets": {"hashValue": "d14098e12dfb2a2349baf81fee547974", "name": "BuildProfile.ets", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 371, "lastModifiedTime": 1750683451153}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader\\default\\loader.json": {"hashValue": "ea9919101057b94d436f3efc4e6aae98", "name": "loader.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 582, "lastModifiedTime": 1750683451177}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json": {"hashValue": "afd40d91a2660538cb1f63fa76967329", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1992, "lastModifiedTime": 1750683451187}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\pack.info": {"hashValue": "6e7a1a0faf3529cb6cd7b080dcef0932", "name": "pack.info", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\pack.info", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 556, "lastModifiedTime": 1750683451221}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json": {"hashValue": "547ab8b5fe932f0ede90b3c50b9a7140", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2078, "lastModifiedTime": 1750683451448}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json": {"hashValue": "8c6a4094341a0c3457e86425d766b988", "name": "resConfig.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 955, "lastModifiedTime": 1750683451457}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources": {"hashValue": "31cbb6f14ce0b66d6da5b0c3c2b0f1f8", "name": "resources", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "4f9d140339e81b34f5e3d07a5a5994f5", "name": "base", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "8375a0c8f7e0cd7ea2c936983c993ade", "name": "element", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "045277200a69951ccd3352446594fc69", "name": "color.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\element\\color.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 241, "lastModifiedTime": 1749022453863}}, {"hashValue": "0e847e049e12144d3f7ffc34796d1fbc", "name": "string.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\element\\string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 272, "lastModifiedTime": 1749022453863}}]}, {"hashValue": "5ded41793140ea643198cebd4ba59fb8", "name": "media", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6f84b90d6a6f23540cb85f54a38f45c5", "name": "camera_pause_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\camera_pause_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10632, "lastModifiedTime": 1749022453864}}, {"hashValue": "387593473c128ab070274fbcc65a7bd4", "name": "camera_switch_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\camera_switch_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6926, "lastModifiedTime": 1749022453864}}, {"hashValue": "cd9e2c9fa9a4a73403e54b0c86ddfa5a", "name": "camera_take_photo_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\camera_take_photo_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14016, "lastModifiedTime": 1749022453865}}, {"hashValue": "69e1c567cd44f6edc7ee1108440ac0e0", "name": "camera_take_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\camera_take_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 15819, "lastModifiedTime": 1749022453865}}, {"hashValue": "7789281643ea3f6b4a227a7e12b4d39a", "name": "camera_thumbnail_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\camera_thumbnail_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7152, "lastModifiedTime": 1749022453866}}, {"hashValue": "63b028c7353011ee8adfa63c3a967fb0", "name": "flash_always_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\flash_always_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1898, "lastModifiedTime": 1749022453866}}, {"hashValue": "90f460eaae837069a6b51717992eee7c", "name": "ic_camera_ic_camera_radio_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_ic_camera_radio_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 795, "lastModifiedTime": 1749022453867}}, {"hashValue": "93c1941a2022ca17176f3981b58d735b", "name": "ic_camera_pad.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_pad.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1073, "lastModifiedTime": 1749022453868}}, {"hashValue": "84ffec0a6189cfd2de43aaca147901d0", "name": "ic_camera_phone.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_phone.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1339, "lastModifiedTime": 1749022453868}}, {"hashValue": "3843a663a62b92f83897b2b627e5d8f0", "name": "ic_camera_progressBar_circle.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_progressBar_circle.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1091, "lastModifiedTime": 1749022453868}}, {"hashValue": "2b4559aff0a3e5a95b8ce3cf9c9f91c4", "name": "ic_camera_public_flash_auto_white.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_public_flash_auto_white.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "8fe205be5fdbe2c82d370016417c2ff3", "name": "ic_camera_public_flash_auto.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_public_flash_auto.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "c863a453524f35a4d3371b7f3bc11897", "name": "ic_camera_public_flash_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_public_flash_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2091, "lastModifiedTime": 1749022453870}}, {"hashValue": "8c5cbaa8ba0a62653b6cc2b11400b670", "name": "ic_camera_public_flash_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_public_flash_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1487, "lastModifiedTime": 1749022453870}}, {"hashValue": "7d2c5d39f46581e2607ec9c0a31e0543", "name": "ic_camera_radio_open.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_radio_open.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1762, "lastModifiedTime": 1749022453871}}, {"hashValue": "b3a59d24aa0a41c1bc430bbdb5e4d471", "name": "ic_camera_set__antishake.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set__antishake.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5218, "lastModifiedTime": 1749022453871}}, {"hashValue": "da4cb2c6f607f8ac528071d4c0a30d16", "name": "ic_camera_set__mirror.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set__mirror.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1627, "lastModifiedTime": 1749022453871}}, {"hashValue": "d59ac73d4ec951681a0203852b9155e2", "name": "ic_camera_set_af.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_af.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 916, "lastModifiedTime": 1749022453872}}, {"hashValue": "d3a93fe8d0efec060a6005c202d3107c", "name": "ic_camera_set_arrow.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_arrow.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1422, "lastModifiedTime": 1749022453872}}, {"hashValue": "a61b4feb69e03c3d958607666710a04e", "name": "ic_camera_set_checked.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_checked.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1400, "lastModifiedTime": 1749022453873}}, {"hashValue": "5ac22f0067e1d99287875612e013f929", "name": "ic_camera_set_class.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_class.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1090, "lastModifiedTime": 1749022453873}}, {"hashValue": "e80ef7c03ad20ec772bbce88000cd2d8", "name": "ic_camera_set_exposure.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_exposure.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2866, "lastModifiedTime": 1749022453873}}, {"hashValue": "ecc027f6a0e0c3eae8748fbfad1d5e89", "name": "ic_camera_set_focus.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_focus.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 519, "lastModifiedTime": 1749022453874}}, {"hashValue": "fbd4ea5b70c71cd78d16d314a4d2ec35", "name": "ic_camera_set_format.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_format.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4020, "lastModifiedTime": 1749022453874}}, {"hashValue": "6b1c2f6b0b3d2df44e34c54ae2f6009f", "name": "ic_camera_set_line.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_line.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1402, "lastModifiedTime": 1749022453875}}, {"hashValue": "8cc5166d9dda3639bbbf727e06e86379", "name": "ic_camera_set_location.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_location.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2508, "lastModifiedTime": 1749022453875}}, {"hashValue": "3517f4d041c08a7472d14e1bbd53fc34", "name": "ic_camera_set_pic_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_pic_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453876}}, {"hashValue": "530b5f9e2e6345b7f1f7e755565fc9ca", "name": "ic_camera_set_quality.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_quality.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13855, "lastModifiedTime": 1749022453876}}, {"hashValue": "1f7f4dda7b345ec5840d5c5ceb86b8cc", "name": "ic_camera_set_video_rate.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_video_rate.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1235, "lastModifiedTime": 1749022453876}}, {"hashValue": "9f57de8ee52701eb4f8d4bd2f4fa06fc", "name": "ic_camera_set_video_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_set_video_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1485, "lastModifiedTime": 1749022453878}}, {"hashValue": "bef04f114418568acf0698a4597eb05a", "name": "ic_camera_switch_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_switch_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1354, "lastModifiedTime": 1749022453878}}, {"hashValue": "2f057ea2d566c1e0599014c2c5811837", "name": "ic_camera_switch_off2.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_switch_off2.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 974, "lastModifiedTime": 1749022453879}}, {"hashValue": "2cc3922cf58c19c1889223ea018b80db", "name": "ic_camera_video_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_video_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 441, "lastModifiedTime": 1749022453879}}, {"hashValue": "f5b428f1a8e5eedbd02a34e3fa7cb2b8", "name": "ic_camera_video_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_video_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453879}}, {"hashValue": "58a400a0045d317e7adf29ae8c037190", "name": "ic_camera_video_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_camera_video_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 913, "lastModifiedTime": 1749022453880}}, {"hashValue": "b3fad9c09dce8787b994d81631d29879", "name": "ic_public_brightness_filled.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_public_brightness_filled.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2685, "lastModifiedTime": 1749022453881}}, {"hashValue": "9693142322f4eafa66f416622e0f0280", "name": "ic_public_brightness.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\ic_public_brightness.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2862, "lastModifiedTime": 1749022453880}}, {"hashValue": "d3b999e99f9c08f171ebe7ab09188297", "name": "icon_camera_setting_timer_on_balk.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\icon_camera_setting_timer_on_balk.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453883}}, {"hashValue": "cb13e3152b25a046c7dd36edef7aa479", "name": "icon_camera_setting_timer_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\icon_camera_setting_timer_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453882}}, {"hashValue": "e9b0ad9dbf060377c7132d87af8c93b4", "name": "icon_camera_setting_timer.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\icon_camera_setting_timer.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3664, "lastModifiedTime": 1749022453882}}, {"hashValue": "f081cce0a4620543cff83f556e7aa7bd", "name": "icon_camera_setting.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\icon_camera_setting.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3573, "lastModifiedTime": 1749022453882}}, {"hashValue": "d1044eb267800fc10fb896e22a11dd41", "name": "icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453881}}, {"hashValue": "be67575a938820711272da8fdfb4a840", "name": "pic_avatar_radio01.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_avatar_radio01.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11660, "lastModifiedTime": 1749022453883}}, {"hashValue": "58f3bcfc0169fd61ce206066c6f9794d", "name": "pic_avatar_radio02.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_avatar_radio02.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 12092, "lastModifiedTime": 1749022453884}}, {"hashValue": "a12dac4cf6f64a5967872296c6169422", "name": "pic_avatar_regular.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_avatar_regular.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14162, "lastModifiedTime": 1749022453884}}, {"hashValue": "e1277770a0627be49b7ee1ed38f0c410", "name": "pic_camera_line.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_camera_line.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 243206, "lastModifiedTime": 1749022453886}}, {"hashValue": "2f590cb280d1d441b49a19b1003b5bd6", "name": "pic_camera_mirror.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_camera_mirror.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 111725, "lastModifiedTime": 1749022453888}}, {"hashValue": "528da4db4b0c74b4ab1f781b58d0b90f", "name": "pic_popup_left.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_popup_left.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 486207, "lastModifiedTime": 1749022453890}}, {"hashValue": "55e8b4f62ca7ff5d41d4ebd02f96b6ec", "name": "pic_popup_right.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\pic_popup_right.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 424653, "lastModifiedTime": 1749022453892}}, {"hashValue": "854908744dd8a55925c5fa3bfe59b7e4", "name": "switch_camera.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\media\\switch_camera.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2350, "lastModifiedTime": 1749022453892}}]}, {"hashValue": "1752577f66bd1a3d28dca9eee8d5043f", "name": "profile", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5ddc969a7aad41d4c4c22edd772abc8a", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 42, "lastModifiedTime": 1749022453893}}]}]}, {"hashValue": "fb73190687b4b3400e7a2b50b517f5c7", "name": "en_US", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\en_US", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "a4bac92b2c6e07a678f2adcdbb4c8489", "name": "element", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\en_US\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "f55483b907c93ff2167cd50c4eab5a30", "name": "string.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\en_US\\element\\string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 272, "lastModifiedTime": 1749022453894}}]}]}, {"hashValue": "d678701bc92e3c634d339130a904bca2", "name": "zh_CN", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\zh_CN", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "eafc7f9552b4fd1a785b2c4d4a9361ba", "name": "element", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\zh_CN\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c631cc9d37ee8466504a4d24c382d0e2", "name": "string.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\zh_CN\\element\\string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 266, "lastModifiedTime": 1749022453894}}]}]}]}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\resources": {"hashValue": "7bc75c4a9d7c24ed0ab0acdf64a9bd55", "name": "resources", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "563d683e2ba4327fdb3be313d2b08fd9", "name": "base", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c5e816eee4bff7277f64b88b80c46bd4", "name": "element", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\resources\\base\\element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "69dcc072c81241b9f0f10acda170c627", "name": "string.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\resources\\base\\element\\string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4405, "lastModifiedTime": 1749022453839}}]}, {"hashValue": "e21a9b0a678508e904265d0756c5a3b0", "name": "media", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0b478312ebe80ab6e93b97a4ce5c0448", "name": "app_icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\resources\\base\\media\\app_icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453840}}]}]}]}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default": {"hashValue": "0ffb317b78902637a08b28a2ce7d1994", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "69eeed78af029157be433a1b97fa9347", "name": "ids_map", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ids_map", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "cd455f4ac116d253b7634998c8a0a44b", "name": "id_defined.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ids_map\\id_defined.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13907, "lastModifiedTime": 1750683451772}}]}, {"hashValue": "bf5258b70313088ea547a01d6b5e1853", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3800, "lastModifiedTime": 1750683451768}}, {"hashValue": "8c6a4094341a0c3457e86425d766b988", "name": "resConfig.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 955, "lastModifiedTime": 1750683451457}}, {"hashValue": "7c74b7971302f5ae70c88097521871b6", "name": "resources", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6dcaabd12ffee1d3c9a6d6bc203af712", "name": "base", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "627e3a54a0c7d5cee1e431efa84443a3", "name": "media", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0b478312ebe80ab6e93b97a4ce5c0448", "name": "app_icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\app_icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453840}}, {"hashValue": "6f84b90d6a6f23540cb85f54a38f45c5", "name": "camera_pause_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_pause_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10632, "lastModifiedTime": 1749022453864}}, {"hashValue": "387593473c128ab070274fbcc65a7bd4", "name": "camera_switch_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_switch_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6926, "lastModifiedTime": 1749022453864}}, {"hashValue": "cd9e2c9fa9a4a73403e54b0c86ddfa5a", "name": "camera_take_photo_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_take_photo_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14016, "lastModifiedTime": 1749022453865}}, {"hashValue": "69e1c567cd44f6edc7ee1108440ac0e0", "name": "camera_take_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_take_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 15819, "lastModifiedTime": 1749022453865}}, {"hashValue": "7789281643ea3f6b4a227a7e12b4d39a", "name": "camera_thumbnail_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_thumbnail_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7152, "lastModifiedTime": 1749022453866}}, {"hashValue": "63b028c7353011ee8adfa63c3a967fb0", "name": "flash_always_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\flash_always_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1898, "lastModifiedTime": 1749022453866}}, {"hashValue": "90f460eaae837069a6b51717992eee7c", "name": "ic_camera_ic_camera_radio_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_ic_camera_radio_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 795, "lastModifiedTime": 1749022453867}}, {"hashValue": "93c1941a2022ca17176f3981b58d735b", "name": "ic_camera_pad.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_pad.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1073, "lastModifiedTime": 1749022453868}}, {"hashValue": "84ffec0a6189cfd2de43aaca147901d0", "name": "ic_camera_phone.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_phone.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1339, "lastModifiedTime": 1749022453868}}, {"hashValue": "3843a663a62b92f83897b2b627e5d8f0", "name": "ic_camera_progressBar_circle.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_progressBar_circle.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1091, "lastModifiedTime": 1749022453868}}, {"hashValue": "2b4559aff0a3e5a95b8ce3cf9c9f91c4", "name": "ic_camera_public_flash_auto_white.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_auto_white.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "8fe205be5fdbe2c82d370016417c2ff3", "name": "ic_camera_public_flash_auto.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_auto.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "c863a453524f35a4d3371b7f3bc11897", "name": "ic_camera_public_flash_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2091, "lastModifiedTime": 1749022453870}}, {"hashValue": "8c5cbaa8ba0a62653b6cc2b11400b670", "name": "ic_camera_public_flash_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1487, "lastModifiedTime": 1749022453870}}, {"hashValue": "7d2c5d39f46581e2607ec9c0a31e0543", "name": "ic_camera_radio_open.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_radio_open.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1762, "lastModifiedTime": 1749022453871}}, {"hashValue": "b3a59d24aa0a41c1bc430bbdb5e4d471", "name": "ic_camera_set__antishake.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set__antishake.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5218, "lastModifiedTime": 1749022453871}}, {"hashValue": "da4cb2c6f607f8ac528071d4c0a30d16", "name": "ic_camera_set__mirror.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set__mirror.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1627, "lastModifiedTime": 1749022453871}}, {"hashValue": "d59ac73d4ec951681a0203852b9155e2", "name": "ic_camera_set_af.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_af.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 916, "lastModifiedTime": 1749022453872}}, {"hashValue": "d3a93fe8d0efec060a6005c202d3107c", "name": "ic_camera_set_arrow.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_arrow.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1422, "lastModifiedTime": 1749022453872}}, {"hashValue": "a61b4feb69e03c3d958607666710a04e", "name": "ic_camera_set_checked.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_checked.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1400, "lastModifiedTime": 1749022453873}}, {"hashValue": "5ac22f0067e1d99287875612e013f929", "name": "ic_camera_set_class.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_class.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1090, "lastModifiedTime": 1749022453873}}, {"hashValue": "e80ef7c03ad20ec772bbce88000cd2d8", "name": "ic_camera_set_exposure.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_exposure.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2866, "lastModifiedTime": 1749022453873}}, {"hashValue": "ecc027f6a0e0c3eae8748fbfad1d5e89", "name": "ic_camera_set_focus.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_focus.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 519, "lastModifiedTime": 1749022453874}}, {"hashValue": "fbd4ea5b70c71cd78d16d314a4d2ec35", "name": "ic_camera_set_format.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_format.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4020, "lastModifiedTime": 1749022453874}}, {"hashValue": "6b1c2f6b0b3d2df44e34c54ae2f6009f", "name": "ic_camera_set_line.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_line.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1402, "lastModifiedTime": 1749022453875}}, {"hashValue": "8cc5166d9dda3639bbbf727e06e86379", "name": "ic_camera_set_location.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_location.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2508, "lastModifiedTime": 1749022453875}}, {"hashValue": "3517f4d041c08a7472d14e1bbd53fc34", "name": "ic_camera_set_pic_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_pic_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453876}}, {"hashValue": "530b5f9e2e6345b7f1f7e755565fc9ca", "name": "ic_camera_set_quality.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_quality.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13855, "lastModifiedTime": 1749022453876}}, {"hashValue": "1f7f4dda7b345ec5840d5c5ceb86b8cc", "name": "ic_camera_set_video_rate.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_video_rate.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1235, "lastModifiedTime": 1749022453876}}, {"hashValue": "9f57de8ee52701eb4f8d4bd2f4fa06fc", "name": "ic_camera_set_video_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_video_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1485, "lastModifiedTime": 1749022453878}}, {"hashValue": "bef04f114418568acf0698a4597eb05a", "name": "ic_camera_switch_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_switch_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1354, "lastModifiedTime": 1749022453878}}, {"hashValue": "2f057ea2d566c1e0599014c2c5811837", "name": "ic_camera_switch_off2.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_switch_off2.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 974, "lastModifiedTime": 1749022453879}}, {"hashValue": "2cc3922cf58c19c1889223ea018b80db", "name": "ic_camera_video_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 441, "lastModifiedTime": 1749022453879}}, {"hashValue": "f5b428f1a8e5eedbd02a34e3fa7cb2b8", "name": "ic_camera_video_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453879}}, {"hashValue": "58a400a0045d317e7adf29ae8c037190", "name": "ic_camera_video_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 913, "lastModifiedTime": 1749022453880}}, {"hashValue": "b3fad9c09dce8787b994d81631d29879", "name": "ic_public_brightness_filled.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_public_brightness_filled.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2685, "lastModifiedTime": 1749022453881}}, {"hashValue": "9693142322f4eafa66f416622e0f0280", "name": "ic_public_brightness.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_public_brightness.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2862, "lastModifiedTime": 1749022453880}}, {"hashValue": "d3b999e99f9c08f171ebe7ab09188297", "name": "icon_camera_setting_timer_on_balk.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer_on_balk.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453883}}, {"hashValue": "cb13e3152b25a046c7dd36edef7aa479", "name": "icon_camera_setting_timer_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453882}}, {"hashValue": "e9b0ad9dbf060377c7132d87af8c93b4", "name": "icon_camera_setting_timer.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3664, "lastModifiedTime": 1749022453882}}, {"hashValue": "f081cce0a4620543cff83f556e7aa7bd", "name": "icon_camera_setting.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3573, "lastModifiedTime": 1749022453882}}, {"hashValue": "d1044eb267800fc10fb896e22a11dd41", "name": "icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453881}}, {"hashValue": "be67575a938820711272da8fdfb4a840", "name": "pic_avatar_radio01.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_radio01.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11660, "lastModifiedTime": 1749022453883}}, {"hashValue": "58f3bcfc0169fd61ce206066c6f9794d", "name": "pic_avatar_radio02.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_radio02.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 12092, "lastModifiedTime": 1749022453884}}, {"hashValue": "a12dac4cf6f64a5967872296c6169422", "name": "pic_avatar_regular.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_regular.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14162, "lastModifiedTime": 1749022453884}}, {"hashValue": "e1277770a0627be49b7ee1ed38f0c410", "name": "pic_camera_line.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_camera_line.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 243206, "lastModifiedTime": 1749022453886}}, {"hashValue": "2f590cb280d1d441b49a19b1003b5bd6", "name": "pic_camera_mirror.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_camera_mirror.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 111725, "lastModifiedTime": 1749022453888}}, {"hashValue": "528da4db4b0c74b4ab1f781b58d0b90f", "name": "pic_popup_left.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_popup_left.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 486207, "lastModifiedTime": 1749022453890}}, {"hashValue": "55e8b4f62ca7ff5d41d4ebd02f96b6ec", "name": "pic_popup_right.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_popup_right.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 424653, "lastModifiedTime": 1749022453892}}, {"hashValue": "854908744dd8a55925c5fa3bfe59b7e4", "name": "switch_camera.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\switch_camera.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2350, "lastModifiedTime": 1749022453892}}]}, {"hashValue": "1752577f66bd1a3d28dca9eee8d5043f", "name": "profile", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5ddc969a7aad41d4c4c22edd772abc8a", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 42, "lastModifiedTime": 1749022453893}}]}]}]}, {"hashValue": "54b4bee4cb02671e32e9b63cf601dcc3", "name": "resources.index", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources.index", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8917, "lastModifiedTime": 1750683451770}}, {"hashValue": "dc9543cd8278b6dd4797acf9e7da5301", "name": "ResourceTable.txt", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3976, "lastModifiedTime": 1750683451766}}]}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h": {"hashValue": "776b8e7dbe07334aac26169dddbf9587", "name": "ResourceTable.h", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6552, "lastModifiedTime": 1750683451765}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\r\\default": {"hashValue": "cd559e2e8dc0c1749df7f1d36504b66f", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\r\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "776b8e7dbe07334aac26169dddbf9587", "name": "ResourceTable.h", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6552, "lastModifiedTime": 1750683451765}}]}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader\\default": {"hashValue": "b50fb4ed41f6e87d1731c7c6647b484a", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ea9919101057b94d436f3efc4e6aae98", "name": "loader.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 582, "lastModifiedTime": 1750683451177}}]}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt": {"hashValue": "dc9543cd8278b6dd4797acf9e7da5301", "name": "ResourceTable.txt", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3976, "lastModifiedTime": 1750683451766}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\module.json": {"hashValue": "bf5258b70313088ea547a01d6b5e1853", "name": "module.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3800, "lastModifiedTime": 1750683451768}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile": {"hashValue": "1752577f66bd1a3d28dca9eee8d5043f", "name": "profile", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5ddc969a7aad41d4c4c22edd772abc8a", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 42, "lastModifiedTime": 1749022453893}}]}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets": {"hashValue": "e975d13e36b82c96027e38615371a226", "name": "ets", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "210e0c050b75f1a5b617344717096c88", "name": "common", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\common", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "2fa07f50c1c40e9f2da16e0be769ad39", "name": "Constants.ts", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\common\\Constants.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2352, "lastModifiedTime": 1749022453851}}, {"hashValue": "726cfaeed739814f125de17b312a4dec", "name": "DisplayCalculator.ts", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\common\\DisplayCalculator.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1797, "lastModifiedTime": 1749022453851}}, {"hashValue": "ebfbe2c26e338083ae725beafd6ca345", "name": "SettingItem.ets", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\common\\SettingItem.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4340, "lastModifiedTime": 1749022453852}}, {"hashValue": "614e88984a4b6d11c8fbb1ada155501d", "name": "SettingPublicLayout.ets", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\common\\SettingPublicLayout.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3575, "lastModifiedTime": 1749022453852}}, {"hashValue": "f67bd173635ebfaec6c113f9f803cde0", "name": "SettingRightLayout.ets", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\common\\SettingRightLayout.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6234, "lastModifiedTime": 1749022453853}}]}, {"hashValue": "d823d5d0172d34fa1662c9331c64f1b9", "name": "Dialog", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\Dialog", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "a31963b78e2f77c76bb4443d0cff6084", "name": "MainDialog.ets", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\Dialog\\MainDialog.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2977, "lastModifiedTime": 1749022453850}}, {"hashValue": "ad0ad06a44cd971cf2765aa35030ad1c", "name": "SettingDialog.ets", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\Dialog\\SettingDialog.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7728, "lastModifiedTime": 1749022453850}}]}, {"hashValue": "d24c9916b230ea26067a94699aa21881", "name": "entryability", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\entryability", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "a30db74181c55d3f533ec7fd54252883", "name": "EntryAbility.ts", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\entryability\\EntryAbility.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2368, "lastModifiedTime": 1749022453853}}]}, {"hashValue": "a07cbc9dc2af3dc47dfbc9a697878474", "name": "model", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\model", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ddf863ab55b13ec8adaed4f1847b3f00", "name": "DateTimeUtil.ts", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\model\\DateTimeUtil.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2062, "lastModifiedTime": 1749022453854}}, {"hashValue": "7b99e20512aba2ff6fd9ee45f98d0e0e", "name": "Logger.ts", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\model\\Logger.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1312, "lastModifiedTime": 1749022453854}}, {"hashValue": "af6f5a81029b1c079b6616dbd7dca9cd", "name": "MediaUtils.ts", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\model\\MediaUtils.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3458, "lastModifiedTime": 1749022453855}}]}, {"hashValue": "861d41a7b95078e8ddf1b0086c609709", "name": "pages", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\pages", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "7e9006753b4da2ad164eacb21b5c4f12", "name": "Index.ets", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\pages\\Index.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7948, "lastModifiedTime": 1749022453855}}]}, {"hashValue": "98bbdcbf4bb7ea8ec16ce098ca5504d2", "name": "views", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\views", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5c2ab13cd5e50160be16ca7fa35df0e3", "name": "CountdownPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\views\\CountdownPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3166, "lastModifiedTime": 1749022453856}}, {"hashValue": "3892e004ba5eb99c243ef912ba5fe010", "name": "DividerPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\views\\DividerPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1150, "lastModifiedTime": 1749022453856}}, {"hashValue": "72a84af30d3bc4a5cd0d8ca43e89c7a6", "name": "FlashingLightPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\views\\FlashingLightPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3914, "lastModifiedTime": 1749022453857}}, {"hashValue": "0562df559ccfdde6befb95b0d1914d0d", "name": "FocusAreaPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\views\\FocusAreaPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3362, "lastModifiedTime": 1749022453857}}, {"hashValue": "553ac0d07c4c6127f9f79c5a8d9d75ac", "name": "FocusPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\views\\FocusPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4414, "lastModifiedTime": 1749022453858}}, {"hashValue": "7c65c6b53549f1b3c9e5ebb1b43ede8e", "name": "ModeSwitchPage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\views\\ModeSwitchPage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 16947, "lastModifiedTime": 1749024082079}}, {"hashValue": "a885876ccb165b0c0b76514cf6b93e29", "name": "SlidePage.ets", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets\\views\\SlidePage.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2740, "lastModifiedTime": 1749022453861}}]}]}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\ets": {"hashValue": "ad40df43eb6914f0cefb9ef847c97efc", "name": "ets", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\ets", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "d62efd63b2e7ee487ae54eaf039c5ffe", "name": "modules.abc", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\modules.abc", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 502756, "lastModifiedTime": 1750683463947}}, {"hashValue": "90f9018993f6ddc7616bf2ba1af901d8", "name": "sourceMaps.map", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 85377, "lastModifiedTime": 1750683463834}}]}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default": {"hashValue": "e3ac545eaf0775e4d3f6c1970544eaa8", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "95293aaaf786cf45db6704e65f7d853b", "name": "arm64-v8a", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\arm64-v8a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bcbb5e7ec8be835eebf55ade0e3a01cf", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\arm64-v8a\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1260824, "lastModifiedTime": 978278400000}}, {"hashValue": "ba1b324795d31274943fa92600db5ef9", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\arm64-v8a\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 214768, "lastModifiedTime": 1750683454715}}]}, {"hashValue": "6c101b56b73a596ce8830550813115e3", "name": "armeabi-v7a", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\armeabi-v7a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "7a315e2f9a7b1223fa1be93f092b81cf", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\armeabi-v7a\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1109100, "lastModifiedTime": 978278400000}}, {"hashValue": "1e976b85cd0a345a553c5953b48a8473", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\armeabi-v7a\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 192296, "lastModifiedTime": 1750683454716}}]}, {"hashValue": "8a67f6b4ebfebd419de340e60d33651c", "name": "x86_64", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\x86_64", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c28a4178127d9b2514c6555ece74fb4c", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\x86_64\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1293080, "lastModifiedTime": 978278400000}}, {"hashValue": "b6ac738d9b3092f32ea3bb4128ac90c1", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default\\x86_64\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 204928, "lastModifiedTime": 1750683456215}}]}]}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default": {"hashValue": "e3ac545eaf0775e4d3f6c1970544eaa8", "name": "default", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "95293aaaf786cf45db6704e65f7d853b", "name": "arm64-v8a", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\arm64-v8a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bcbb5e7ec8be835eebf55ade0e3a01cf", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\arm64-v8a\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1260824, "lastModifiedTime": 978278400000}}, {"hashValue": "ba1b324795d31274943fa92600db5ef9", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\arm64-v8a\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 214768, "lastModifiedTime": 1750683454715}}]}, {"hashValue": "6c101b56b73a596ce8830550813115e3", "name": "armeabi-v7a", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\armeabi-v7a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "7a315e2f9a7b1223fa1be93f092b81cf", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\armeabi-v7a\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1109100, "lastModifiedTime": 978278400000}}, {"hashValue": "1e976b85cd0a345a553c5953b48a8473", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\armeabi-v7a\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 192296, "lastModifiedTime": 1750683454716}}]}, {"hashValue": "8a67f6b4ebfebd419de340e60d33651c", "name": "x86_64", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\x86_64", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c28a4178127d9b2514c6555ece74fb4c", "name": "libc++_shared.so", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\x86_64\\libc++_shared.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1293080, "lastModifiedTime": 978278400000}}, {"hashValue": "b6ac738d9b3092f32ea3bb4128ac90c1", "name": "libentry.so", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default\\x86_64\\libentry.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 204928, "lastModifiedTime": 1750683456215}}]}]}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\patch\\default\\base_native_libs.json": {"hashValue": "92673baea5aec94076fa0604cf39949c", "name": "base_native_libs.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\patch\\default\\base_native_libs.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2137, "lastModifiedTime": 1750683456408}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources": {"hashValue": "7c74b7971302f5ae70c88097521871b6", "name": "resources", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6dcaabd12ffee1d3c9a6d6bc203af712", "name": "base", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "627e3a54a0c7d5cee1e431efa84443a3", "name": "media", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0b478312ebe80ab6e93b97a4ce5c0448", "name": "app_icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\app_icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453840}}, {"hashValue": "6f84b90d6a6f23540cb85f54a38f45c5", "name": "camera_pause_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_pause_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10632, "lastModifiedTime": 1749022453864}}, {"hashValue": "387593473c128ab070274fbcc65a7bd4", "name": "camera_switch_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_switch_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6926, "lastModifiedTime": 1749022453864}}, {"hashValue": "cd9e2c9fa9a4a73403e54b0c86ddfa5a", "name": "camera_take_photo_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_take_photo_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14016, "lastModifiedTime": 1749022453865}}, {"hashValue": "69e1c567cd44f6edc7ee1108440ac0e0", "name": "camera_take_video_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_take_video_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 15819, "lastModifiedTime": 1749022453865}}, {"hashValue": "7789281643ea3f6b4a227a7e12b4d39a", "name": "camera_thumbnail_4x.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\camera_thumbnail_4x.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7152, "lastModifiedTime": 1749022453866}}, {"hashValue": "63b028c7353011ee8adfa63c3a967fb0", "name": "flash_always_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\flash_always_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1898, "lastModifiedTime": 1749022453866}}, {"hashValue": "90f460eaae837069a6b51717992eee7c", "name": "ic_camera_ic_camera_radio_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_ic_camera_radio_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 795, "lastModifiedTime": 1749022453867}}, {"hashValue": "93c1941a2022ca17176f3981b58d735b", "name": "ic_camera_pad.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_pad.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1073, "lastModifiedTime": 1749022453868}}, {"hashValue": "84ffec0a6189cfd2de43aaca147901d0", "name": "ic_camera_phone.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_phone.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1339, "lastModifiedTime": 1749022453868}}, {"hashValue": "3843a663a62b92f83897b2b627e5d8f0", "name": "ic_camera_progressBar_circle.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_progressBar_circle.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1091, "lastModifiedTime": 1749022453868}}, {"hashValue": "2b4559aff0a3e5a95b8ce3cf9c9f91c4", "name": "ic_camera_public_flash_auto_white.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_auto_white.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "8fe205be5fdbe2c82d370016417c2ff3", "name": "ic_camera_public_flash_auto.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_auto.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1794, "lastModifiedTime": 1749022453869}}, {"hashValue": "c863a453524f35a4d3371b7f3bc11897", "name": "ic_camera_public_flash_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2091, "lastModifiedTime": 1749022453870}}, {"hashValue": "8c5cbaa8ba0a62653b6cc2b11400b670", "name": "ic_camera_public_flash_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_public_flash_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1487, "lastModifiedTime": 1749022453870}}, {"hashValue": "7d2c5d39f46581e2607ec9c0a31e0543", "name": "ic_camera_radio_open.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_radio_open.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1762, "lastModifiedTime": 1749022453871}}, {"hashValue": "b3a59d24aa0a41c1bc430bbdb5e4d471", "name": "ic_camera_set__antishake.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set__antishake.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 5218, "lastModifiedTime": 1749022453871}}, {"hashValue": "da4cb2c6f607f8ac528071d4c0a30d16", "name": "ic_camera_set__mirror.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set__mirror.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1627, "lastModifiedTime": 1749022453871}}, {"hashValue": "d59ac73d4ec951681a0203852b9155e2", "name": "ic_camera_set_af.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_af.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 916, "lastModifiedTime": 1749022453872}}, {"hashValue": "d3a93fe8d0efec060a6005c202d3107c", "name": "ic_camera_set_arrow.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_arrow.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1422, "lastModifiedTime": 1749022453872}}, {"hashValue": "a61b4feb69e03c3d958607666710a04e", "name": "ic_camera_set_checked.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_checked.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1400, "lastModifiedTime": 1749022453873}}, {"hashValue": "5ac22f0067e1d99287875612e013f929", "name": "ic_camera_set_class.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_class.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1090, "lastModifiedTime": 1749022453873}}, {"hashValue": "e80ef7c03ad20ec772bbce88000cd2d8", "name": "ic_camera_set_exposure.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_exposure.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2866, "lastModifiedTime": 1749022453873}}, {"hashValue": "ecc027f6a0e0c3eae8748fbfad1d5e89", "name": "ic_camera_set_focus.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_focus.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 519, "lastModifiedTime": 1749022453874}}, {"hashValue": "fbd4ea5b70c71cd78d16d314a4d2ec35", "name": "ic_camera_set_format.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_format.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4020, "lastModifiedTime": 1749022453874}}, {"hashValue": "6b1c2f6b0b3d2df44e34c54ae2f6009f", "name": "ic_camera_set_line.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_line.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1402, "lastModifiedTime": 1749022453875}}, {"hashValue": "8cc5166d9dda3639bbbf727e06e86379", "name": "ic_camera_set_location.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_location.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2508, "lastModifiedTime": 1749022453875}}, {"hashValue": "3517f4d041c08a7472d14e1bbd53fc34", "name": "ic_camera_set_pic_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_pic_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453876}}, {"hashValue": "530b5f9e2e6345b7f1f7e755565fc9ca", "name": "ic_camera_set_quality.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_quality.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13855, "lastModifiedTime": 1749022453876}}, {"hashValue": "1f7f4dda7b345ec5840d5c5ceb86b8cc", "name": "ic_camera_set_video_rate.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_video_rate.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1235, "lastModifiedTime": 1749022453876}}, {"hashValue": "9f57de8ee52701eb4f8d4bd2f4fa06fc", "name": "ic_camera_set_video_resolution.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_set_video_resolution.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1485, "lastModifiedTime": 1749022453878}}, {"hashValue": "bef04f114418568acf0698a4597eb05a", "name": "ic_camera_switch_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_switch_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1354, "lastModifiedTime": 1749022453878}}, {"hashValue": "2f057ea2d566c1e0599014c2c5811837", "name": "ic_camera_switch_off2.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_switch_off2.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 974, "lastModifiedTime": 1749022453879}}, {"hashValue": "2cc3922cf58c19c1889223ea018b80db", "name": "ic_camera_video_close.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_close.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 441, "lastModifiedTime": 1749022453879}}, {"hashValue": "f5b428f1a8e5eedbd02a34e3fa7cb2b8", "name": "ic_camera_video_off.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_off.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1033, "lastModifiedTime": 1749022453879}}, {"hashValue": "58a400a0045d317e7adf29ae8c037190", "name": "ic_camera_video_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_camera_video_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 913, "lastModifiedTime": 1749022453880}}, {"hashValue": "b3fad9c09dce8787b994d81631d29879", "name": "ic_public_brightness_filled.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_public_brightness_filled.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2685, "lastModifiedTime": 1749022453881}}, {"hashValue": "9693142322f4eafa66f416622e0f0280", "name": "ic_public_brightness.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\ic_public_brightness.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2862, "lastModifiedTime": 1749022453880}}, {"hashValue": "d3b999e99f9c08f171ebe7ab09188297", "name": "icon_camera_setting_timer_on_balk.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer_on_balk.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453883}}, {"hashValue": "cb13e3152b25a046c7dd36edef7aa479", "name": "icon_camera_setting_timer_on.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer_on.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1715, "lastModifiedTime": 1749022453882}}, {"hashValue": "e9b0ad9dbf060377c7132d87af8c93b4", "name": "icon_camera_setting_timer.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting_timer.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3664, "lastModifiedTime": 1749022453882}}, {"hashValue": "f081cce0a4620543cff83f556e7aa7bd", "name": "icon_camera_setting.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon_camera_setting.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3573, "lastModifiedTime": 1749022453882}}, {"hashValue": "d1044eb267800fc10fb896e22a11dd41", "name": "icon.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\icon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6790, "lastModifiedTime": 1749022453881}}, {"hashValue": "be67575a938820711272da8fdfb4a840", "name": "pic_avatar_radio01.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_radio01.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11660, "lastModifiedTime": 1749022453883}}, {"hashValue": "58f3bcfc0169fd61ce206066c6f9794d", "name": "pic_avatar_radio02.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_radio02.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 12092, "lastModifiedTime": 1749022453884}}, {"hashValue": "a12dac4cf6f64a5967872296c6169422", "name": "pic_avatar_regular.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_avatar_regular.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14162, "lastModifiedTime": 1749022453884}}, {"hashValue": "e1277770a0627be49b7ee1ed38f0c410", "name": "pic_camera_line.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_camera_line.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 243206, "lastModifiedTime": 1749022453886}}, {"hashValue": "2f590cb280d1d441b49a19b1003b5bd6", "name": "pic_camera_mirror.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_camera_mirror.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 111725, "lastModifiedTime": 1749022453888}}, {"hashValue": "528da4db4b0c74b4ab1f781b58d0b90f", "name": "pic_popup_left.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_popup_left.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 486207, "lastModifiedTime": 1749022453890}}, {"hashValue": "55e8b4f62ca7ff5d41d4ebd02f96b6ec", "name": "pic_popup_right.png", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\pic_popup_right.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 424653, "lastModifiedTime": 1749022453892}}, {"hashValue": "854908744dd8a55925c5fa3bfe59b7e4", "name": "switch_camera.svg", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\media\\switch_camera.svg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2350, "lastModifiedTime": 1749022453892}}]}, {"hashValue": "1752577f66bd1a3d28dca9eee8d5043f", "name": "profile", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5ddc969a7aad41d4c4c22edd772abc8a", "name": "main_pages.json", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 42, "lastModifiedTime": 1749022453893}}]}]}]}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources.index": {"hashValue": "54b4bee4cb02671e32e9b63cf601dcc3", "name": "resources.index", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources.index", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8917, "lastModifiedTime": 1750683451770}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap": {"hashValue": "e43577c0c828d9c286daa6dd302f5c1e", "name": "entry-default-unsigned.hap", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6336864, "lastModifiedTime": 1750683465049}}, "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap": {"hashValue": "7a1ae483651e1708e5d37365b0d20911", "name": "entry-default-signed.hap", "path": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6430969, "lastModifiedTime": 1750683468561}}}