{":Camera:entry:default@PreBuild": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apiType\",\"_value\":\"stageMode\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"codeType\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":11,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":11,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceType\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSupportOhpmProj\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"profileModuleName\",\"_value\":\"entry\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"4.1.10.3\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetStatusCode\",\"_value\":1,\"_valueType\":\"number\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@PreBuild", "_key": ":Camera:entry:default@PreBuild", "_executionId": ":Camera:entry:default@PreBuild:1750683436088", "_inputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\app.json5", {"isDirectory": false, "fileSnapShotHashValue": "b272ae09c35f24ae3fab922faad24c82"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\module.json5", {"isDirectory": false, "fileSnapShotHashValue": "8a89638127253b3d548cd28f1b96f6c1"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\build-profile.json5", {"fileSnapShotHashValue": "e8693f92c7bebe783cb0e9adbf3a5eca"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build-profile.json5", {"fileSnapShotHashValue": "a6f543b6c877af84066d4050145db38e"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", {"fileSnapShotHashValue": "5ddc969a7aad41d4c4c22edd772abc8a"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\oh-package.json5", {"fileSnapShotHashValue": "dfebcc02ee1adc714cb3f7a31f3f6448"}]]}, "_outputFiles": {"dataType": "Map", "value": []}}, ":Camera:entry:default@CreateBuildProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildMode\",\"_value\":\"Debug\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@CreateBuildProfile", "_key": ":Camera:entry:default@CreateBuildProfile", "_executionId": ":Camera:entry:default@CreateBuildProfile:1750683451150", "_inputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\app.json5", {"fileSnapShotHashValue": "b272ae09c35f24ae3fab922faad24c82"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\build-profile.json5", {"fileSnapShotHashValue": "e8693f92c7bebe783cb0e9adbf3a5eca"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "d14098e12dfb2a2349baf81fee547974"}]]}}, ":Camera:entry:default@GenerateLoaderJson": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"anBuildMode\",\"_value\":\"type\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apPath\",\"_value\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\modules.ap\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"fallbackAnBuild\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"harNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"modulePathMap\",\"_value\":\"{\\\"entry\\\":\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\baseCamera\\\\\\\\Camera\\\\\\\\entry\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"nodeModulesPath\",\"_value\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\node_modules\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectRootPath\",\"_value\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetConfig\",\"_value\":\"{\\\"name\\\":\\\"default\\\",\\\"runtimeOS\\\":\\\"OpenHarmony\\\"}\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@GenerateLoaderJson", "_key": ":Camera:entry:default@GenerateLoaderJson", "_executionId": ":Camera:entry:default@GenerateLoaderJson:1750683451169", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", {"isDirectory": false, "fileSnapShotHashValue": "ea9919101057b94d436f3efc4e6aae98"}]]}}, ":Camera:entry:default@MergeProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"asanEnable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildRoot\",\"_value\":\"build\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":11,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isDebug\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarModule\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"multiProjects\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetSdkVersion\",\"_value\":11,\"_valueType\":\"number\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@MergeProfile", "_key": ":Camera:entry:default@MergeProfile", "_executionId": ":Camera:entry:default@MergeProfile:1750683451182", "_inputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\app.json5", {"fileSnapShotHashValue": "b272ae09c35f24ae3fab922faad24c82"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\build-profile.json5", {"fileSnapShotHashValue": "e8693f92c7bebe783cb0e9adbf3a5eca"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "8a89638127253b3d548cd28f1b96f6c1"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "afd40d91a2660538cb1f63fa76967329"}]]}}, ":Camera:entry:default@ProcessProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkEnable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"dependency\",\"_value\":\"[]\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@ProcessProfile", "_key": ":Camera:entry:default@ProcessProfile", "_executionId": ":Camera:entry:default@ProcessProfile:1750683451228", "_inputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "afd40d91a2660538cb1f63fa76967329"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json", {"fileSnapShotHashValue": "547ab8b5fe932f0ede90b3c50b9a7140"}]]}}, ":Camera:entry:default@PreviewProcessResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"previewCompileResourcesCommand\",\"_value\":\"{\\\"commandList\\\":[\\\"D:\\\\\\\\harmonyFor\\\\\\\\openSDK\\\\\\\\11\\\\\\\\toolchains\\\\\\\\restool.exe\\\",\\\"-m\\\",\\\"entry\\\",\\\"-f\\\",\\\"-j\\\",\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\plantDemos\\\\\\\\Camera\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\process_profile\\\\\\\\default\\\\\\\\module.json\\\",\\\"-p\\\",\\\"com.samples.camera\\\",\\\"-o\\\",\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\plantDemos\\\\\\\\Camera\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\",\\\"-r\\\",\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\plantDemos\\\\\\\\Camera\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\generated\\\\\\\\r\\\\\\\\default\\\\\\\\ResourceTable.h\\\"],\\\"inputFiles\\\":{\\\"_group\\\":{}},\\\"outputFiles\\\":{\\\"_group\\\":{}},\\\"existsResourceDir\\\":true,\\\"restoolLinkParamObj\\\":{\\\"appScopeResources\\\":\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\plantDemos\\\\\\\\Camera\\\\\\\\AppScope\\\\\\\\resources\\\",\\\"moduleResourcesMap\\\":{},\\\"harResourcesMap\\\":{},\\\"outputPath\\\":\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\plantDemos\\\\\\\\Camera\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\",\\\"linkCommand\\\":[\\\"D:\\\\\\\\harmonyFor\\\\\\\\openSDK\\\\\\\\11\\\\\\\\toolchains\\\\\\\\restool.exe\\\",\\\"-m\\\",\\\"entry\\\",\\\"-f\\\",\\\"-j\\\",\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\plantDemos\\\\\\\\Camera\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\process_profile\\\\\\\\default\\\\\\\\module.json\\\",\\\"-p\\\",\\\"com.samples.camera\\\",\\\"-r\\\",\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\plantDemos\\\\\\\\Camera\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\generated\\\\\\\\r\\\\\\\\default\\\\\\\\ResourceTable.h\\\",\\\"-z\\\",\\\"--ids\\\",\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\plantDemos\\\\\\\\Camera\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ids_map\\\",\\\"--defined-ids\\\",\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\plantDemos\\\\\\\\Camera\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ids_map\\\\\\\\id_defined.json\\\"],\\\"idsMapPath\\\":\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\plantDemos\\\\\\\\Camera\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ids_map\\\"}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@PreviewProcessResource", "_key": ":Camera:entry:default@PreviewProcessResource", "_executionId": ":Camera:entry:default@PreviewProcessResource:1749088489031", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["{\"commandList\":[\"D:\\\\harmonyFor\\\\openSDK\\\\11\\\\toolchains\\\\restool.exe\",\"-m\",\"entry\",\"-f\",\"-j\",\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json\",\"-p\",\"com.samples.camera\",\"-o\",\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\",\"-r\",\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h\"],\"inputFiles\":{\"_group\":{}},\"outputFiles\":{\"_group\":{}},\"existsResourceDir\":true,\"restoolLinkParamObj\":{\"appScopeResources\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\AppScope\\\\resources\",\"moduleResourcesMap\":{},\"harResourcesMap\":{},\"outputPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\",\"linkCommand\":[\"D:\\\\harmonyFor\\\\openSDK\\\\11\\\\toolchains\\\\restool.exe\",\"-m\",\"entry\",\"-f\",\"-j\",\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json\",\"-p\",\"com.samples.camera\",\"-r\",\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h\",\"-z\",\"--ids\",\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\",\"--defined-ids\",\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json\"],\"idsMapPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\"}}", {"isDirectory": false, "fileSnapShotHashValue": ""}]]}}, ":Camera:entry:default@PreviewCompileResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"PREVIEWER_REPLACE_PAGE\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"PREVIEWER_REPLACE_SRCPATH\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"TARGET_CONFIG\",\"_value\":\"{\\\"name\\\":\\\"default\\\",\\\"runtimeOS\\\":\\\"OpenHarmony\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"D:\\\\harmonyFor\\\\openSDK\\\\11\\\\toolchains\\\\restool.exe\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@PreviewCompileResource", "_key": ":Camera:entry:default@PreviewCompileResource", "_executionId": ":Camera:entry:default@PreviewCompileResource:1749088489044", "_inputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\resources", {"fileSnapShotHashValue": "31cbb6f14ce0b66d6da5b0c3c2b0f1f8"}], ["D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "d3b5238702f6297fda22b46f7b382117"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default", {"isDirectory": true, "fileSnapShotHashValue": "fcd7eda9c7575d23c809c1a6b1212a94"}], ["D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\generated\\r\\default", {"isDirectory": true, "fileSnapShotHashValue": "6838303a38187532ed235ac13335945d"}]]}}, ":Camera:entry:default@PreviewUpdateAssets": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@PreviewUpdateAssets", "_key": ":Camera:entry:default@PreviewUpdateAssets", "_executionId": ":Camera:entry:default@PreviewUpdateAssets:1749088489458", "_inputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\config\\buildConfig.json", {"isDirectory": false, "fileSnapShotHashValue": "fb0836601e0e0cba6a282404152bcc1c"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", {"isDirectory": false, "fileSnapShotHashValue": "1e2da8770f6341edc3e77fab2ca62655"}], ["D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", {"isDirectory": false, "fileSnapShotHashValue": "e3b8f3c482b1fc54071fa2ed0d229d6b"}]]}}, ":Camera:entry:default@PreviewArkTS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkTsWdiget\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"OBFUSCATION_ENABLE\",\"_value\":\"undefined\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@PreviewArkTS", "_key": ":Camera:entry:default@PreviewArkTS", "_executionId": ":Camera:entry:default@PreviewArkTS:1749088489476", "_inputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\loader\\default", {"isDirectory": true, "fileSnapShotHashValue": "9363a83a27fea780cb88b313bbaac6ac"}], ["D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", {"fileSnapShotHashValue": "7da17948d3901ed9b5b8c8b6368b42e8"}], ["D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", {"fileSnapShotHashValue": "1e2da8770f6341edc3e77fab2ca62655"}], ["D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", {"isDirectory": true, "fileSnapShotHashValue": "4982958e641819be87a264e525a6e604"}], ["D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\src\\main\\ets", {"isDirectory": true, "fileSnapShotHashValue": "e975d13e36b82c96027e38615371a226"}], ["D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "9abe1f2dd5998dac8f16afca5c1784bb"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":Camera:entry:default@GenerateMetadata": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"allRemoteHspPathList\",\"_value\":[],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"entryModules\",\"_value\":[],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"existMaterial\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"existSigningConfig\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleDeviceType\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"product\",\"_value\":\"default\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"properties.enableSignTask\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"4.1.10.3\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_keyAlias\",\"_value\":\"debugKey\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_keyPassword\",\"_value\":\"0000001B2757CA7CCC65F317C7F09EA6979E0FCA80AA2053DD35DEC957F0365A517F577DE6506C8531D412\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_name\",\"_value\":\"default\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_signAlg\",\"_value\":\"SHA256withECDSA\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_storePassword\",\"_value\":\"0000001BF6C6DB4400328BFB5E2823AF6A1116CFEFDEA89C3E7FE26AF9EBE9C2DFFC3FD37E218940A8355C\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_type\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetConfig\",\"_value\":\"{\\\"name\\\":\\\"default\\\",\\\"runtimeOS\\\":\\\"OpenHarmony\\\"}\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@GenerateMetadata", "_key": ":Camera:entry:default@GenerateMetadata", "_executionId": ":Camera:entry:default@GenerateMetadata:1750683451123", "_inputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "8a89638127253b3d548cd28f1b96f6c1"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json", {"fileSnapShotHashValue": "02a996a9476e07f52e44efd9850c8cb0"}]]}}, ":Camera:entry:default@MakePackInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":11,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileSdkVersion\",\"_value\":11,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@MakePackInfo", "_key": ":Camera:entry:default@MakePackInfo", "_executionId": ":Camera:entry:default@MakePackInfo:1750683451196", "_inputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\app.json5", {"fileSnapShotHashValue": "b272ae09c35f24ae3fab922faad24c82"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "8a89638127253b3d548cd28f1b96f6c1"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\build-profile.json5", {"fileSnapShotHashValue": "e8693f92c7bebe783cb0e9adbf3a5eca"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\pack.info", {"fileSnapShotHashValue": "6e7a1a0faf3529cb6cd7b080dcef0932"}]]}}, ":Camera:entry:default@ProcessResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"resConfigJsonContent\",\"_value\":\"{\\\"configPath\\\":\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\baseCamera\\\\\\\\Camera\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\process_profile\\\\\\\\default\\\\\\\\module.json\\\",\\\"packageName\\\":\\\"com.samples.camera\\\",\\\"output\\\":\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\baseCamera\\\\\\\\Camera\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\",\\\"moduleNames\\\":\\\"entry\\\",\\\"ResourceTable\\\":[\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\baseCamera\\\\\\\\Camera\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\generated\\\\\\\\r\\\\\\\\default\\\\\\\\ResourceTable.h\\\"],\\\"applicationResource\\\":\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\baseCamera\\\\\\\\Camera\\\\\\\\AppScope\\\\\\\\resources\\\",\\\"moduleResources\\\":[\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\baseCamera\\\\\\\\Camera\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\resources\\\"],\\\"dependencies\\\":[],\\\"iconCheck\\\":false,\\\"ids\\\":\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\baseCamera\\\\\\\\Camera\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ids_map\\\",\\\"definedIds\\\":\\\"D:\\\\\\\\harmonyforwork\\\\\\\\aboutCamera\\\\\\\\baseCamera\\\\\\\\Camera\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ids_map\\\\\\\\id_defined.json\\\"}\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@ProcessResource", "_key": ":Camera:entry:default@ProcessResource", "_executionId": ":Camera:entry:default@ProcessResource:1750683451456", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", {"isDirectory": false, "fileSnapShotHashValue": "8c6a4094341a0c3457e86425d766b988"}]]}}, ":Camera:entry:default@CompileResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"D:\\\\harmonyFor\\\\openSDK\\\\11\\\\toolchains\\\\restool.exe,-l,D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resConfig.json\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"D:\\\\harmonyFor\\\\openSDK\\\\11\\\\toolchains\\\\restool.exe\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"TARGET_CONFIG\",\"_value\":\"{\\\"name\\\":\\\"default\\\",\\\"runtimeOS\\\":\\\"OpenHarmony\\\"}\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@CompileResource", "_key": ":Camera:entry:default@CompileResource", "_executionId": ":Camera:entry:default@CompileResource:1750683451469", "_inputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources", {"fileSnapShotHashValue": "31cbb6f14ce0b66d6da5b0c3c2b0f1f8"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\resources", {"fileSnapShotHashValue": "7bc75c4a9d7c24ed0ab0acdf64a9bd55"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json", {"isDirectory": false, "fileSnapShotHashValue": "547ab8b5fe932f0ede90b3c50b9a7140"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", {"isDirectory": false, "fileSnapShotHashValue": "8c6a4094341a0c3457e86425d766b988"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default", {"isDirectory": true, "fileSnapShotHashValue": "0ffb317b78902637a08b28a2ce7d1994"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h", {"isDirectory": false, "fileSnapShotHashValue": "776b8e7dbe07334aac26169dddbf9587"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\r\\default", {"isDirectory": true, "fileSnapShotHashValue": "cd559e2e8dc0c1749df7f1d36504b66f"}]]}}, ":Camera:entry:default@CompileArkTS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"OBFUSCATION_ENABLE\",\"_value\":\"undefined\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkTsWdiget\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@CompileArkTS", "_key": ":Camera:entry:default@CompileArkTS", "_executionId": ":Camera:entry:default@CompileArkTS:1750683451798", "_inputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader\\default", {"isDirectory": true, "fileSnapShotHashValue": "b50fb4ed41f6e87d1731c7c6647b484a"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", {"fileSnapShotHashValue": "dc9543cd8278b6dd4797acf9e7da5301"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\module.json", {"fileSnapShotHashValue": "bf5258b70313088ea547a01d6b5e1853"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", {"isDirectory": true, "fileSnapShotHashValue": "1752577f66bd1a3d28dca9eee8d5043f"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets", {"isDirectory": true, "fileSnapShotHashValue": "e975d13e36b82c96027e38615371a226"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "d14098e12dfb2a2349baf81fee547974"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\ets", {"isDirectory": true, "fileSnapShotHashValue": "ad40df43eb6914f0cefb9ef847c97efc"}]]}}, ":Camera:entry:default@BuildJS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@BuildJS", "_key": ":Camera:entry:default@BuildJS", "_executionId": ":Camera:entry:default@BuildJS:1750683530427", "_inputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader\\default", {"isDirectory": true, "fileSnapShotHashValue": "b50fb4ed41f6e87d1731c7c6647b484a"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", {"fileSnapShotHashValue": "dc9543cd8278b6dd4797acf9e7da5301"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\module.json", {"fileSnapShotHashValue": "bf5258b70313088ea547a01d6b5e1853"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", {"isDirectory": true, "fileSnapShotHashValue": "1752577f66bd1a3d28dca9eee8d5043f"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\js", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":Camera:entry:default@ProcessLibs": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arm64-v8a\\\\libc++_shared.so\",\"_value\":\"daaf5f2428baa6d36cb60edd01bb085f\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arm64-v8a\\\\libentry.so\",\"_value\":\"471810f524975114048d600d6678d17b\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"armeabi-v7a\\\\libc++_shared.so\",\"_value\":\"38d86f2d58e96719b063aba2783fb15d\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"armeabi-v7a\\\\libentry.so\",\"_value\":\"e8607b609975902831003677b5b6b992\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"x86_64\\\\libc++_shared.so\",\"_value\":\"60f1eca271e4c51060f396d69c57f2b8\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"x86_64\\\\libentry.so\",\"_value\":\"e38767002d0ba96251121c2ce0ac8bbf\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@ProcessLibs", "_key": ":Camera:entry:default@ProcessLibs", "_executionId": ":Camera:entry:default@ProcessLibs:1750683456279", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default", {"isDirectory": true, "fileSnapShotHashValue": "e3ac545eaf0775e4d3f6c1970544eaa8"}]]}}, ":Camera:entry:default@DoNativeStrip": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@DoNativeStrip", "_key": ":Camera:entry:default@DoNativeStrip", "_executionId": ":Camera:entry:default@DoNativeStrip:1750683456322", "_inputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default", {"isDirectory": true, "fileSnapShotHashValue": "e3ac545eaf0775e4d3f6c1970544eaa8"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default", {"isDirectory": true, "fileSnapShotHashValue": "e3ac545eaf0775e4d3f6c1970544eaa8"}]]}}, ":Camera:entry:default@CacheNativeLibs": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@CacheNativeLibs", "_key": ":Camera:entry:default@CacheNativeLibs", "_executionId": ":Camera:entry:default@CacheNativeLibs:1750683456345", "_inputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default", {"isDirectory": true, "fileSnapShotHashValue": "e3ac545eaf0775e4d3f6c1970544eaa8"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default", {"isDirectory": true, "fileSnapShotHashValue": "e3ac545eaf0775e4d3f6c1970544eaa8"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\patch\\default\\base_native_libs.json", {"isDirectory": false, "fileSnapShotHashValue": "92673baea5aec94076fa0604cf39949c"}]]}}, ":Camera:entry:default@PackageHap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"java,-Dfile.encoding=utf-8,-jar,D:\\\\harmonyFor\\\\openSDK\\\\11\\\\toolchains\\\\lib\\\\app_packing_tool.jar,--mode,hap,--force,true,--lib-path,D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default,--json-path,D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\module.json,--resources-path,D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources,--index-path,D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index,--pack-info-path,D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info,--out-path,D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap,--ets-path,D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"D:\\\\harmonyFor\\\\openSDK\\\\11\\\\toolchains\\\\lib\\\\app_packing_tool.jar\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hotReload\",\"_value\":false,\"_valueType\":\"boolean\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@PackageHap", "_key": ":Camera:entry:default@PackageHap", "_executionId": ":Camera:entry:default@PackageHap:1750683463982", "_inputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default", {"isDirectory": false, "fileSnapShotHashValue": "e3ac545eaf0775e4d3f6c1970544eaa8"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\module.json", {"isDirectory": false, "fileSnapShotHashValue": "bf5258b70313088ea547a01d6b5e1853"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources", {"isDirectory": false, "fileSnapShotHashValue": "7c74b7971302f5ae70c88097521871b6"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources.index", {"isDirectory": false, "fileSnapShotHashValue": "54b4bee4cb02671e32e9b63cf601dcc3"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\pack.info", {"isDirectory": false, "fileSnapShotHashValue": "6e7a1a0faf3529cb6cd7b080dcef0932"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\ets", {"isDirectory": false, "fileSnapShotHashValue": "ad40df43eb6914f0cefb9ef847c97efc"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "d14098e12dfb2a2349baf81fee547974"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap", {"isDirectory": false, "fileSnapShotHashValue": "e43577c0c828d9c286daa6dd302f5c1e"}]]}}, ":Camera:entry:default@SignHap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"existMaterial\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"existSigningConfig\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"properties.enableSignTask\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"4.1.10.3\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_keyAlias\",\"_value\":\"debugKey\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_keyPassword\",\"_value\":\"0000001B2757CA7CCC65F317C7F09EA6979E0FCA80AA2053DD35DEC957F0365A517F577DE6506C8531D412\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_name\",\"_value\":\"default\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_signAlg\",\"_value\":\"SHA256withECDSA\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_storePassword\",\"_value\":\"0000001BF6C6DB4400328BFB5E2823AF6A1116CFEFDEA89C3E7FE26AF9EBE9C2DFFC3FD37E218940A8355C\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_type\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Camera", "_moduleName": "entry", "_taskName": "default@SignHap", "_key": ":Camera:entry:default@SignHap", "_executionId": ":Camera:entry:default@SignHap:1750683465077", "_inputFiles": {"dataType": "Map", "value": [["C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.cer", {"isDirectory": false, "fileSnapShotHashValue": "0ef1eb36b070f41cfe25bf0771e7a6eb"}], ["C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.p7b", {"isDirectory": false, "fileSnapShotHashValue": "b0e807b170dfb621afc8dc41c1da3122"}], ["C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.p12", {"isDirectory": false, "fileSnapShotHashValue": "2a536c636f6190c40adfedadfc94d7c5"}], ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap", {"isDirectory": false, "fileSnapShotHashValue": "e43577c0c828d9c286daa6dd302f5c1e"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap", {"isDirectory": false, "fileSnapShotHashValue": "7a1ae483651e1708e5d37365b0d20911"}]]}}}