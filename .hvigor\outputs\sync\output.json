{"ohos-module-entry": {"SELECT_TARGET": "default", "MODULE_BUILD_DIR": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build", "TARGETS": {"default": {"SOURCE_ROOT": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main", "RESOURCES_PATH": ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources"], "BUILD_PATH": {"OUTPUT_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default", "INTERMEDIA_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates", "JS_ASSETS_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default", "JS_LITE_ASSETS_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader_out_lite\\default", "RES_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default", "RES_PROFILE_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "ETS_SUPER_VISUAL_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\cache\\default\\default@CompileArkTS\\esmodule", "JS_SUPER_VISUAL_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\cache\\default\\default@CompileJS\\jsbundle", "WORKER_LOADER": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", "MANIFEST_JSON": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\manifest\\default", "OUTPUT_METADATA_JSON": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json"}, "BUILD_OPTION": {"debuggable": true}}, "ohosTest": {"SOURCE_ROOT": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\ohosTest", "RESOURCES_PATH": ["D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\ohosTest\\resources"], "BUILD_PATH": {"OUTPUT_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\ohosTest", "INTERMEDIA_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates", "JS_ASSETS_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader_out\\ohosTest", "JS_LITE_ASSETS_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader_out_lite\\ohosTest", "RES_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\ohosTest", "RES_PROFILE_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\ohosTest\\resources\\base\\profile", "ETS_SUPER_VISUAL_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\cache\\ohosTest\\ohosTest@OhosTestCompileArkTS\\esmodule", "JS_SUPER_VISUAL_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\cache\\ohosTest\\ohosTest@OhosTestCompileJS\\jsbundle", "WORKER_LOADER": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader\\ohosTest\\loader.json", "MANIFEST_JSON": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\manifest\\ohosTest", "OUTPUT_METADATA_JSON": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\hap_metadata\\ohosTest\\output_metadata.json"}, "BUILD_OPTION": {"debuggable": true}}}}, "ohos-project": {"SELECT_PRODUCT_NAME": "default", "MODULE_BUILD_DIR": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\build", "BUNDLE_NAME": "com.samples.camera", "BUILD_PATH": {"OUTPUT_PATH": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\build\\outputs\\default"}}, "version": 1}