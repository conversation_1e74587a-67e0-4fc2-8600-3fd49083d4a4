{"version": "1.0", "events": [{"head": {"id": "1cc2943d-27f7-4711-952a-ba57cd0ef07c", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 176956881700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d5e54aa-c31e-457d-8408-ca2d7ba3efa6", "name": "worker[1] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 176967464800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77e3e399-d22f-42f9-a173-503fca675aa9", "name": "worker[0] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 176967961700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aac390a-b861-43a9-9ac5-859ff6972010", "name": "hvigor disconnect:  transport close", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 176969868200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69983fd3-5637-4da0-87c0-09693bc9188d", "name": "hvigor daemon: Socket will be closed. socketId=C8_C7Z9zmR93HjnJAAAB, connectionCount=0", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 176971754800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c875592c-31e9-46ee-8141-0449a6c7c927", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":11852,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"2a4f0f5de49f06431b08222813af151a0701879e\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45001,\"lastUsedTime\":1749169587924,\"createdBy\":\"deveco\",\"sessionId\":\"0000005008b22e0b63b254e7e7985f798b2284cc64396a1c142e557cab70f0e1add71365d6c987668e0a0c6a87149e4394049ac63deaa0f88962e546e0442e70d2ee2798eead5a6d584b1ac6c3dfef1f329bd3409a0ce5c6a59b263fb12a0497\"}", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 176973218800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78317810-a027-4f2e-832c-1a9e8b24cda6", "name": "hvigor daemon: Socket is connected. socketId=KaV1eOFRN-S190tYAAAD, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178562576000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d11d30bc-109f-48b6-bf70-0b439347d01f", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"a545278c8650069115c2463ed05b18ff5921bf33\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\MyApplication3\",\"nodeVersion\":\"v16.20.2\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":6292,\"state\":\"idle\",\"lastUsedTime\":1749029204366,\"info\":null,\"createdBy\":\"deveco\",\"sessionId\":\"00000050fc26eea18dce4c9ba4ac184a1dfd6bae64c42d5b69726cd96ec1eca0e78f6a8d3cf30634de77c16fc4b610b74e63868986aa2a5371cf87f944b7ae0282bb709d43a05b313c5755c6355ba772371945f5b6878cd14ddf3f9ff8565876\"},{\"keyId\":\"355217a8cdd3d4f7d530d97277eea88eae5cc7a1\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45002,\"pid\":10236,\"state\":\"idle\",\"lastUsedTime\":1749169582109,\"createdBy\":\"deveco\",\"sessionId\":\"00000050755b0171d6599281a1e853c4be313cfc29104b23394b96884e0d828c6337f4e715b2f36f9682eb175d334eb7941c5c933953ca77247780aa737775a494c69d68c17130f7564c2676bc2fb62a896b091a45311d707e4e77e902b59040\"},{\"keyId\":\"9463dfc03c4fec9c3c3a06bd6064b90e6effde2c\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45001,\"pid\":4880,\"state\":\"broken\",\"lastUsedTime\":1749121809340,\"info\":\"The process with pid 4880 does not exist.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050b6acf27c62e357be133cc98efce73cf8bef6f99170c87039d655379841e455c5788be675374ed8e890c0e0eac1357c660cf4bf7d56940a510566b3225c44b7e10df5d8bd62aa770577cd0f69c9af0aa2bc219c1180603436e22eba68\"},{\"pid\":11852,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"2a4f0f5de49f06431b08222813af151a0701879e\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45001,\"lastUsedTime\":1749169587957,\"createdBy\":\"deveco\",\"sessionId\":\"0000005008b22e0b63b254e7e7985f798b2284cc64396a1c142e557cab70f0e1add71365d6c987668e0a0c6a87149e4394049ac63deaa0f88962e546e0442e70d2ee2798eead5a6d584b1ac6c3dfef1f329bd3409a0ce5c6a59b263fb12a0497\"}]", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178563962600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06c7b5be-9b33-44b9-bb0c-38978c2e3e12", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":11852,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"2a4f0f5de49f06431b08222813af151a0701879e\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45001,\"lastUsedTime\":1749169587957,\"createdBy\":\"deveco\",\"sessionId\":\"0000005008b22e0b63b254e7e7985f798b2284cc64396a1c142e557cab70f0e1add71365d6c987668e0a0c6a87149e4394049ac63deaa0f88962e546e0442e70d2ee2798eead5a6d584b1ac6c3dfef1f329bd3409a0ce5c6a59b263fb12a0497\"}", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178565152200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1be8f633-b22c-4aa4-8a5b-bad21dc1891c", "name": "set active socket. socketId=KaV1eOFRN-S190tYAAAD", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178574259000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96753a22-2286-4688-8d36-0a096dcfbb9c", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":11852,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"2a4f0f5de49f06431b08222813af151a0701879e\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45001,\"lastUsedTime\":1749169589549,\"createdBy\":\"deveco\",\"sessionId\":\"0000005008b22e0b63b254e7e7985f798b2284cc64396a1c142e557cab70f0e1add71365d6c987668e0a0c6a87149e4394049ac63deaa0f88962e546e0442e70d2ee2798eead5a6d584b1ac6c3dfef1f329bd3409a0ce5c6a59b263fb12a0497\"}", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178575481700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b99a4868-ccaa-4d0b-9775-1395a9486826", "name": "hvigor daemon: Receive data from client. data={\n  mode: 'module',\n  parallel: true,\n  prop: [ 'module=entry', 'product=default' ],\n  incremental: true,\n  _: [ 'compileNative' ],\n  daemon: true\n}.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178578946000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da8b3f0b-b693-49e7-b5e2-652b2d68bfd6", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178580008600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "670dfc49-8e24-429a-a43d-ebdcc2d450f3", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":11852,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"2a4f0f5de49f06431b08222813af151a0701879e\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45001,\"lastUsedTime\":1749169589559,\"createdBy\":\"deveco\",\"sessionId\":\"0000005008b22e0b63b254e7e7985f798b2284cc64396a1c142e557cab70f0e1add71365d6c987668e0a0c6a87149e4394049ac63deaa0f88962e546e0442e70d2ee2798eead5a6d584b1ac6c3dfef1f329bd3409a0ce5c6a59b263fb12a0497\"}", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178580903500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "034b0d7f-7e2a-40ce-9b86-f1f5e7b1c5e5", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178596960800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc4878b1-4ef0-4112-879d-472e8478d656", "name": "Cache service initialization finished in 20 ms ", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178616757000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cccc6204-82f8-44e7-b202-b4c734be67b4", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178626407200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6f54f6a-d4ed-48d0-b803-dd79de57ca67", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178641050800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4275365f-5801-49b3-99ee-6345b3a751ff", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178641116700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e30638f1-297a-4815-8c53-2646532a6f98", "name": "hvigorfile, resolve finished D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178651169800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a2ebfce-3bc1-441d-8765-df25085d0f84", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178654665100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c404d021-f44e-4695-bf76-8a0903d2dbf2", "name": "hvigorfile, require result:  { hapTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178665228000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e693ef2-3e7b-409b-b019-69aaaeb547c7", "name": "hvigorfile, binding system plugins { hapTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178665279300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e52f4d1-62c5-4926-9866-93838262a7e2", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178677195000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8f449be-9626-4722-ac2f-a2ee88f3f412", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178677227600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dd5e5da-25ac-48c2-bb0f-579f6bc7b95e", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178681485400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "508ba853-f1d5-48da-9098-88be33e178a7", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178681581900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c9e22d6-74b3-4760-bdd7-8d9ede4118df", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178681831500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4880719-0316-43ea-893c-09aac29a58f9", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178682251100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d90cf5e-2047-4b24-a883-53c38903a110", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178682272200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25921bc7-7db6-4c84-9e4b-9c03ddf04936", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178682279800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8aec188-f0f6-430d-b61b-114325f2da0f", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178682307700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1749266-105a-473b-9278-676d7a7f1c5d", "name": "require SDK: toolchains,ArkTS,native; moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178683768800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88463c99-7870-42b3-9bbc-e532a25a6b16", "name": "Module entry task initialization takes 14 ms ", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178701587400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c29f5ff-11af-46db-b21b-3609de1c3da1", "name": "hvigorfile, resolve finished D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178701646800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b94027af-5ca9-4c0f-af7c-e932aa1e5edc", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178701961700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc435aaa-cb2b-4910-962f-9a2af6d9c60e", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178702001800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efb169c8-d5aa-47c2-84f4-e478e03531c5", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178702061300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bb0c305-6d52-427a-9ddb-3b9ba8001161", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178702073500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b85e3250-b81c-494d-af18-51a6e554917d", "name": "Mo<PERSON>le Camera Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178702978800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d10c3fb2-2c60-4c7d-b0f9-809aa7098ac8", "name": "Module Camera's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178703007300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fcf099b-b791-4ba4-9dba-9b6bf76dfc84", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178705495700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d4269f5-14d9-4743-8bd9-32afa74ad660", "name": "Sdk init in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178718794900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f68eafa6-76e2-4eae-ad88-07890b3cb720", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178803871800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dc864e1-cdde-431a-97f8-1a8a356665c5", "name": "Project task initialization takes 94 ms ", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178812276600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b879937e-6d9b-4505-8ef9-d06075c82aa4", "name": "Sdk init in 15 ms ", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178826762800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f17b522d-bebc-458f-b96d-59daf8b93956", "name": "Configuration phase cost:213 ms ", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178829472500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c0d7130-31ce-4315-91e0-aee7ce003f03", "name": "Configuration task cost before running: 236 ms ", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178830624900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2943e99b-21b2-4be5-aa37-172cb56d93ec", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178844111000, "endTime": 178858395000}, "additional": {"children": [], "state": "success", "detailId": "710c88dd-36e1-4fa1-b5fd-3f9f9e3cfabb", "logId": "20e6e55b-1b5c-4f62-94a6-f162bed6a2c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "710c88dd-36e1-4fa1-b5fd-3f9f9e3cfabb", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178833487000}, "additional": {"logType": "detail", "children": [], "durationId": "2943e99b-21b2-4be5-aa37-172cb56d93ec"}}, {"head": {"id": "53a23aba-bc35-4f5d-96d7-66e4bf906ac2", "name": "entry : default@PreBuild start {\n  rss: 191885312,\n  heapTotal: 125689856,\n  heapUsed: 104737912,\n  external: 1571058,\n  arrayBuffers: 605702\n}", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178844071100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7385394-3fa5-4093-9601-166c94c1eea2", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178844129600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04cb3d90-e67e-4292-b093-d525bbd37c67", "name": "Incremental task entry:default@PreBuild pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178858115200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbd317db-b3e5-44af-8d12-c6d35b61a8be", "name": "entry : default@PreBuild end {\n  rss: 191004672,\n  heapTotal: 125689856,\n  heapUsed: 104925144,\n  external: 1571058,\n  arrayBuffers: 605702\n}", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178858279100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20e6e55b-1b5c-4f62-94a6-f162bed6a2c9", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178844111000, "endTime": 178858395000}, "additional": {"logType": "info", "children": [], "durationId": "2943e99b-21b2-4be5-aa37-172cb56d93ec"}}, {"head": {"id": "6f848b87-36f8-4b2b-a264-79bcb3f76000", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178876977200, "endTime": 181575175200}, "additional": {"children": ["eefa5909-9d06-40b8-a6cf-88fb619e4ec4", "e7522a62-dbba-48a5-bce6-35ca3314008b", "4e567656-9f2d-478f-a921-fb26c681022a"], "state": "success", "detailId": "37aa0755-5f81-41b4-83de-bb97d06847f7", "logId": "8e6fd326-9087-4d4b-b855-7580e9b6b97c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "37aa0755-5f81-41b4-83de-bb97d06847f7", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178865154700}, "additional": {"logType": "detail", "children": [], "durationId": "6f848b87-36f8-4b2b-a264-79bcb3f76000"}}, {"head": {"id": "292e49af-ac16-4987-b2cd-ebaafdc88084", "name": "entry : default@BuildNativeWithCmake start {\n  rss: 192757760,\n  heapTotal: 125689856,\n  heapUsed: 105161376,\n  external: 1571058,\n  arrayBuffers: 605702\n}", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178876927000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03509da3-8063-4635-b425-261cb588d1db", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178877001700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56b66096-038b-4d9d-ba3b-48c2d93e62ae", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\arm64-v8a',\n  '-DOHOS_ARCH=arm64-v8a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\arm64-v8a',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=arm64-v8a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178885771200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d173c1f-c802-44ab-9cae-16d1b140e093", "name": "default@BuildNativeWithCmake work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178888431700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eefa5909-9d06-40b8-a6cf-88fb619e4ec4", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 11852, "tid": "Worker0", "startTime": 179011701700, "endTime": 181017470400}, "additional": {"children": [], "state": "success", "parent": "6f848b87-36f8-4b2b-a264-79bcb3f76000", "logId": "6bcf734d-d0f3-4d7c-9c41-3987495c056c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "36a70848-1715-4107-86e7-dcff912bca15", "name": "default@BuildNativeWithCmake work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178890126900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d547dc2f-8399-448c-b46c-1b86996b9186", "name": "default@BuildNativeWithCmake work[0] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178890218600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa493b04-e192-45b9-b522-4d178f345f43", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\armeabi-v7a',\n  '-DOHOS_ARCH=armeabi-v7a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\armeabi-v7a',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=armeabi-v7a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178894581200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff3a7b74-8abe-4b60-9d82-af4a7c0011b4", "name": "default@BuildNativeWithCmake work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178896500900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7522a62-dbba-48a5-bce6-35ca3314008b", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 11852, "tid": "Worker1", "startTime": 181373000000, "endTime": 181574928900}, "additional": {"children": [], "state": "success", "parent": "6f848b87-36f8-4b2b-a264-79bcb3f76000", "logId": "6d36be1c-235e-4140-ab73-2adeb9b47271"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "acb2301b-e205-48fc-a213-a9cc04a16ca3", "name": "default@BuildNativeWithCmake work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178897235700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25bc036b-5b27-42d4-b68b-f32012cc39b4", "name": "default@BuildNativeWithCmake work[1] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178897286500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7db4245e-03ed-44b1-b44c-a016a194f421", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\x86_64',\n  '-DOHOS_ARCH=x86_64',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\x86_64',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=x86_64',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178902451200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4e03c10-c16b-419f-a725-b718d40cb2c4", "name": "default@BuildNativeWithCmake work[2] is submitted.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178904026900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e567656-9f2d-478f-a921-fb26c681022a", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 11852, "tid": "Worker1", "startTime": 179336331900, "endTime": 181267205000}, "additional": {"children": [], "state": "success", "parent": "6f848b87-36f8-4b2b-a264-79bcb3f76000", "logId": "893e5f47-3867-434a-a448-c199014e10f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "66f867b8-46b9-4715-88d1-17e043733c68", "name": "default@BuildNativeWithCmake work[2] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178904786200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eab44f5-f2ad-482e-94c2-2dc3f932667e", "name": "default@BuildNativeWithCmake work[2] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178904828900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20a31cf0-9f48-4db7-90f3-42ac59c76dee", "name": "entry : default@BuildNativeWithCmake end {\n  rss: 194850816,\n  heapTotal: 125689856,\n  heapUsed: 105827824,\n  external: 1571058,\n  arrayBuffers: 605702\n}", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178904945700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75c78e88-6dba-4987-808d-50e0b940be2a", "name": "default@BuildNativeWithCmake work[0] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 179011779300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1845c6d-633d-4c06-86d5-5377959268b0", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 179336170900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a82c18f7-4b97-4de5-bf1c-542dde3d20c4", "name": "default@BuildNativeWithCmake work[2] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 179336353900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2377c66-9786-45ec-8982-d7e8af6f061b", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 180032741600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78cddb3b-bfa1-4811-b33a-d530c9c7134d", "name": "A work dispatched to worker[1] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 180032774900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b08bb237-51b3-4c9a-9908-add71382257e", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 180032789100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dec0e40-04ce-4c14-8dc8-9cdd2fa9ea66", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 180032800100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51cfca4c-85e1-40e0-9f36-38e460f30c4a", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 180032810200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dee2ae52-94c6-4789-b90d-b34878a55a60", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 180032825200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f984a23c-375f-48ee-bc41-87e6616d2a3e", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 180032840000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26561533-05c6-496d-a2ee-1791081aa872", "name": "default@BuildNativeWithCmake work[0] done.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 181017670900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bcf734d-d0f3-4d7c-9c41-3987495c056c", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 11852, "tid": "Worker0", "startTime": 179011701700, "endTime": 181017470400}, "additional": {"logType": "info", "children": [], "durationId": "eefa5909-9d06-40b8-a6cf-88fb619e4ec4", "parent": "8e6fd326-9087-4d4b-b855-7580e9b6b97c"}}, {"head": {"id": "24fd19e4-7a36-47bc-989c-7cf8173802d0", "name": "default@BuildNativeWithCmake work[2] done.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 181267249700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "893e5f47-3867-434a-a448-c199014e10f5", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 11852, "tid": "Worker1", "startTime": 179336331900, "endTime": 181267205000}, "additional": {"logType": "info", "children": [], "durationId": "4e567656-9f2d-478f-a921-fb26c681022a", "parent": "8e6fd326-9087-4d4b-b855-7580e9b6b97c"}}, {"head": {"id": "c926ea1e-0333-4c3f-8699-f5e15bc8e2a1", "name": "default@BuildNativeWithCmake work[1] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 181373037800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de61e939-52ae-4b2b-a356-34d334aef159", "name": "default@BuildNativeWithCmake work[1] done.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 181574983300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d36be1c-235e-4140-ab73-2adeb9b47271", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 11852, "tid": "Worker1", "startTime": 181373000000, "endTime": 181574928900}, "additional": {"logType": "info", "children": [], "durationId": "e7522a62-dbba-48a5-bce6-35ca3314008b", "parent": "8e6fd326-9087-4d4b-b855-7580e9b6b97c"}}, {"head": {"id": "8e6fd326-9087-4d4b-b855-7580e9b6b97c", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178876977200, "endTime": 181575175200}, "additional": {"logType": "info", "children": ["6bcf734d-d0f3-4d7c-9c41-3987495c056c", "6d36be1c-235e-4140-ab73-2adeb9b47271", "893e5f47-3867-434a-a448-c199014e10f5"], "durationId": "6f848b87-36f8-4b2b-a264-79bcb3f76000"}}, {"head": {"id": "5f089b4b-583b-48c0-b673-4a70f568c848", "name": "entry:compileNative", "description": "Compile the hook task for native resources.", "type": "duration"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 181579033400, "endTime": 181579295200}, "additional": {"children": [], "state": "success", "detailId": "66e05240-4844-4a31-ac13-51e02c04451a", "logId": "e83287a5-9037-4b4b-a59b-7582a0b32794"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "66e05240-4844-4a31-ac13-51e02c04451a", "name": "create entry:compileNative task", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 181578819300}, "additional": {"logType": "detail", "children": [], "durationId": "5f089b4b-583b-48c0-b673-4a70f568c848"}}, {"head": {"id": "786ac894-133c-4c41-8d71-12530360a243", "name": "entry : compileNative start {\n  rss: 292732928,\n  heapTotal: 126763008,\n  heapUsed: 94206464,\n  external: 1571058,\n  arrayBuffers: 71696\n}", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 181578998400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb669bd2-a2fc-4f79-9999-4f723c0e0140", "name": "Executing task :entry:compileNative", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 181579055600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff80850a-6138-4eeb-9158-dd6c88e140bb", "name": "entry : compileNative end {\n  rss: 292732928,\n  heapTotal: 126763008,\n  heapUsed: 94216056,\n  external: 1571058,\n  arrayBuffers: 71696\n}", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 181579263600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e83287a5-9037-4b4b-a59b-7582a0b32794", "name": "Finished :entry:compileNative", "description": "Compile the hook task for native resources.", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 181579033400, "endTime": 181579295200}, "additional": {"logType": "info", "children": [], "durationId": "5f089b4b-583b-48c0-b673-4a70f568c848"}}, {"head": {"id": "fa16f6c0-f544-4ea0-b999-3d2eb2c328e0", "name": "BUILD SUCCESSFUL in 2 s 986 ms ", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 181580244000}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "bde46273-df4f-43d7-b701-dc077ab5461a", "name": "compileNative", "description": "", "type": "mark"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 178594721400, "endTime": 181581131900}, "additional": {"time": {"year": 2025, "month": 6, "day": 6, "hour": 8, "minute": 26}, "markType": "history", "category": "build", "state": "success"}}, {"head": {"id": "314f68c1-a551-4e3d-b19e-5f01265912f0", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11852, "tid": "Main Thread", "startTime": 181581457000}, "additional": {"logType": "debug", "children": []}}], "workLog": []}