{"version": "1.0", "events": [{"head": {"id": "454ad35c-b69a-4097-affe-628015ba2216", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 146806152700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f2aeb64-3fc7-439e-9bcf-3cae7fb22bc8", "name": "hvigor disconnect:  client namespace disconnect", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 146809363700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38c14cf8-7149-43e6-b620-0640cfe96d0d", "name": "hvigor daemon: Socket will be closed. socketId=QVAVhBncOOObfBktAAAB, connectionCount=0", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 146810733000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b08acacb-7bbd-4e53-9a4f-865048dc2d77", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2492,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"e46c659e366bd1f2744e9e73bee7d7f2471c6256\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45002,\"lastUsedTime\":1749189995784,\"createdBy\":\"deveco\",\"sessionId\":\"000000505800223cc3611f8ea223b7036b5d7d3bfb8c3a2a3567bdc136f1f6b6e1dcfb7142303423c3707873dac08c5f845ab945da94b11274fc2bf38d3a2c8abbf1f4621143416022e24c2b0611960de2541ba8025a98beea9cbe7cdb88b6b4\"}", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 146812364300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de2b8bab-197a-4e60-b030-583ce2a81137", "name": "worker[0] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 146821504600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61a02c0d-fdf0-4a48-98ca-4be07f98e37b", "name": "worker[1] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 146827796200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25ea4ecb-1f50-4a46-8677-6477135bc1d9", "name": "hvigor daemon: Socket is connected. socketId=VUus6EU2t5AUghKVAAAD, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149280059300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9758b49a-8486-4e04-8141-d995abc3f661", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"a545278c8650069115c2463ed05b18ff5921bf33\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\MyApplication3\",\"nodeVersion\":\"v16.20.2\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":6292,\"state\":\"idle\",\"lastUsedTime\":1749029204366,\"info\":null,\"createdBy\":\"deveco\",\"sessionId\":\"00000050fc26eea18dce4c9ba4ac184a1dfd6bae64c42d5b69726cd96ec1eca0e78f6a8d3cf30634de77c16fc4b610b74e63868986aa2a5371cf87f944b7ae0282bb709d43a05b313c5755c6355ba772371945f5b6878cd14ddf3f9ff8565876\"},{\"keyId\":\"355217a8cdd3d4f7d530d97277eea88eae5cc7a1\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45002,\"pid\":10236,\"state\":\"broken\",\"lastUsedTime\":1749177668459,\"info\":\"The process with pid 10236 does not exist.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050755b0171d6599281a1e853c4be313cfc29104b23394b96884e0d828c6337f4e715b2f36f9682eb175d334eb7941c5c933953ca77247780aa737775a494c69d68c17130f7564c2676bc2fb62a896b091a45311d707e4e77e902b59040\"},{\"keyId\":\"9463dfc03c4fec9c3c3a06bd6064b90e6effde2c\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45001,\"pid\":4880,\"state\":\"broken\",\"lastUsedTime\":1749121809340,\"info\":\"The process with pid 4880 does not exist.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050b6acf27c62e357be133cc98efce73cf8bef6f99170c87039d655379841e455c5788be675374ed8e890c0e0eac1357c660cf4bf7d56940a510566b3225c44b7e10df5d8bd62aa770577cd0f69c9af0aa2bc219c1180603436e22eba68\"},{\"keyId\":\"2a4f0f5de49f06431b08222813af151a0701879e\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45001,\"pid\":11852,\"state\":\"stopped\",\"lastUsedTime\":1749180393801,\"info\":\"stopped by exit event with code: -1\",\"createdBy\":\"deveco\",\"sessionId\":\"0000005008b22e0b63b254e7e7985f798b2284cc64396a1c142e557cab70f0e1add71365d6c987668e0a0c6a87149e4394049ac63deaa0f88962e546e0442e70d2ee2798eead5a6d584b1ac6c3dfef1f329bd3409a0ce5c6a59b263fb12a0497\"},{\"pid\":13712,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera_js\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"472ca034a924d7773eabb171b356743a6116a108\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45001,\"lastUsedTime\":1749189991099,\"createdBy\":\"deveco\",\"sessionId\":\"00000050042400a14f609affad4389fe7d380cd775cee98119e7af8a7ec06f226f5889a0d903bd8bdc23e51e15472fd40a0a697bd66021780efb521b0f923d9af0d55a11578ce827db0d6637815bcbb674754576783257b0bea5df13c1a9c6cd\"},{\"pid\":2492,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"e46c659e366bd1f2744e9e73bee7d7f2471c6256\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45002,\"lastUsedTime\":1749189995797,\"createdBy\":\"deveco\",\"sessionId\":\"000000505800223cc3611f8ea223b7036b5d7d3bfb8c3a2a3567bdc136f1f6b6e1dcfb7142303423c3707873dac08c5f845ab945da94b11274fc2bf38d3a2c8abbf1f4621143416022e24c2b0611960de2541ba8025a98beea9cbe7cdb88b6b4\"}]", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149281428100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9e2011a-de17-4142-a553-6b40245fd12c", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2492,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"e46c659e366bd1f2744e9e73bee7d7f2471c6256\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45002,\"lastUsedTime\":1749189995797,\"createdBy\":\"deveco\",\"sessionId\":\"000000505800223cc3611f8ea223b7036b5d7d3bfb8c3a2a3567bdc136f1f6b6e1dcfb7142303423c3707873dac08c5f845ab945da94b11274fc2bf38d3a2c8abbf1f4621143416022e24c2b0611960de2541ba8025a98beea9cbe7cdb88b6b4\"}", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149282481000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6719caa-fb16-4169-8e48-34ed3cf90d83", "name": "set active socket. socketId=VUus6EU2t5AUghKVAAAD", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149291040500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4758fd64-b7c0-484e-b455-5fabd52bcb6c", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2492,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"e46c659e366bd1f2744e9e73bee7d7f2471c6256\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45002,\"lastUsedTime\":1749189998267,\"createdBy\":\"deveco\",\"sessionId\":\"000000505800223cc3611f8ea223b7036b5d7d3bfb8c3a2a3567bdc136f1f6b6e1dcfb7142303423c3707873dac08c5f845ab945da94b11274fc2bf38d3a2c8abbf1f4621143416022e24c2b0611960de2541ba8025a98beea9cbe7cdb88b6b4\"}", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149292120100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a7803f7-c64a-46a2-8101-c8dd2b757714", "name": "hvigor daemon: Receive data from client. data={\n  mode: 'module',\n  parallel: true,\n  prop: [ 'module=entry', 'product=default' ],\n  incremental: true,\n  _: [ 'compileNative' ],\n  daemon: true\n}.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149295462400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3a5a059-7496-4665-960b-e213b664f48f", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149296055300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b22d5a0-09b3-41bd-af9d-4c410962dc44", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2492,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"e46c659e366bd1f2744e9e73bee7d7f2471c6256\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45002,\"lastUsedTime\":1749189998277,\"createdBy\":\"deveco\",\"sessionId\":\"000000505800223cc3611f8ea223b7036b5d7d3bfb8c3a2a3567bdc136f1f6b6e1dcfb7142303423c3707873dac08c5f845ab945da94b11274fc2bf38d3a2c8abbf1f4621143416022e24c2b0611960de2541ba8025a98beea9cbe7cdb88b6b4\"}", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149296868400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29df21c0-2da9-4692-a7dc-2992df7f0676", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149301993600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eafdcec9-d6fc-4ce3-b9c5-299b92755575", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149308880500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02fa0ef7-e08f-4ab3-b7bc-19c6a551ff83", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149325060000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65faa9f0-eadf-4204-b9a1-4adedf476131", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149337371000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "612c7cdc-b80f-4ac0-b5cd-fee1679c7c22", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149337434000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be34e07d-75fe-4288-a61e-0877f131ce95", "name": "hvigorfile, resolve finished D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149346871100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "072ca6eb-b24e-4a89-ac71-8d9bc687db09", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149351025700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f70ecafd-1e1e-425e-b9c7-343c7317138a", "name": "hvigorfile, require result:  { hapTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149397316700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07397212-cbca-45ed-83da-aab6b0798c99", "name": "hvigorfile, binding system plugins { hapTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149397373400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfab6865-db0a-42b2-b1da-85968d26240c", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149409944000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79266338-ce76-4216-863e-183eb4b45a8d", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149410030600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5e9d0a7-b270-4f94-bffa-da8f8447444e", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149416300300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81accdc5-0be1-4201-90cd-7ecbd9bd8117", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149416360000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "167b818d-e56a-4e14-8dde-3d0aaaef9804", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149416645600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63720d67-3043-438d-942a-20838ab9e500", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149417102200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a18f71b7-118c-4438-b8ef-5c8ff063de3c", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149417124600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b3ce7ee-facd-4c53-9725-7e9de94e4f40", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149417134000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d03ad3b-4d00-4819-9f24-d93d75bc0fc9", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149417163900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e8b0959-c416-4e07-ab80-9c19eb4171ac", "name": "require SDK: toolchains,ArkTS,native; moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149418658000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fe24c02-ed01-4a2e-96e9-2c6712327924", "name": "Module entry task initialization takes 6 ms ", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149441831500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c650b83f-1fbe-4c36-a6e2-a504f46ddc46", "name": "hvigorfile, resolve finished D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149441877000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb2624a4-b230-49bb-8a3b-fbe9971bca71", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149441938600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5e0ecc6-c155-4d8e-94e9-170c76ed194e", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149441955700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f622e5d-e832-43b5-b788-97527e6ad809", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149441996400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "925d6e14-4466-4032-93ba-abb1c9516fba", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149442005500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "484cf626-25fb-45e0-a1fa-3d9366e565a1", "name": "Mo<PERSON>le Camera Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149442718200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1af2e23-2c66-4209-9c1f-b83678f011a9", "name": "Module Camera's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149442735900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1e7b383-1d6d-4bf0-b184-<PERSON><PERSON>2297dd", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149467196100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cf4afbc-7aef-42e0-8a49-6261bdf301f0", "name": "Sdk init in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149475486900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e5d50bd-29c5-413e-a686-01e1fb098661", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149523871700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f3d0ca1-104a-439c-974c-9be2fe4ee687", "name": "Project task initialization takes 55 ms ", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149530629600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acf0d869-711a-4879-ab0b-08df661ec761", "name": "Sdk init in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149536763700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f827a2dd-6048-4451-8026-c0ec607d8717", "name": "Configuration phase cost:231 ms ", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149539436200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9da4dea-1a1a-4a59-9e3b-a85d64e3e8c6", "name": "Configuration task cost before running: 240 ms ", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149540623500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0cb876-8efc-40e3-ab9a-cdc92fd368bc", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149584738600, "endTime": 149599841100}, "additional": {"children": [], "state": "success", "detailId": "75862aa4-1ccf-47a5-8322-501673f4539d", "logId": "827ba74f-be3d-487e-8467-ed5ead769b69"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "75862aa4-1ccf-47a5-8322-501673f4539d", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149543243500}, "additional": {"logType": "detail", "children": [], "durationId": "0d0cb876-8efc-40e3-ab9a-cdc92fd368bc"}}, {"head": {"id": "a2e746d8-1c7c-4bc5-8646-40cd77f34fdf", "name": "entry : default@PreBuild start {\n  rss: 195592192,\n  heapTotal: 125689856,\n  heapUsed: 104764120,\n  external: 1582157,\n  arrayBuffers: 616801\n}", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149584694800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31c75b76-6143-4931-bc0c-0f087382f76f", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149584758000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c7340b1-69fa-457d-a9ae-1649674f3f32", "name": "Incremental task entry:default@PreBuild pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149599572000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab816233-9e23-4b3d-87d0-04d19cb15fad", "name": "entry : default@PreBuild end {\n  rss: 195694592,\n  heapTotal: 125689856,\n  heapUsed: 104949424,\n  external: 1590349,\n  arrayBuffers: 624993\n}", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149599723100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "827ba74f-be3d-487e-8467-ed5ead769b69", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149584738600, "endTime": 149599841100}, "additional": {"logType": "info", "children": [], "durationId": "0d0cb876-8efc-40e3-ab9a-cdc92fd368bc"}}, {"head": {"id": "775a629f-388a-4a15-8f5f-bed6b28b392d", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149605161600, "endTime": 152576072100}, "additional": {"children": ["c8f25c41-a4ce-4f8b-866c-60b6fdbaa85d", "d51b5877-1110-4c1a-bbc3-8800313b2792", "8a60c3b3-870b-40ee-bcca-e2041798a91c"], "state": "success", "detailId": "c6b854a1-9bba-4908-bb2b-dd4d698a18c8", "logId": "bfc6fd03-e644-4121-98a3-768f577ed6f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "c6b854a1-9bba-4908-bb2b-dd4d698a18c8", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149603881900}, "additional": {"logType": "detail", "children": [], "durationId": "775a629f-388a-4a15-8f5f-bed6b28b392d"}}, {"head": {"id": "62f92624-47e6-44c4-b254-1cd523db720e", "name": "entry : default@BuildNativeWithCmake start {\n  rss: 196345856,\n  heapTotal: 125689856,\n  heapUsed: 105185664,\n  external: 1590349,\n  arrayBuffers: 624993\n}", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149605131500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "802ca182-6d28-4eff-b55f-b7bbf4ad7677", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149605182100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7478d363-016a-459e-a974-ef97bb807d81", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\arm64-v8a',\n  '-DOHOS_ARCH=arm64-v8a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\arm64-v8a',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=arm64-v8a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149614179800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "396b37b4-35eb-4795-867f-6248c1f81127", "name": "default@BuildNativeWithCmake work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149616878300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8f25c41-a4ce-4f8b-866c-60b6fdbaa85d", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 2492, "tid": "Worker0", "startTime": 149768621500, "endTime": 151979198500}, "additional": {"children": [], "state": "success", "parent": "775a629f-388a-4a15-8f5f-bed6b28b392d", "logId": "eeb54c76-6ed8-4ae4-b424-175f923483d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "1fe6776c-c125-4785-a8f5-673a78ff6072", "name": "default@BuildNativeWithCmake work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149618443900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73927421-b61d-40aa-ab59-798cb620a27c", "name": "default@BuildNativeWithCmake work[0] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149618524100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a1cf587-364b-40c9-a220-a7d92c31a00e", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\armeabi-v7a',\n  '-DOHOS_ARCH=armeabi-v7a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\armeabi-v7a',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=armeabi-v7a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149622492400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eafe5433-b713-4374-9f67-59fbc3342929", "name": "default@BuildNativeWithCmake work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149624613800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d51b5877-1110-4c1a-bbc3-8800313b2792", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 2492, "tid": "Worker0", "startTime": 152308830500, "endTime": 152575925400}, "additional": {"children": [], "state": "success", "parent": "775a629f-388a-4a15-8f5f-bed6b28b392d", "logId": "913380aa-e0d8-4287-aeb8-c1505c2923a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "1ac81eef-3c36-4cad-87ac-09365810ef01", "name": "default@BuildNativeWithCmake work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149625422800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9e29c07-d613-4cd3-8b3e-08cf6369a2a3", "name": "default@BuildNativeWithCmake work[1] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149625539400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5408592c-114b-4cfb-aa04-70cb0d9a73f9", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\x86_64',\n  '-DOHOS_ARCH=x86_64',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\x86_64',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=x86_64',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149653467500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9056903a-dc7f-44f4-bc13-3a50433abf19", "name": "default@BuildNativeWithCmake work[2] is submitted.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149654955200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a60c3b3-870b-40ee-bcca-e2041798a91c", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 2492, "tid": "Worker1", "startTime": 150102154600, "endTime": 152146101500}, "additional": {"children": [], "state": "success", "parent": "775a629f-388a-4a15-8f5f-bed6b28b392d", "logId": "916caa4c-0328-4343-97a1-bc3d5c7b66bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "e6fb1b2e-af97-4a81-8e04-ade29b1bff7a", "name": "default@BuildNativeWithCmake work[2] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149655693200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "226a6d62-86e7-4a6f-9fe7-228e2c16e980", "name": "default@BuildNativeWithCmake work[2] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149655731300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "182a7601-60ef-48d2-bb63-b4d224fdfb09", "name": "entry : default@BuildNativeWithCmake end {\n  rss: 196526080,\n  heapTotal: 125689856,\n  heapUsed: 105852032,\n  external: 1590349,\n  arrayBuffers: 624993\n}", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149655823800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a83bf9e-25a7-4cde-9c74-8745de655ab9", "name": "default@BuildNativeWithCmake work[0] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149768712200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5664705-7403-4b49-9e66-fae239c1aa68", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 150101992300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc976386-d5d9-4430-bbd2-ec5de40cd685", "name": "default@BuildNativeWithCmake work[2] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 150102174900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10f8dd1a-3d3e-4dc2-b15d-60e855554285", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 150876970200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f134fb6-4c96-48fa-82b3-fb940e02d977", "name": "A work dispatched to worker[1] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 150877002500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "788a2833-692d-4dfd-83ff-c870b561b880", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 150877018200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a0a9058-2fb2-4263-8d43-28d3e99b4092", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 150877028900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a9575de-be95-4435-9d7a-0d507c646905", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 150877038400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d688bd8-db14-4205-b76b-a52e594e4b52", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 150877053000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7a2aed9-813b-4d7c-8d51-4b2fb78f1754", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 150877067300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "556f5a5d-672f-45a7-8acb-587c7082523f", "name": "default@BuildNativeWithCmake work[0] done.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 151979427700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeb54c76-6ed8-4ae4-b424-175f923483d1", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 2492, "tid": "Worker0", "startTime": 149768621500, "endTime": 151979198500}, "additional": {"logType": "info", "children": [], "durationId": "c8f25c41-a4ce-4f8b-866c-60b6fdbaa85d", "parent": "bfc6fd03-e644-4121-98a3-768f577ed6f2"}}, {"head": {"id": "1b7e5f29-19f6-4521-a32b-721ef9ecd141", "name": "default@BuildNativeWithCmake work[2] done.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 152146203100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "916caa4c-0328-4343-97a1-bc3d5c7b66bf", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 2492, "tid": "Worker1", "startTime": 150102154600, "endTime": 152146101500}, "additional": {"logType": "info", "children": [], "durationId": "8a60c3b3-870b-40ee-bcca-e2041798a91c", "parent": "bfc6fd03-e644-4121-98a3-768f577ed6f2"}}, {"head": {"id": "c2580c55-694f-4a4c-b631-58c87fff4adc", "name": "default@BuildNativeWithCmake work[1] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 152308886300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "935991f4-8afa-49c7-aca8-40d12b2f695d", "name": "default@BuildNativeWithCmake work[1] done.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 152575977500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "913380aa-e0d8-4287-aeb8-c1505c2923a3", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 2492, "tid": "Worker0", "startTime": 152308830500, "endTime": 152575925400}, "additional": {"logType": "info", "children": [], "durationId": "d51b5877-1110-4c1a-bbc3-8800313b2792", "parent": "bfc6fd03-e644-4121-98a3-768f577ed6f2"}}, {"head": {"id": "bfc6fd03-e644-4121-98a3-768f577ed6f2", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149605161600, "endTime": 152576072100}, "additional": {"logType": "info", "children": ["eeb54c76-6ed8-4ae4-b424-175f923483d1", "913380aa-e0d8-4287-aeb8-c1505c2923a3", "916caa4c-0328-4343-97a1-bc3d5c7b66bf"], "durationId": "775a629f-388a-4a15-8f5f-bed6b28b392d"}}, {"head": {"id": "1ef25154-4c0e-4cad-aec9-8db277b8cc56", "name": "entry:compileNative", "description": "Compile the hook task for native resources.", "type": "duration"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 152585202800, "endTime": 152585750900}, "additional": {"children": [], "state": "success", "detailId": "f608efb6-8b6e-47b3-a125-78139d5c516e", "logId": "94333dcf-1427-42c9-a0d5-c6e5e37d8b28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "f608efb6-8b6e-47b3-a125-78139d5c516e", "name": "create entry:compileNative task", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 152584897400}, "additional": {"logType": "detail", "children": [], "durationId": "1ef25154-4c0e-4cad-aec9-8db277b8cc56"}}, {"head": {"id": "1827627c-7bfd-440e-b0f6-3b74fb2057b2", "name": "entry : compileNative start {\n  rss: 292720640,\n  heapTotal: 126238720,\n  heapUsed: 93969336,\n  external: 1590349,\n  arrayBuffers: 71704\n}", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 152585154300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bce8e793-6f9e-4578-b4b1-889aff5b2388", "name": "Executing task :entry:compileNative", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 152585496300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49c7ebb1-2af5-4f9e-bf02-a65184d5c85b", "name": "entry : compileNative end {\n  rss: 292728832,\n  heapTotal: 126238720,\n  heapUsed: 93978928,\n  external: 1590349,\n  arrayBuffers: 71704\n}", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 152585723100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94333dcf-1427-42c9-a0d5-c6e5e37d8b28", "name": "Finished :entry:compileNative", "description": "Compile the hook task for native resources.", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 152585202800, "endTime": 152585750900}, "additional": {"logType": "info", "children": [], "durationId": "1ef25154-4c0e-4cad-aec9-8db277b8cc56"}}, {"head": {"id": "d0c8e27d-e892-4caf-b006-0492896111c5", "name": "BUILD SUCCESSFUL in 3 s 286 ms ", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 152586464700}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "3575acec-17b0-43cf-974f-e617e9536202", "name": "compileNative", "description": "", "type": "mark"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 149300837800, "endTime": 152587425300}, "additional": {"time": {"year": 2025, "month": 6, "day": 6, "hour": 14, "minute": 6}, "markType": "history", "category": "build", "state": "success"}}, {"head": {"id": "20f06f6a-5852-4f99-a72c-d3ba3012a608", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 2492, "tid": "Main Thread", "startTime": 152587753700}, "additional": {"logType": "debug", "children": []}}], "workLog": []}