{"version": "1.0", "events": [{"head": {"id": "153065f9-79f8-4854-971e-413e2abba26e", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883027450700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7799294c-b3f3-4d57-8ca8-ad43eb1a9320", "name": "hvigor disconnect:  client namespace disconnect", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883030431700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5e3cb56-9043-47dd-ae1d-1d488426867c", "name": "hvigor daemon: Socket will be closed. socketId=vDk9jpXraJ0I1DM4AAAB, connectionCount=0", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883031626300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09335ffd-e05b-479d-b339-6a284e847409", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":13320,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"6affa5e8c485fb6924f7ddf1a995f403282ad928\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45002,\"lastUsedTime\":1749543258005,\"createdBy\":\"deveco\",\"sessionId\":\"00000050ca07df05da60d6fec9ca2e9ea2fa8af6b39378e2453d9ed4718d0f67e2c905fedce31ad831a66eac581d39bb9ceb2ecaa6ae5eac7d72c1feafd3345ecf5dcc534b50986dcd76f6b5840d78312aef6d76dc2094f9aafcdda7648164fa\"}", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883032583200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11403808-69ec-4e30-a02b-89db1488c955", "name": "worker[1] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883038036900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14f964d1-318f-4d4a-9edb-dd708919dca6", "name": "worker[0] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883038285100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bdd384e-ac8b-47c3-8f54-c6d8e2576d42", "name": "hvigor daemon: Socket is connected. socketId=VwEkMJ1mu7SxyVmvAAAD, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883734932800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e30f53ab-fea1-4b2e-b785-15e1b57f7e1f", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"a545278c8650069115c2463ed05b18ff5921bf33\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\MyApplication3\",\"nodeVersion\":\"v16.20.2\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":6292,\"state\":\"idle\",\"lastUsedTime\":1749029204366,\"info\":null,\"createdBy\":\"deveco\",\"sessionId\":\"00000050fc26eea18dce4c9ba4ac184a1dfd6bae64c42d5b69726cd96ec1eca0e78f6a8d3cf30634de77c16fc4b610b74e63868986aa2a5371cf87f944b7ae0282bb709d43a05b313c5755c6355ba772371945f5b6878cd14ddf3f9ff8565876\"},{\"keyId\":\"be6ff80c0012323f0a9e307fc294b858c4f419aa\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45001,\"pid\":18812,\"state\":\"idle\",\"lastUsedTime\":1749543194403,\"createdBy\":\"deveco\",\"sessionId\":\"0000005050db6ab1e9598944357d07499b716a2b67cab352d3db8b8a4a419d34af92450cb83b99e573981cd21cc9bb5e97717915455989d5102a6a83d4acd60114f53cbf2d2aa5bc6cf11f25704151176f3807054656461250b09515260b4cd9\"},{\"keyId\":\"cfa61f58304889589cb7907b92da6d267ca7748f\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\fileManager\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45001,\"pid\":17332,\"state\":\"stopped\",\"lastUsedTime\":1749528151457,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"000000504936484c8fd94b213c826d4b3963c50c84e7bdbca3ddf44a5eadbfc36a45ea109396e2b5e3911a6c94840fb23cfb308d8ca45b4f7264f775a0b6bfe58ca6359c1f81f47ad77e0c6aea72402757405b25b37b84f67d35057717905637\"},{\"keyId\":\"0563bb86252a9df17385bcc95b4701bc0a95c35b\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\fileManagerNew\",\"nodeVersion\":\"v16.20.2\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45002,\"pid\":5844,\"state\":\"stopped\",\"lastUsedTime\":1749528244942,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050da48fac7f7ca35129bea256e5a747cca2e353626e6099dee4174dfb3c4b280579c2f14c23af76ce94e2d39a3e9f128d440212c4a3de04427886a3f8e9ad3b1ef5820075174ab346e4aeb93a49f532c014e999add27427d8ce87e7e17\"},{\"pid\":17820,\"state\":\"stopped\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\fileManagerNew\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"4be656988a522eed855ed6bbe943664c8ba928d5\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v16.20.2\",\"port\":45002,\"lastUsedTime\":1749542684121,\"createdBy\":\"deveco\",\"sessionId\":\"0000005008f595e94a622831244646cd31b0e315d1d859339cd763193fc0fbf589e10235e7e3420b4991204105750cddca1342da59ed206255cf85af1e6121f20fa4e978f3611c79b5094d13240f034ee54b208f70c82e356ab3f6b9fb005d8e\",\"info\":\"stopped by exit event with code: -1\"},{\"pid\":13320,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"6affa5e8c485fb6924f7ddf1a995f403282ad928\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45002,\"lastUsedTime\":1749543258016,\"createdBy\":\"deveco\",\"sessionId\":\"00000050ca07df05da60d6fec9ca2e9ea2fa8af6b39378e2453d9ed4718d0f67e2c905fedce31ad831a66eac581d39bb9ceb2ecaa6ae5eac7d72c1feafd3345ecf5dcc534b50986dcd76f6b5840d78312aef6d76dc2094f9aafcdda7648164fa\"}]", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883736393500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b0c0bc-2b0e-4f99-81b3-2a588ba8668d", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":13320,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"6affa5e8c485fb6924f7ddf1a995f403282ad928\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45002,\"lastUsedTime\":1749543258016,\"createdBy\":\"deveco\",\"sessionId\":\"00000050ca07df05da60d6fec9ca2e9ea2fa8af6b39378e2453d9ed4718d0f67e2c905fedce31ad831a66eac581d39bb9ceb2ecaa6ae5eac7d72c1feafd3345ecf5dcc534b50986dcd76f6b5840d78312aef6d76dc2094f9aafcdda7648164fa\"}", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883737429100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4091d5d2-1494-4cb1-93cc-dbbc2b74c9ea", "name": "set active socket. socketId=VwEkMJ1mu7SxyVmvAAAD", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883742694500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d40621a0-4d7c-4c54-a050-e088ec99e0ce", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":13320,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"6affa5e8c485fb6924f7ddf1a995f403282ad928\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45002,\"lastUsedTime\":1749543258721,\"createdBy\":\"deveco\",\"sessionId\":\"00000050ca07df05da60d6fec9ca2e9ea2fa8af6b39378e2453d9ed4718d0f67e2c905fedce31ad831a66eac581d39bb9ceb2ecaa6ae5eac7d72c1feafd3345ecf5dcc534b50986dcd76f6b5840d78312aef6d76dc2094f9aafcdda7648164fa\"}", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883743494800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe410b4e-68ea-4b1b-8b4c-86aeb3dd9648", "name": "hvigor daemon: Receive data from client. data={\n  mode: 'module',\n  parallel: true,\n  prop: [ 'module=entry', 'product=default' ],\n  incremental: true,\n  _: [ 'compileNative' ],\n  daemon: true\n}.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883746273500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3bff9a6-79c5-4ada-a156-c8d2cc29e200", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883746918700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "101d2d7c-3611-4dda-bb9c-f096e11cc491", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":13320,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"6affa5e8c485fb6924f7ddf1a995f403282ad928\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45002,\"lastUsedTime\":1749543258726,\"createdBy\":\"deveco\",\"sessionId\":\"00000050ca07df05da60d6fec9ca2e9ea2fa8af6b39378e2453d9ed4718d0f67e2c905fedce31ad831a66eac581d39bb9ceb2ecaa6ae5eac7d72c1feafd3345ecf5dcc534b50986dcd76f6b5840d78312aef6d76dc2094f9aafcdda7648164fa\"}", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883747696500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca156828-448b-4244-94d0-bb5688692541", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883752227500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43c4a796-b277-4e20-af01-7faa7eaf1d92", "name": "Cache service initialization finished in 8 ms ", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883759601600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8191dc5c-3558-4335-b071-67da80f83ba0", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883767673100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2f771f0-68cf-401a-abf2-d1e9a6aaa532", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883778563000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab4b33f6-0a66-4864-87ad-5d42170ae6cc", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883778677200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66584c2d-e84a-42da-9f6e-7db8d4f9aba7", "name": "hvigorfile, resolve finished D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883789803500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc1d3a00-8524-4425-ae6f-2bbe4c747126", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883793740400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42c11d45-ad02-4711-b431-46a48881a806", "name": "hvigorfile, require result:  { hapTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883803739700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f924231-0fc6-45ee-8630-46495fdcbfdd", "name": "hvigorfile, binding system plugins { hapTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883803831300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d36c82c-adb8-4c9c-a190-e25369554ba7", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883817799000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e210800-e6a9-4c4c-84e6-39919e2ee945", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883817826500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19b37251-3552-4285-9477-95f3842b8e4b", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883823082600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bfd56c6-d369-4288-84db-447f2ad4b813", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883823142800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be4eecfd-9770-43a9-8fd2-bc339a42fc87", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883823424900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6bdf6ed-28ae-4a88-9e12-68f6ce622627", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883823902000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b73af1c7-3b36-498c-8879-c8955c4a5f9d", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883823928000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29c05dff-9b9e-4c90-8c32-02956f8c98a2", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883823936700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a844f106-e5a0-45cc-9c30-5596fd244dd1", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883823975800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8798c33d-e1f6-4dc1-8a43-c88eaebe2fb7", "name": "require SDK: toolchains,ArkTS,native; moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883825385300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e613de92-b0a3-44dc-a130-ba82e28a6e3c", "name": "Module entry task initialization takes 9 ms ", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883837041400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7235902c-bfd7-4db7-a5fc-114a26511793", "name": "hvigorfile, resolve finished D:\\harmonyforwork\\aboutCamera\\plantDemos\\Camera\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883837098300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dae622c2-0ca8-4dd6-a5db-3e7ea4832594", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883837169500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ec2ec30-a37d-47cb-9eec-c38bc64bac75", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883837191200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da009843-dd50-4446-9603-f3b3416422ff", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883837243900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a104eed-5816-40b5-a811-1e6c1dc5401a", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883837254400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5caaa4d-73ef-462a-a990-588d1ff7baee", "name": "Mo<PERSON>le Camera Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883838184500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1ddc445-2756-4863-a630-3757c92b5888", "name": "Module Camera's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883838207200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5705eb8e-986e-43f9-9e7a-7a6706ae3215", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883840621000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3907e277-d395-4e3f-9d97-cf46160ae3c4", "name": "Sdk init in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883851603500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e8dd0ef-c53d-451a-8a8a-39d0f172abf3", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883893720900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75e9f291-353d-4d46-98e7-db6b4b03a8c9", "name": "Project task initialization takes 52 ms ", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883903432400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ded0f76-72b9-47c0-a983-158b444b2795", "name": "Sdk init in 8 ms ", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883911360300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e8f6d68-799b-4e03-a2c7-7598b92f330c", "name": "Configuration phase cost:158 ms ", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883917577900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4924642-5627-446d-b0cf-67cc1fe6aea4", "name": "Configuration task cost before running: 168 ms ", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883918974800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c97245d-165c-4b87-b066-99ce3a70b16d", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883932999600, "endTime": 27883949247400}, "additional": {"children": [], "state": "success", "detailId": "58b5abaf-10f4-4460-b177-eac37f963fe6", "logId": "f1e642f2-279c-46b0-ae83-b57051404ed4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "58b5abaf-10f4-4460-b177-eac37f963fe6", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883921921700}, "additional": {"logType": "detail", "children": [], "durationId": "5c97245d-165c-4b87-b066-99ce3a70b16d"}}, {"head": {"id": "3af765a2-b290-4783-b913-1cb77d9ca860", "name": "entry : default@PreBuild start {\n  rss: 189526016,\n  heapTotal: 125427712,\n  heapUsed: 104631672,\n  external: 1582157,\n  arrayBuffers: 616801\n}", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883932943500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd82a666-3a39-45f5-ae35-9c831a14dd0a", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883933024700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3a2e241-59c2-421a-8bf9-4645795f3563", "name": "Incremental task entry:default@PreBuild pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883948939300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5bf5829-d790-4251-8f33-a172990d94fc", "name": "entry : default@PreBuild end {\n  rss: 191451136,\n  heapTotal: 125689856,\n  heapUsed: 105074544,\n  external: 1590349,\n  arrayBuffers: 624993\n}", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883949117900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1e642f2-279c-46b0-ae83-b57051404ed4", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883932999600, "endTime": 27883949247400}, "additional": {"logType": "info", "children": [], "durationId": "5c97245d-165c-4b87-b066-99ce3a70b16d"}}, {"head": {"id": "d64a35ed-1130-4c34-92c9-3f9a4f1a4133", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883954691500, "endTime": 27886009800700}, "additional": {"children": ["4a1e5a92-27bf-4338-8d45-aacb6a15a3be", "b28d045f-3290-4413-9dd4-4d4e5a72a087", "93014e3d-a6df-4564-964e-85ee5486d130"], "state": "success", "detailId": "4392010b-ee69-42f4-8420-f8db74dc9d0a", "logId": "7a68c642-6828-4b63-959b-be4cec0eb0f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "4392010b-ee69-42f4-8420-f8db74dc9d0a", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883953280400}, "additional": {"logType": "detail", "children": [], "durationId": "d64a35ed-1130-4c34-92c9-3f9a4f1a4133"}}, {"head": {"id": "e88b84ac-b70e-4bfe-b8fc-9f66c9e8ec82", "name": "entry : default@BuildNativeWithCmake start {\n  rss: 192040960,\n  heapTotal: 125689856,\n  heapUsed: 105310800,\n  external: 1590349,\n  arrayBuffers: 624993\n}", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883954645400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "670fcca8-1c0a-45d6-a0e7-0f5417311ac4", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883954725700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09efedf1-de6d-4fe7-aadb-bae2c9478d92", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\arm64-v8a',\n  '-DOHOS_ARCH=arm64-v8a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\arm64-v8a',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=arm64-v8a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883964763500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffd5d7a4-c070-40be-8994-9a3465885a37", "name": "default@BuildNativeWithCmake work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883967457100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a1e5a92-27bf-4338-8d45-aacb6a15a3be", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 13320, "tid": "Worker0", "startTime": 27883970138300, "endTime": 27885753506300}, "additional": {"children": [], "state": "success", "parent": "d64a35ed-1130-4c34-92c9-3f9a4f1a4133", "logId": "f78ab5c9-09b1-4b1c-b8e4-a7b581d7750b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "5d06a55b-3600-4831-a7d1-b61768667339", "name": "default@BuildNativeWithCmake work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883969147000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b5aae9d-96bf-4b18-bc85-58a3a56d829a", "name": "default@BuildNativeWithCmake work[0] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883970211000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f955dd4-44dd-4189-87f9-eb1c7f8f10ac", "name": "default@BuildNativeWithCmake work[0] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883970362500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d1aea11-6021-4ca5-9604-925d6a5740f5", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\armeabi-v7a',\n  '-DOHOS_ARCH=armeabi-v7a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\armeabi-v7a',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=armeabi-v7a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883973676400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd959de2-1f0c-4f79-912c-458a6c54111b", "name": "default@BuildNativeWithCmake work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883975906500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b28d045f-3290-4413-9dd4-4d4e5a72a087", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 13320, "tid": "Worker1", "startTime": 27883976921400, "endTime": 27885752614500}, "additional": {"children": [], "state": "success", "parent": "d64a35ed-1130-4c34-92c9-3f9a4f1a4133", "logId": "890b17cf-5043-44ca-98a9-c0d29853697d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "16890f89-11e5-41be-a509-89539720268b", "name": "default@BuildNativeWithCmake work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883976753000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3add5c41-cd98-4d89-945e-9c4d4fc71b0f", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883976783800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f342bc6-0fd6-4748-83ef-d007f66c19e9", "name": "default@BuildNativeWithCmake work[1] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883976943000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0f413ea-b016-4bd1-9b7f-346b0091676d", "name": "default@BuildNativeWithCmake work[1] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883976984500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8326175f-899b-4535-b409-41c595a9f7e0", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\x86_64',\n  '-DOHOS_ARCH=x86_64',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\x86_64',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=x86_64',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883980869000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "475c3e3d-c5fe-4b3f-877c-8f0a3dc6f059", "name": "default@BuildNativeWithCmake work[2] is submitted.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883982416700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93014e3d-a6df-4564-964e-85ee5486d130", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 13320, "tid": "Worker1", "startTime": 27885753351500, "endTime": 27886009587800}, "additional": {"children": [], "state": "success", "parent": "d64a35ed-1130-4c34-92c9-3f9a4f1a4133", "logId": "51c20308-0917-4093-a89d-ddf694aa6bc6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "c9949845-d619-4e0d-8a38-4ccd37bd7b30", "name": "default@BuildNativeWithCmake work[2] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883983246600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0de9f15e-9920-463a-9272-537cd382d7af", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883983281000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35dd570d-3da1-4bee-a81b-9f1e42599761", "name": "A work dispatched to worker[1] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883983294000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61bf0930-5085-4807-8ae3-193a9b8ecf40", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883983305300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2405e4b-06be-4227-8957-c115206cf3ee", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883983313600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21e91516-1d97-478b-80eb-b7cc590e7bae", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883983330900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16fb04d1-41db-4ffa-915f-1473395fc119", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883983352900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f3047f2-8719-47dc-b1a1-5997ea892ec3", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883983373100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "935f0327-a67e-4856-a09c-6bc1c4c46b39", "name": "default@BuildNativeWithCmake work[2] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883983384600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "add7f7b6-79af-4b06-ad46-6fbdb506e8a3", "name": "entry : default@BuildNativeWithCmake end {\n  rss: 193351680,\n  heapTotal: 125689856,\n  heapUsed: 105996232,\n  external: 1590349,\n  arrayBuffers: 624993\n}", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883983498900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58f90e73-43a1-4f03-8445-7616a29d427c", "name": "default@BuildNativeWithCmake work[1] done.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27885752829500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "890b17cf-5043-44ca-98a9-c0d29853697d", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 13320, "tid": "Worker1", "startTime": 27883976921400, "endTime": 27885752614500}, "additional": {"logType": "info", "children": [], "durationId": "b28d045f-3290-4413-9dd4-4d4e5a72a087", "parent": "7a68c642-6828-4b63-959b-be4cec0eb0f4"}}, {"head": {"id": "f2118f12-e4c9-4ea8-ae61-2e1ec7c13b07", "name": "default@BuildNativeWithCmake work[2] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27885753372700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a302bcd-e770-4822-be46-59ce2c724e27", "name": "default@BuildNativeWithCmake work[0] done.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27885753532700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f78ab5c9-09b1-4b1c-b8e4-a7b581d7750b", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 13320, "tid": "Worker0", "startTime": 27883970138300, "endTime": 27885753506300}, "additional": {"logType": "info", "children": [], "durationId": "4a1e5a92-27bf-4338-8d45-aacb6a15a3be", "parent": "7a68c642-6828-4b63-959b-be4cec0eb0f4"}}, {"head": {"id": "e803d790-52cf-49f7-be21-96b020fc969a", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27885753617200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "040e6ba6-f1f6-45e3-9c41-11f2821c5418", "name": "default@BuildNativeWithCmake work[2] done.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27886009647600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51c20308-0917-4093-a89d-ddf694aa6bc6", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 13320, "tid": "Worker1", "startTime": 27885753351500, "endTime": 27886009587800}, "additional": {"logType": "info", "children": [], "durationId": "93014e3d-a6df-4564-964e-85ee5486d130", "parent": "7a68c642-6828-4b63-959b-be4cec0eb0f4"}}, {"head": {"id": "7a68c642-6828-4b63-959b-be4cec0eb0f4", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883954691500, "endTime": 27886009800700}, "additional": {"logType": "info", "children": ["f78ab5c9-09b1-4b1c-b8e4-a7b581d7750b", "890b17cf-5043-44ca-98a9-c0d29853697d", "51c20308-0917-4093-a89d-ddf694aa6bc6"], "durationId": "d64a35ed-1130-4c34-92c9-3f9a4f1a4133"}}, {"head": {"id": "ac2bd8dc-e883-4a6d-92f8-9b39386694a9", "name": "entry:compileNative", "description": "Compile the hook task for native resources.", "type": "duration"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27886018520200, "endTime": 27886020602800}, "additional": {"children": [], "state": "success", "detailId": "59b4b37b-8525-43f2-9142-f9310dc10ebe", "logId": "e4e4b0a2-d784-4459-bc31-5553c1a39f85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "59b4b37b-8525-43f2-9142-f9310dc10ebe", "name": "create entry:compileNative task", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27886016834800}, "additional": {"logType": "detail", "children": [], "durationId": "ac2bd8dc-e883-4a6d-92f8-9b39386694a9"}}, {"head": {"id": "eac6be84-8265-4d16-a0cc-73fde3abf4d8", "name": "entry : compileNative start {\n  rss: 294064128,\n  heapTotal: 126500864,\n  heapUsed: 94054544,\n  external: 1590349,\n  arrayBuffers: 71704\n}", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27886018441300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a572d4b-fc5a-44fa-8f56-b4794343ef12", "name": "Executing task :entry:compileNative", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27886020189100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a2d452c-43fe-44b2-875e-659dd42a1764", "name": "entry : compileNative end {\n  rss: 294072320,\n  heapTotal: 126500864,\n  heapUsed: 94063784,\n  external: 1590349,\n  arrayBuffers: 71704\n}", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27886020540500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4e4b0a2-d784-4459-bc31-5553c1a39f85", "name": "Finished :entry:compileNative", "description": "Compile the hook task for native resources.", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27886018520200, "endTime": 27886020602800}, "additional": {"logType": "info", "children": [], "durationId": "ac2bd8dc-e883-4a6d-92f8-9b39386694a9"}}, {"head": {"id": "19fce4c1-93fe-4338-82d3-75fad9876d59", "name": "BUILD SUCCESSFUL in 2 s 271 ms ", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27886021329800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "882e8317-7f51-42cd-855a-77e419898354", "name": "compileNative", "description": "", "type": "mark"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27883751009100, "endTime": 27886022752200}, "additional": {"time": {"year": 2025, "month": 6, "day": 10, "hour": 16, "minute": 14}, "markType": "history", "category": "build", "state": "success"}}, {"head": {"id": "607201e4-daef-4926-bf85-0c145bd2f974", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13320, "tid": "Main Thread", "startTime": 27886023086000}, "additional": {"logType": "debug", "children": []}}], "workLog": []}