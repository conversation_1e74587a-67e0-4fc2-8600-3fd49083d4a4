{"version": "1.0", "events": [{"head": {"id": "323c064c-8fbf-47c8-99a5-e8e827fce1bb", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785193099600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cf30d26-befb-4078-bad8-93680b0a1275", "name": "hvigor disconnect:  client namespace disconnect", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785195720700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0ca971a-fbc3-4888-95f3-e449048ba64d", "name": "hvigor daemon: Socket will be closed. socketId=i1Vt8zEMlkUiN5ZcAAAB, connectionCount=0", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785197022900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00b55120-645a-4e65-ac32-1d0f495e5085", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":27036,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"fd9e05b4d84c056550a5ca3011a9a345f2863f7c\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1750683435169,\"createdBy\":\"deveco\",\"sessionId\":\"0000005011a4a8137b923b9a1f8574e249fdc2b8abf50e8220c993b1f13dcaa360a4f09cb218793e0881f9579ab0a3fb0e188eade7c4d362e698abddd6a6c737fcaffdebabb3904bba293cb3df5ff7b3376bd0aaadf926390f76bd1444b1c369\"}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785197913700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f26ddc39-c8e1-4386-8ba3-88a16f38e9ed", "name": "worker[0] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785202766600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "233bda81-14dd-483c-9153-75086de9f10d", "name": "worker[1] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785203004100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9d21168-bd16-4a0c-9189-686b8fdba678", "name": "hvigor daemon: Socket is connected. socketId=_8G_4ALFE-5xneJ5AAAD, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785914491800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69d008ae-62c5-4c9b-a92d-ac559f81b566", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"fef7962a3805bfc5e665909552d6b84411df2805\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\testC\\\\Camera\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45001,\"pid\":9284,\"state\":\"idle\",\"lastUsedTime\":1750407074786,\"info\":null,\"createdBy\":\"deveco\",\"sessionId\":\"00000050aab88ac64b4178893fd796169b640639e1c9b813c06886269a431824f1c6089863679a3ccb64e3a10d6a82cfd481d956f2ce09eff3c9e009bf2456969ec7152898c9fea931f63ba2c72e0a1aee70f1f5847df547a40a94e65b6a302a\"},{\"pid\":27036,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"fd9e05b4d84c056550a5ca3011a9a345f2863f7c\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1750683435179,\"createdBy\":\"deveco\",\"sessionId\":\"0000005011a4a8137b923b9a1f8574e249fdc2b8abf50e8220c993b1f13dcaa360a4f09cb218793e0881f9579ab0a3fb0e188eade7c4d362e698abddd6a6c737fcaffdebabb3904bba293cb3df5ff7b3376bd0aaadf926390f76bd1444b1c369\"}]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785915765400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bff2f341-ae22-44eb-9842-3f6e473994f8", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":27036,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"fd9e05b4d84c056550a5ca3011a9a345f2863f7c\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1750683435179,\"createdBy\":\"deveco\",\"sessionId\":\"0000005011a4a8137b923b9a1f8574e249fdc2b8abf50e8220c993b1f13dcaa360a4f09cb218793e0881f9579ab0a3fb0e188eade7c4d362e698abddd6a6c737fcaffdebabb3904bba293cb3df5ff7b3376bd0aaadf926390f76bd1444b1c369\"}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785916727200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0917a4a0-b540-4c08-bd9f-aa1f4715138a", "name": "set active socket. socketId=_8G_4ALFE-5xneJ5AAAD", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785922736100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e33054f5-817d-4037-b315-a8237df7fba6", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":27036,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"fd9e05b4d84c056550a5ca3011a9a345f2863f7c\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1750683435898,\"createdBy\":\"deveco\",\"sessionId\":\"0000005011a4a8137b923b9a1f8574e249fdc2b8abf50e8220c993b1f13dcaa360a4f09cb218793e0881f9579ab0a3fb0e188eade7c4d362e698abddd6a6c737fcaffdebabb3904bba293cb3df5ff7b3376bd0aaadf926390f76bd1444b1c369\"}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785923666000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e413a8f-de58-4f66-a8b0-356f49a3e6b8", "name": "hvigor daemon: Receive data from client. data={\n  mode: 'module',\n  parallel: true,\n  prop: [ 'module=entry', 'product=default' ],\n  incremental: true,\n  _: [ 'compileNative' ],\n  daemon: true\n}.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785927121000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2a5cf8c-c345-4ec6-8de8-a84cd1c2f04f", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785927857100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7aa68f7-eaa1-4dbc-8e23-ef85fa0be79c", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":27036,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"fd9e05b4d84c056550a5ca3011a9a345f2863f7c\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1750683435905,\"createdBy\":\"deveco\",\"sessionId\":\"0000005011a4a8137b923b9a1f8574e249fdc2b8abf50e8220c993b1f13dcaa360a4f09cb218793e0881f9579ab0a3fb0e188eade7c4d362e698abddd6a6c737fcaffdebabb3904bba293cb3df5ff7b3376bd0aaadf926390f76bd1444b1c369\"}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785928754700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14a8f093-d985-40e0-b841-8cf36b2af7c1", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785933150900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "057c6bee-60ff-4ed9-be0d-dea90facec9f", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785938802300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b161afa-958e-43da-bde1-f8cee8f1f8a9", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785946260700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5fceb27-1dad-4d67-ada4-21afd9ce232d", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785955375900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49bfb7ee-7096-4dc7-a061-8090a89b6f19", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785955456000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caced08a-79f1-4568-9693-e5477dbf1374", "name": "hvigorfile, resolve finished D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785963913500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f9866b2-933f-477c-a21c-0c05692872d9", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785967176700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67556c98-cde8-4649-b049-636c44fea51a", "name": "hvigorfile, require result:  { hapTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785977334500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd04adc6-311e-4cb3-8175-8516a17eb690", "name": "hvigorfile, binding system plugins { hapTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785977408300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "457d0e53-b21d-496f-94ad-38fae0372da1", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785989791000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba9ddaa2-8a86-41f5-91ab-177147216af2", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785989834600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52eba5aa-09bd-4374-b028-cd576912d810", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785994890500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3bf9c70-03b6-49a2-ad2b-b2c4a36bc0da", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785994937800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f543852b-1324-4c12-9469-b12c501c51e9", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785995221600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6343005-362d-4775-a824-69d5b8124ed7", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785995677600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e3b2948-51e6-4de3-bb0c-46f255970e54", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785995700000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed491fb4-d1b2-47a4-bcb8-323894d47eab", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785995707500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e766c0d7-891c-43cb-991e-1d2cd877bc66", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785995736300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7c323a1-c5f6-49e0-b6be-d6e66cb4f418", "name": "require SDK: toolchains,ArkTS,native; moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785997187400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adb37f4e-cc89-4fa7-85ad-cf5f6e31f06e", "name": "Module entry task initialization takes 6 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786005982700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ef24328-b271-4686-b5eb-720cfb8567ac", "name": "hvigorfile, resolve finished D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786006024800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb42e26f-bac1-41cb-843d-3ce717f77ec6", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786006083200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4089c49-ca19-43a9-ba03-b7390f6412cd", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786006098000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5408aa6e-5cca-42c1-a24f-389f342b8d20", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786006135200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69007f9e-75da-49a8-a0c1-e71145a6e26f", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786006140800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1470a0d9-1f64-45e0-b966-0c1f945b0204", "name": "Mo<PERSON>le Camera Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786006835200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac458ba6-8011-4576-8995-ae5486ccd6a6", "name": "Module Camera's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786006852400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be597321-20f6-443a-a321-d1cc7e2f1c1d", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786009234000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04a1cbf3-a7cb-456f-b75a-1968fb9dde4b", "name": "Sdk init in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786022969200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cb7a9b4-1487-4d8d-a8b5-a6cc85d32321", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786068592400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c1176c2-4d7b-49bb-914a-5de5771f39ec", "name": "Project task initialization takes 52 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786075048700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b4a7da7-ec12-4aa3-a248-b4995e9a0542", "name": "Sdk init in 10 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786084445700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e97534e-60bf-456e-acef-486e6f6a8c67", "name": "Configuration phase cost:149 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786087914100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "996f3cdb-6ce2-4439-83d7-26e790afe885", "name": "Configuration task cost before running: 158 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786089387500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4076e024-226b-4bbc-a26e-eb759b8807f3", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786104050300, "endTime": 5786364782600}, "additional": {"children": [], "state": "success", "detailId": "33137cfa-435e-40a5-89b9-ca1e40ef783f", "logId": "5283d925-c9d7-473f-a954-275fb5e996eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "33137cfa-435e-40a5-89b9-ca1e40ef783f", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786091823400}, "additional": {"logType": "detail", "children": [], "durationId": "4076e024-226b-4bbc-a26e-eb759b8807f3"}}, {"head": {"id": "94ecb8ce-0133-4bd7-8083-35e7cc4d105d", "name": "entry : default@PreBuild start {\n  rss: 190009344,\n  heapTotal: 125689856,\n  heapUsed: 104872552,\n  external: 1549389,\n  arrayBuffers: 584033\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786103992900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fccf5b79-0813-44dd-a875-e652669fec08", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786104072900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1be52bc5-4e8d-4231-9d1b-1b78c5f4c9d2", "name": "entry:default@PreBuild is not up-to-date, since the input file 'D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\app.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786114214400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad1fa207-ee3c-478d-adcf-a07783daa691", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786114309700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08b9e2a4-0053-4eeb-8a66-b0cb31d92602", "name": "Use tool [win32: JAVA_HOME, CLASSPATH]\n [\n  { JAVA_HOME: 'D:\\\\harmonyFor\\\\DevEco Studio\\\\jbr' },\n  {\n    CLASSPATH: '.;D:\\\\houduan\\\\java1.8\\\\jdk\\\\lib\\\\dt.jar;D:\\\\houduan\\\\java1.8\\\\jdk\\\\lib\\\\tools.jar'\n  }\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786364112900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f8172f2-b8bd-443b-bad9-2cc5807404e5", "name": "Use tool [win32: NODE_HOME]\n [ { NODE_HOME: 'D:\\\\harmonyFor\\\\node' } ]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786364221700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "550fc930-239a-4e4d-98a9-3b8f772c14f4", "name": "entry : default@PreBuild end {\n  rss: 203730944,\n  heapTotal: 130527232,\n  heapUsed: 107907080,\n  external: 1053444,\n  arrayBuffers: 88088\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786364743100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5283d925-c9d7-473f-a954-275fb5e996eb", "name": "Finished :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786104050300, "endTime": 5786364782600}, "additional": {"logType": "info", "children": [], "durationId": "4076e024-226b-4bbc-a26e-eb759b8807f3"}}, {"head": {"id": "263d09e9-1599-4edc-935b-05112a401450", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786368736700, "endTime": 5794618356800}, "additional": {"children": ["41ff710f-cef4-45fd-a395-c5c9157d6caa", "3ac80116-3398-4a28-b3e9-a80305d6630c", "ce269774-ccdc-4039-976e-f26ff32f8cf5"], "state": "success", "detailId": "4dbfa5cb-6a69-49c7-9154-abc3323c49de", "logId": "0e904380-c816-4387-ad5d-7a9e25f1b0ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "4dbfa5cb-6a69-49c7-9154-abc3323c49de", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786367895100}, "additional": {"logType": "detail", "children": [], "durationId": "263d09e9-1599-4edc-935b-05112a401450"}}, {"head": {"id": "d2261e2e-2e2d-45df-b609-edc05a6e871f", "name": "entry : default@BuildNativeWithCmake start {\n  rss: 203763712,\n  heapTotal: 130527232,\n  heapUsed: 108145304,\n  external: 1053444,\n  arrayBuffers: 88088\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786368712500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae0759e1-8f7d-4671-be70-6c615ee5f563", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786368750800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b7cd36d-fd06-46b7-b6a7-4112d54d4874", "name": "runTaskFromQueue task cost before running: 437 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786368849200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f3f70a7-0166-4636-9677-13711797240a", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\arm64-v8a',\n  '-DOHOS_ARCH=arm64-v8a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\arm64-v8a',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=arm64-v8a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786374102300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1b36c4f-5402-447c-9785-a692066ad0d0", "name": "Clean the cmake cache file CMakeCache.txt due to configuration change.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786375029500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f97e7fdf-66cf-4b3b-a6f6-9e42c4b19d79", "name": "default@BuildNativeWithCmake work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786379698000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41ff710f-cef4-45fd-a395-c5c9157d6caa", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5786381859900, "endTime": 5792179567000}, "additional": {"children": [], "state": "success", "parent": "263d09e9-1599-4edc-935b-05112a401450", "logId": "1e8b2aac-1268-47de-81e4-34d35406af7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "fb2bb75e-ba73-44c0-a4f3-70e33e782386", "name": "default@BuildNativeWithCmake work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786381120200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73042a3c-b738-465a-98a2-7c17ddd0478c", "name": "default@BuildNativeWithCmake work[0] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786381922000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1df4073-4ba9-4cd2-9fe3-ffce384599d3", "name": "default@BuildNativeWithCmake work[0] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786382041000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4af34ad-7e2d-4761-b86a-f41127d3560a", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\armeabi-v7a',\n  '-DOHOS_ARCH=armeabi-v7a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\armeabi-v7a',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=armeabi-v7a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786384100900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "751b07fc-0a9b-4f65-83db-c7ee7fa1b5a3", "name": "Clean the cmake cache file CMakeCache.txt due to configuration change.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786385071000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e6b6272-9ee1-4e38-a48e-f897b339d128", "name": "default@BuildNativeWithCmake work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786388187200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ac80116-3398-4a28-b3e9-a80305d6630c", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker1", "startTime": 5786388867300, "endTime": 5792180314700}, "additional": {"children": [], "state": "success", "parent": "263d09e9-1599-4edc-935b-05112a401450", "logId": "f7818df7-0721-4146-bb92-54550710a24f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "d420b126-c55c-42d3-aceb-df3650257bb3", "name": "default@BuildNativeWithCmake work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786388750300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e82e6d7-011e-4e39-a701-c011960f5b83", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786388768600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96258741-bac5-4bcf-8b9c-f37099b99020", "name": "default@BuildNativeWithCmake work[1] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786388878800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54a81531-f861-4d7c-9016-854170fa255d", "name": "default@BuildNativeWithCmake work[1] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786388905200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "463117d5-599d-4dfe-b9b0-8f42241a86c9", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\x86_64',\n  '-DOHOS_ARCH=x86_64',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\x86_64',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=x86_64',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786390899000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "340ce04d-c1f7-4cb1-aa59-2ca965d56a04", "name": "Clean the cmake cache file CMakeCache.txt due to configuration change.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786391672600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69b5be9b-4495-4efa-814d-03f4e8fd4d01", "name": "default@BuildNativeWithCmake work[2] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786395215200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce269774-ccdc-4039-976e-f26ff32f8cf5", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5792180204400, "endTime": 5794618254600}, "additional": {"children": [], "state": "success", "parent": "263d09e9-1599-4edc-935b-05112a401450", "logId": "370994c6-fd08-44b6-a7a1-87ac72cf4f77"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "9c9020c6-a21e-43b8-bf64-b5a3e446312a", "name": "default@BuildNativeWithCmake work[2] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786395843000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67164937-5843-4207-9fd0-27a5a3ca7f4f", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786395867800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c81c878-d1a6-44dc-b01f-f0f2bc75ba2f", "name": "A work dispatched to worker[1] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786395878000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7e10538-c537-4cf8-b8a4-713dbe85d8fe", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786395887500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63269e7c-e57f-4ef7-a38d-bf6f12df09cb", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786395894900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ede4ffd4-b8fe-4b85-b85f-bb751e131915", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786395907100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a972bbae-ab47-4d50-913c-a71e73ed5542", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786395919700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4177533-fc19-462a-8ac1-0c76a33426ec", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786395934300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d61a3687-18c7-489d-9386-ece2ccb447ad", "name": "default@BuildNativeWithCmake work[2] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786395992900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f38bfaa0-acad-4151-9334-ab0d4743b984", "name": "entry : default@BuildNativeWithCmake end {\n  rss: 204603392,\n  heapTotal: 130789376,\n  heapUsed: 109147056,\n  external: 1053444,\n  arrayBuffers: 88088\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786396110700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a0914bd-d71d-4170-a613-75eff3cb19bd", "name": "default@BuildNativeWithCmake work[0] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5792179810700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e8b2aac-1268-47de-81e4-34d35406af7c", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5786381859900, "endTime": 5792179567000}, "additional": {"logType": "info", "children": [], "durationId": "41ff710f-cef4-45fd-a395-c5c9157d6caa", "parent": "0e904380-c816-4387-ad5d-7a9e25f1b0ca"}}, {"head": {"id": "3544928d-c2c9-4eda-b7b7-6d4aabd8c9d1", "name": "default@BuildNativeWithCmake work[2] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5792180220900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3b41306-8235-40f5-9555-3c3749a256dd", "name": "default@BuildNativeWithCmake work[1] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5792180335100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7818df7-0721-4146-bb92-54550710a24f", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker1", "startTime": 5786388867300, "endTime": 5792180314700}, "additional": {"logType": "info", "children": [], "durationId": "3ac80116-3398-4a28-b3e9-a80305d6630c", "parent": "0e904380-c816-4387-ad5d-7a9e25f1b0ca"}}, {"head": {"id": "8879ad4a-9344-40b8-b276-7f3f85edd9f0", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5792180388200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "164244ee-3e6b-4318-9bf8-5506b05705c7", "name": "default@BuildNativeWithCmake work[2] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794618289500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "370994c6-fd08-44b6-a7a1-87ac72cf4f77", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5792180204400, "endTime": 5794618254600}, "additional": {"logType": "info", "children": [], "durationId": "ce269774-ccdc-4039-976e-f26ff32f8cf5", "parent": "0e904380-c816-4387-ad5d-7a9e25f1b0ca"}}, {"head": {"id": "0e904380-c816-4387-ad5d-7a9e25f1b0ca", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5786368736700, "endTime": 5794618356800}, "additional": {"logType": "info", "children": ["1e8b2aac-1268-47de-81e4-34d35406af7c", "f7818df7-0721-4146-bb92-54550710a24f", "370994c6-fd08-44b6-a7a1-87ac72cf4f77"], "durationId": "263d09e9-1599-4edc-935b-05112a401450"}}, {"head": {"id": "4764088b-a846-4481-bafb-a784aadcbb60", "name": "entry:compileNative", "description": "Compile the hook task for native resources.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794621015700, "endTime": 5794621142300}, "additional": {"children": [], "state": "success", "detailId": "3e189976-1085-4dd5-b3cf-9a52e8b1172e", "logId": "69ba2d64-f463-42a6-a4e8-75fbf6b03c51"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "3e189976-1085-4dd5-b3cf-9a52e8b1172e", "name": "create entry:compileNative task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794620902900}, "additional": {"logType": "detail", "children": [], "durationId": "4764088b-a846-4481-bafb-a784aadcbb60"}}, {"head": {"id": "61507c9b-9fb3-43ad-a5de-fb5b7618b6e6", "name": "entry : compileNative start {\n  rss: 299794432,\n  heapTotal: 132100096,\n  heapUsed: 97010416,\n  external: 1053444,\n  arrayBuffers: 71704\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794620997200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf96700b-c1f2-40dd-93f8-c10c49389fd4", "name": "Executing task :entry:compileNative", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794621026700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67ce22d3-1357-4d86-83fc-14dfcac7cad2", "name": "entry : compileNative end {\n  rss: 299798528,\n  heapTotal: 132100096,\n  heapUsed: 97019656,\n  external: 1053444,\n  arrayBuffers: 71704\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794621128600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69ba2d64-f463-42a6-a4e8-75fbf6b03c51", "name": "Finished :entry:compileNative", "description": "Compile the hook task for native resources.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794621015700, "endTime": 5794621142300}, "additional": {"logType": "info", "children": [], "durationId": "4764088b-a846-4481-bafb-a784aadcbb60"}}, {"head": {"id": "2aa5dcab-0ff5-4840-878e-033dcd1d6e25", "name": "BUILD SUCCESSFUL in 8 s 690 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794621653400}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "b7386dcf-0391-4e99-b0ea-b25098d5f929", "name": "compileNative", "description": "", "type": "mark"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5785932034200, "endTime": 5794621999400}, "additional": {"time": {"year": 2025, "month": 6, "day": 23, "hour": 20, "minute": 57}, "markType": "history", "category": "build", "state": "success"}}, {"head": {"id": "f61b1b30-c574-465e-a8c5-e022cccf4b67", "name": "Update task entry:default@PreBuild input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\app.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794622677400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc076ca1-dd5b-41dd-b440-759bc1891e8b", "name": "Update task entry:default@PreBuild input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794622710500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ac1989f-afd3-463d-882e-b0943de78e2d", "name": "Update task entry:default@PreBuild input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794622919400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76c80a4f-098e-491f-8f7f-04fa32062057", "name": "Update task entry:default@PreBuild input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794623076400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb502659-1953-4ce1-a67b-10d98972d72c", "name": "Update task entry:default@PreBuild input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources\\base\\profile\\main_pages.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794623349300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc3796d7-5d9f-4bcf-a892-b66d5f266815", "name": "Update task entry:default@PreBuild input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794623520200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da99e07a-5267-4c1d-b3ea-654451bfc3fa", "name": "Incremental task entry:default@PreBuild post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794623831200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}