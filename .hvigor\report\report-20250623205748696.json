{"version": "1.0", "events": [{"head": {"id": "1a028790-614c-4039-8ad9-a4efcadec286", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794639780900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dab3c60f-01e0-41db-9b9f-eaceb20336b8", "name": "hvigor disconnect:  client namespace disconnect", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794645027600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4928960-6dd6-4b33-a303-b94eba2d5863", "name": "hvigor daemon: Socket will be closed. socketId=_8G_4ALFE-5xneJ5AAAD, connectionCount=0", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794646072400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "592a3d29-5998-47e7-b838-4375a5e3ba85", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":27036,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"fd9e05b4d84c056550a5ca3011a9a345f2863f7c\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1750683444617,\"createdBy\":\"deveco\",\"sessionId\":\"0000005011a4a8137b923b9a1f8574e249fdc2b8abf50e8220c993b1f13dcaa360a4f09cb218793e0881f9579ab0a3fb0e188eade7c4d362e698abddd6a6c737fcaffdebabb3904bba293cb3df5ff7b3376bd0aaadf926390f76bd1444b1c369\"}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794647176200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a0af314-8b19-41a4-9fae-63195f679273", "name": "worker[0] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794656356200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58f72eb8-2d2d-400c-a929-43ebcbb3818b", "name": "worker[1] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5794656721700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89593a7e-90aa-431f-a1ee-5f9c24c010d7", "name": "hvigor daemon: Socket is connected. socketId=twtd7eTYdvwPAyH2AAAF, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801011057500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "554d1fbf-dba1-476c-8cb5-949e059b6b5c", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"fef7962a3805bfc5e665909552d6b84411df2805\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\testC\\\\Camera\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45001,\"pid\":9284,\"state\":\"idle\",\"lastUsedTime\":1750407074786,\"info\":null,\"createdBy\":\"deveco\",\"sessionId\":\"00000050aab88ac64b4178893fd796169b640639e1c9b813c06886269a431824f1c6089863679a3ccb64e3a10d6a82cfd481d956f2ce09eff3c9e009bf2456969ec7152898c9fea931f63ba2c72e0a1aee70f1f5847df547a40a94e65b6a302a\"},{\"pid\":27036,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"fd9e05b4d84c056550a5ca3011a9a345f2863f7c\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1750683444629,\"createdBy\":\"deveco\",\"sessionId\":\"0000005011a4a8137b923b9a1f8574e249fdc2b8abf50e8220c993b1f13dcaa360a4f09cb218793e0881f9579ab0a3fb0e188eade7c4d362e698abddd6a6c737fcaffdebabb3904bba293cb3df5ff7b3376bd0aaadf926390f76bd1444b1c369\"}]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801012047700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3709f6ab-aca0-4740-8ae6-ac0fae1bf769", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":27036,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"fd9e05b4d84c056550a5ca3011a9a345f2863f7c\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1750683444629,\"createdBy\":\"deveco\",\"sessionId\":\"0000005011a4a8137b923b9a1f8574e249fdc2b8abf50e8220c993b1f13dcaa360a4f09cb218793e0881f9579ab0a3fb0e188eade7c4d362e698abddd6a6c737fcaffdebabb3904bba293cb3df5ff7b3376bd0aaadf926390f76bd1444b1c369\"}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801012838000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfc2f973-d537-4dda-ab39-73d54eed1590", "name": "set active socket. socketId=twtd7eTYdvwPAyH2AAAF", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801016807100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "770d6d3a-458f-4d8f-bf1d-163fa750b171", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":27036,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"fd9e05b4d84c056550a5ca3011a9a345f2863f7c\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1750683450994,\"createdBy\":\"deveco\",\"sessionId\":\"0000005011a4a8137b923b9a1f8574e249fdc2b8abf50e8220c993b1f13dcaa360a4f09cb218793e0881f9579ab0a3fb0e188eade7c4d362e698abddd6a6c737fcaffdebabb3904bba293cb3df5ff7b3376bd0aaadf926390f76bd1444b1c369\"}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801017510600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03d7f087-1753-4354-bbae-696a215c90b3", "name": "hvigor daemon: Receive data from client. data={\n  mode: 'module',\n  parallel: true,\n  prop: [ 'module=entry@default', 'product=default' ],\n  incremental: true,\n  _: [ 'assembleHap' ],\n  daemon: true\n}.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801019958200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05fb1790-40c7-4a7c-bbff-523f1a95c7f5", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801020432300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9198230-2a5e-4069-8019-f667400575b5", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":27036,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"fd9e05b4d84c056550a5ca3011a9a345f2863f7c\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1750683450998,\"createdBy\":\"deveco\",\"sessionId\":\"0000005011a4a8137b923b9a1f8574e249fdc2b8abf50e8220c993b1f13dcaa360a4f09cb218793e0881f9579ab0a3fb0e188eade7c4d362e698abddd6a6c737fcaffdebabb3904bba293cb3df5ff7b3376bd0aaadf926390f76bd1444b1c369\"}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801021055500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cec7aff1-489f-4584-a0bf-cd426f54e033", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801024205700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adf6134b-ec2b-4939-9979-fc14375f2ffb", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801029711400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b4abede-e714-47a1-9be3-98be9d7a92ab", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801035361000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "912f981b-c05a-4c41-b519-c9b7be178c9e", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801040926400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2706f4e4-8a0d-4d19-a95b-79e7ee69bbb6", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801040967900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1a91db7-e400-4be7-bacc-a2c385ce6078", "name": "hvigorfile, resolve finished D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801046773200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c424166-ce53-4fc1-a28b-24bfc790c04a", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801048900300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "005def8c-d911-4d52-beef-cdcd51c2f7cd", "name": "hvigorfile, require result:  { hapTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801053459800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cc6032a-7bee-4429-86c3-e1fad18ac785", "name": "hvigorfile, binding system plugins { hapTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801053489200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74827ed8-343c-47c4-a9b9-c5d458bb5154", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801061734200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "196a90a8-168f-4d9a-aa33-c46f9bec8687", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801061786900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdd237e7-2dca-4754-a0f6-786550b6c111", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801064965900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad1529e7-66de-49c7-8df3-1123fea220c2", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801064999100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a8939b9-d3e6-4566-a60c-5aa2a5bfbaf8", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801065124100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61243b42-5a18-457b-8eeb-0e7e0e9b0b79", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801065196900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "704d341b-59e1-48c0-8b8d-0d83ff5c6cf2", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801065207400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa1699cd-7087-4672-8bb9-eec727565425", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801065211800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f81eba9e-acec-4765-be0a-6a9cc3a03f11", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801065231000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0489ead7-4a63-4d6a-bff7-4b3a71305b60", "name": "require SDK: toolchains,ArkTS,native; moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801066278400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac426722-35b8-42f2-b46e-64df3d2633ab", "name": "Module entry task initialization takes 6 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801074356200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "437ddd55-57da-4761-b1cd-31bad2318591", "name": "hvigorfile, resolve finished D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801074410000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "544a225f-85bd-4864-85d0-131f6e3b5083", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801074473200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "806f6bae-e8b8-41e0-b8c4-7a3a84e8e661", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801074490000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70b784a8-5fb0-4cb5-b024-dd335b618298", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801074532700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0634d3c-f56f-4c8a-985d-918c5666a640", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801074541100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c1afb94-1f17-499d-94b7-9c4d57a48a25", "name": "Mo<PERSON>le Camera Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801075191900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c12e68ed-3b5e-43d6-b09e-088fa55f85ba", "name": "Module Camera's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801075211700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d952028-0622-4b57-86a7-899ed7593c9b", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801078004800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84e552d4-fb9f-43f2-a59f-e0c783b80473", "name": "Sdk init in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801085574000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "889152a5-2dca-4e54-9ac5-509f84b9c1b4", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801109018900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4691368f-aa10-43c8-90e5-95c61ca15828", "name": "Project task initialization takes 28 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801112771200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6de70741-8df1-46d0-9fac-ccdb28c42694", "name": "Sdk init in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801117332600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fc429b0-a9b4-4821-9fee-c35e4772de33", "name": "Configuration phase cost:90 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801119465700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2688a3f0-6f97-4b3e-90ab-00bbb74ea07c", "name": "Configuration task cost before running: 97 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801120228600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "433e42a5-1823-4b54-b723-f92f14533433", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801125908300, "endTime": 5801134330000}, "additional": {"children": [], "state": "success", "detailId": "daf96de3-ad6a-4058-a8eb-6bedd791cd3d", "logId": "5700e91c-4e8c-45ef-8c9e-d42ce6635611"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "daf96de3-ad6a-4058-a8eb-6bedd791cd3d", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801121103900}, "additional": {"logType": "detail", "children": [], "durationId": "433e42a5-1823-4b54-b723-f92f14533433"}}, {"head": {"id": "43a56a1f-0af3-46c1-a6d4-31a3afd421da", "name": "entry : default@PreBuild start {\n  rss: 197435392,\n  heapTotal: 133369856,\n  heapUsed: 108800432,\n  external: 1820854,\n  arrayBuffers: 855498\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801125868000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b65098b8-f6f5-4926-a4cb-ed4a727ec32c", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801125925500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bfb289b-0175-4959-8b64-22b7fb5dfa19", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801134060400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff4928be-a082-489e-9975-99a91dd77167", "name": "entry : default@PreBuild end {\n  rss: 197808128,\n  heapTotal: 133369856,\n  heapUsed: 108978984,\n  external: 1829046,\n  arrayBuffers: 863690\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801134204900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5700e91c-4e8c-45ef-8c9e-d42ce6635611", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801125908300, "endTime": 5801134330000}, "additional": {"logType": "info", "children": [], "durationId": "433e42a5-1823-4b54-b723-f92f14533433"}}, {"head": {"id": "75538489-b1e9-4366-b4e6-8bba2700503e", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801137726200, "endTime": 5801165331200}, "additional": {"children": [], "state": "success", "detailId": "985922e3-6d88-4ed5-a716-9c3cf4969126", "logId": "28a0a25d-190d-46b7-9f2d-d6db9c3870d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "985922e3-6d88-4ed5-a716-9c3cf4969126", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801136622800}, "additional": {"logType": "detail", "children": [], "durationId": "75538489-b1e9-4366-b4e6-8bba2700503e"}}, {"head": {"id": "9adcb391-c509-4b22-a84f-dcc29629ac0a", "name": "entry : default@GenerateMetadata start {\n  rss: 198041600,\n  heapTotal: 133369856,\n  heapUsed: 109216984,\n  external: 1829046,\n  arrayBuffers: 863690\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801137706900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "096c6134-6e3d-42dd-b058-9f168c42f58c", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801137738900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1493d533-d18b-41ba-bcfb-083d09277d99", "name": "entry:default@GenerateMetadata is not up-to-date, since the output file 'D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801155045300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16555130-632e-4f25-86c5-8f52dfed96ad", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801155102400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5055430-b377-43fd-a696-adca52aeb6ec", "name": "entry : default@GenerateMetadata end {\n  rss: 202616832,\n  heapTotal: 133369856,\n  heapUsed: 110537496,\n  external: 1886390,\n  arrayBuffers: 921034\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801165289100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28a0a25d-190d-46b7-9f2d-d6db9c3870d0", "name": "Finished :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801137726200, "endTime": 5801165331200}, "additional": {"logType": "info", "children": [], "durationId": "75538489-b1e9-4366-b4e6-8bba2700503e"}}, {"head": {"id": "713f2f96-a4ba-44ab-94d7-e3919f43309c", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801170069300, "endTime": 5801173599800}, "additional": {"children": [], "state": "success", "detailId": "728db724-d290-40c8-b41d-6b6148ac6733", "logId": "5e8cda4b-1d39-484b-8aeb-32ca167962a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "728db724-d290-40c8-b41d-6b6148ac6733", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801169404200}, "additional": {"logType": "detail", "children": [], "durationId": "713f2f96-a4ba-44ab-94d7-e3919f43309c"}}, {"head": {"id": "31d10826-205b-4984-a393-bcb3cf249755", "name": "entry : default@CreateBuildProfile start {\n  rss: 202739712,\n  heapTotal: 133369856,\n  heapUsed: 110995632,\n  external: 1894582,\n  arrayBuffers: 929226\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801170056800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fe6a5cd-f9ec-4905-8cdb-54c9f55f9adf", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801170079000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27ad68b9-731c-46fc-a6b8-7a2feac8c830", "name": "runTaskFromQueue task cost before running: 148 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801170595100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce8d4dcd-1574-4543-8eec-e65c834e6ae5", "name": "entry:default@CreateBuildProfile is not up-to-date, since the output file 'D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801172083700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a51e08d-e7bd-4ad5-b30a-076adacf7f84", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801172115200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7334ddb-c73c-4b79-8d7e-6f25e62fdfa3", "name": "entry : default@CreateBuildProfile end {\n  rss: 203214848,\n  heapTotal: 133369856,\n  heapUsed: 111122152,\n  external: 1902774,\n  arrayBuffers: 937418\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801173565700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e8cda4b-1d39-484b-8aeb-32ca167962a0", "name": "Finished :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801170069300, "endTime": 5801173599800}, "additional": {"logType": "info", "children": [], "durationId": "713f2f96-a4ba-44ab-94d7-e3919f43309c"}}, {"head": {"id": "f8b1fe66-a848-413e-95bb-8161e50c85d2", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801175727600, "endTime": 5802590301000}, "additional": {"children": ["0efb995c-b75f-455e-afd5-8267a57c2dd3", "af8e2267-86f7-4ef6-8538-95e1b9e4847b", "7482009a-1a5c-4ebc-9d65-bdfcbf88cade"], "state": "success", "detailId": "08189559-483d-46ab-a916-b59459e376d8", "logId": "0774e7ce-2339-4d9c-838a-5324c88c961a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "08189559-483d-46ab-a916-b59459e376d8", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801175210500}, "additional": {"logType": "detail", "children": [], "durationId": "f8b1fe66-a848-413e-95bb-8161e50c85d2"}}, {"head": {"id": "ec6f14fc-534f-44e5-bdc3-33dd5bf08366", "name": "entry : default@BuildNativeWithCmake start {\n  rss: 203587584,\n  heapTotal: 133369856,\n  heapUsed: 111355480,\n  external: 1902774,\n  arrayBuffers: 937418\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801175714700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e8582d1-ffcd-434c-99a0-f3ac46198013", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801175740800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88d47254-45d9-41bf-a4b9-206412052ff4", "name": "runTaskFromQueue task cost before running: 153 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801175799500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e673847-accb-410b-a6f8-151db0824e38", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\arm64-v8a',\n  '-DOHOS_ARCH=arm64-v8a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\arm64-v8a',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=arm64-v8a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801177261400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cf69057-212f-469b-816f-42792a6e4f8c", "name": "default@BuildNativeWithCmake work[3] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801177902600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0efb995c-b75f-455e-afd5-8267a57c2dd3", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5801178321300, "endTime": 5802423964700}, "additional": {"children": [], "state": "success", "parent": "f8b1fe66-a848-413e-95bb-8161e50c85d2", "logId": "1c854f61-abc4-43cf-8a9e-71507e859224"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "da31329e-2262-478e-9a85-25fe86f911d2", "name": "default@BuildNativeWithCmake work[3] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801178260100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e43e05d2-9806-4099-8958-c005690feed1", "name": "default@BuildNativeWithCmake work[3] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801178329500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6958bf82-e340-4932-a19f-3e892479df37", "name": "default@BuildNativeWithCmake work[3] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801178347300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1faffbe-3bb7-4e03-b46f-6c7bdbd262f3", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\armeabi-v7a',\n  '-DOHOS_ARCH=armeabi-v7a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\armeabi-v7a',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=armeabi-v7a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801179613300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92af4306-7fac-4233-a939-f4183b6f17cd", "name": "default@BuildNativeWithCmake work[4] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801180693400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af8e2267-86f7-4ef6-8538-95e1b9e4847b", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker1", "startTime": 5801181759900, "endTime": 5802424912300}, "additional": {"children": [], "state": "success", "parent": "f8b1fe66-a848-413e-95bb-8161e50c85d2", "logId": "4ad758fb-7468-4624-ba42-6ec696c2e114"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "8586cd20-7f51-41a0-9363-a9a902cb0d72", "name": "default@BuildNativeWithCmake work[4] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801181286600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92f11be4-1abc-438a-8b86-dc8c551626a7", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801181305000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b54089e4-6891-41a1-a8ea-3ea881ebe19d", "name": "default@BuildNativeWithCmake work[4] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801181777200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84430166-fb31-4c14-bb2c-5c186839d721", "name": "default@BuildNativeWithCmake work[4] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801181808800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deef6ef8-003c-4c7c-8d27-a4f54d289a55", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\x86_64',\n  '-DOHOS_ARCH=x86_64',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\x86_64',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=x86_64',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801182890200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3710f5ad-b481-41a8-9a22-8796eb84f4dd", "name": "default@BuildNativeWithCmake work[5] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801183551000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7482009a-1a5c-4ebc-9d65-bdfcbf88cade", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5802424338000, "endTime": 5802590158400}, "additional": {"children": [], "state": "success", "parent": "f8b1fe66-a848-413e-95bb-8161e50c85d2", "logId": "fe674455-1a6b-457a-adb1-6b06006f2b2c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "1de0966b-5e5a-493f-bd66-71ea659d8870", "name": "default@BuildNativeWithCmake work[5] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801183932700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01c28c65-1c45-469b-af0d-58bcd2bef6d1", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801183945700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd42eab3-59ac-4f10-8414-3c32fa1045f4", "name": "A work dispatched to worker[1] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801183951400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e46fe872-441b-43f2-ad97-a8c481b64fee", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801183958600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "929855c6-9899-43be-a594-ce4d5d370512", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801183963300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2e3caa8-ecb4-4d86-8557-ec3494e89141", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801183966900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b4b0446-eb8c-415e-b3f0-568eafd87018", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801183996400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88350413-4053-4eab-8462-15a23a2afb89", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801184000400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "346e2bc3-e53a-441c-b3c4-4702cb168826", "name": "default@BuildNativeWithCmake work[5] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801184006400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ea1aba1-4ab1-46d1-ab87-5c989a5eacae", "name": "entry : default@BuildNativeWithCmake end {\n  rss: 204890112,\n  heapTotal: 133632000,\n  heapUsed: 112269656,\n  external: 1902774,\n  arrayBuffers: 937418\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801184049500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d0904b5-477c-4250-bff6-44c91a39da1a", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801188906000, "endTime": 5801197826800}, "additional": {"children": [], "state": "success", "detailId": "f0172158-41be-4c2e-b843-a5dcc96e322b", "logId": "9841be68-df50-485d-a9c7-cf01a01cab8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "f0172158-41be-4c2e-b843-a5dcc96e322b", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801184998000}, "additional": {"logType": "detail", "children": [], "durationId": "2d0904b5-477c-4250-bff6-44c91a39da1a"}}, {"head": {"id": "67130ae3-6b38-4107-babc-4849e0801e70", "name": "entry : default@GenerateLoaderJson start {\n  rss: 204914688,\n  heapTotal: 133632000,\n  heapUsed: 112648560,\n  external: 1902774,\n  arrayBuffers: 937418\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801188882200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d0e6775-aeb5-4fd2-9843-759a89930f75", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801188922700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdf61753-34e5-4a05-9a4d-ef90f7c79705", "name": "entry:default@GenerateLoaderJson is not up-to-date, since the output file 'D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader\\default\\loader.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801190872400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8127b5f-c522-4f39-b146-ab41870bd740", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801190920000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcd8c845-05ae-4327-a397-6d58fed39560", "name": "entry : default@GenerateLoaderJson end {\n  rss: 206086144,\n  heapTotal: 133632000,\n  heapUsed: 113042104,\n  external: 1910977,\n  arrayBuffers: 945621\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801197782700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9841be68-df50-485d-a9c7-cf01a01cab8a", "name": "Finished :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801188906000, "endTime": 5801197826800}, "additional": {"logType": "info", "children": [], "durationId": "2d0904b5-477c-4250-bff6-44c91a39da1a"}}, {"head": {"id": "ef8d8f8d-8a37-46a9-961a-701d236e0837", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801202069200, "endTime": 5801207382500}, "additional": {"children": [], "state": "success", "detailId": "50abb07d-da86-42c9-b19a-a688b465a8f2", "logId": "3917bdfc-80b8-4a5e-baae-646cc000fec4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "50abb07d-da86-42c9-b19a-a688b465a8f2", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801199524800}, "additional": {"logType": "detail", "children": [], "durationId": "ef8d8f8d-8a37-46a9-961a-701d236e0837"}}, {"head": {"id": "ae2733f8-37a6-4517-97b3-cfabbf3ff37a", "name": "entry : default@MergeProfile start {\n  rss: 208113664,\n  heapTotal: 133632000,\n  heapUsed: 113333640,\n  external: 1910977,\n  arrayBuffers: 945621\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801202045100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d135f6b-4277-47ca-a95f-6c3a992708e0", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801202082900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfd674d9-38af-4c6a-972a-e5047c53509d", "name": "runTaskFromQueue task cost before running: 179 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801202303100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a505fbd-0699-4862-b9cd-657dadc8e41e", "name": "entry:default@MergeProfile is not up-to-date, since the output file 'D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801203714400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d80420b-6b63-4cd1-b870-4128f44107b1", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801203801100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44a9604b-01e3-4197-983e-7e1f90eec386", "name": "Change app api release type with 'Release'", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801204230200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "277ebcf6-2eb2-4572-9d41-a3edc8785455", "name": "Change app compile API version with '********'", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801204425700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14297f29-44b3-4b6f-a86d-0311dc54e354", "name": "Change app target API version with '11'", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801204439800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ce6a077-4832-41bf-9009-299d297da361", "name": "Change app minimum API version with '11'", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801204450300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5acae79f-f5c5-411a-89cd-273fc589a07c", "name": "entry : default@MergeProfile end {\n  rss: 207298560,\n  heapTotal: 133632000,\n  heapUsed: 113482304,\n  external: 1919169,\n  arrayBuffers: 953813\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801207340200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3917bdfc-80b8-4a5e-baae-646cc000fec4", "name": "Finished :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801202069200, "endTime": 5801207382500}, "additional": {"logType": "info", "children": [], "durationId": "ef8d8f8d-8a37-46a9-961a-701d236e0837"}}, {"head": {"id": "e6a8517e-dec4-44c1-b26b-956cdfc2ab17", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801209943700, "endTime": 5801241353700}, "additional": {"children": [], "state": "success", "detailId": "67ef23eb-5daa-4b53-be38-2939459b55d8", "logId": "19eaa92b-7264-48fc-adc0-855ff41c52f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "67ef23eb-5daa-4b53-be38-2939459b55d8", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801208594800}, "additional": {"logType": "detail", "children": [], "durationId": "e6a8517e-dec4-44c1-b26b-956cdfc2ab17"}}, {"head": {"id": "bd265a52-564a-45f3-917b-a26fad81ce27", "name": "entry : default@MakePackInfo start {\n  rss: 207699968,\n  heapTotal: 133632000,\n  heapUsed: 113705848,\n  external: 1919169,\n  arrayBuffers: 953813\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801209911400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38fabb24-6a2a-4cd5-b638-a6381bb4fa6c", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801209966100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "939369cb-500c-4c06-8526-25d2b5b3c80e", "name": "runTaskFromQueue task cost before running: 193 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801216195900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d83eed6a-d9ed-4dd4-9e91-039cb71fd469", "name": "entry:default@MakePackInfo is not up-to-date, since the output file 'D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\pack.info' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801226466700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92422ae1-b7c3-401d-ac37-fac3df01c783", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801226531100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e8c9d0b-6152-46dc-9ab3-ef9d5b0d893a", "name": "Module Pack Info:  {\n  summary: {\n    app: {\n      bundleName: 'com.samples.camera',\n      bundleType: undefined,\n      version: [Object]\n    },\n    modules: [ [Object] ]\n  },\n  packages: [\n    {\n      deviceType: [Array],\n      moduleType: 'entry',\n      deliveryWithInstall: true,\n      name: 'entry-default'\n    }\n  ]\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801240189700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83fe248d-d90a-4c3d-a514-29b0710f3acc", "name": "entry : default@MakePackInfo end {\n  rss: 205377536,\n  heapTotal: 133672960,\n  heapUsed: 100457280,\n  external: 1061814,\n  arrayBuffers: 96458\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801241314600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19eaa92b-7264-48fc-adc0-855ff41c52f2", "name": "Finished :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801209943700, "endTime": 5801241353700}, "additional": {"logType": "info", "children": [], "durationId": "e6a8517e-dec4-44c1-b26b-956cdfc2ab17"}}, {"head": {"id": "ff827ef8-2779-47c4-85b4-9e839db16c5c", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801248372400, "endTime": 5801468299200}, "additional": {"children": [], "state": "success", "detailId": "24848f3d-dd8e-4fbf-8f1e-2fcff915f30c", "logId": "c9cbbbfb-d9f4-4af3-9147-1b185a34dca4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "24848f3d-dd8e-4fbf-8f1e-2fcff915f30c", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801246147900}, "additional": {"logType": "detail", "children": [], "durationId": "ff827ef8-2779-47c4-85b4-9e839db16c5c"}}, {"head": {"id": "8c999b03-bd60-4488-86ae-785ef2455b36", "name": "entry : default@ProcessProfile start {\n  rss: 205619200,\n  heapTotal: 133672960,\n  heapUsed: 100867744,\n  external: 1078198,\n  arrayBuffers: 112842\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801248353800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e28354bd-3119-45e9-b181-6e1886bab190", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801248385000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8d327b8-27ab-4fa7-a902-db1a4e89e05a", "name": "runTaskFromQueue task cost before running: 226 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801248525300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5460a043-c8d8-4f22-afeb-97b5cf08662e", "name": "entry:default@ProcessProfile is not up-to-date, since the input file 'D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801248900100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "006bd3bd-2360-4a1a-8c0c-a48834332e5c", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801248927900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9ea5559-00a4-4c58-99db-3dade5267b55", "name": "********", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801466592200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a59e292-cee6-4878-b678-d5fc52c3d306", "name": "entry : default@ProcessProfile end {\n  rss: 289009664,\n  heapTotal: 133672960,\n  heapUsed: 102896368,\n  external: 1133734,\n  arrayBuffers: 168378\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801468139600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9cbbbfb-d9f4-4af3-9147-1b185a34dca4", "name": "Finished :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801248372400, "endTime": 5801468299200}, "additional": {"logType": "info", "children": [], "durationId": "ff827ef8-2779-47c4-85b4-9e839db16c5c"}}, {"head": {"id": "8ef86a4b-d4f8-42b6-91b3-d2c132a84bbf", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801471964400, "endTime": 5801481312700}, "additional": {"children": [], "state": "success", "detailId": "934c257a-79a7-450b-a3bc-e23f7a9374b0", "logId": "aab69a1b-e60b-4900-8ad5-bcafeeedb1e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "934c257a-79a7-450b-a3bc-e23f7a9374b0", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801469454700}, "additional": {"logType": "detail", "children": [], "durationId": "8ef86a4b-d4f8-42b6-91b3-d2c132a84bbf"}}, {"head": {"id": "84bb11d1-2e74-4d7c-b4f5-a7bb94987b05", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801471476300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a238edbe-10d7-4677-b145-c2e2fd5eb7de", "name": "entry : default@ProcessResource start {\n  rss: 290852864,\n  heapTotal: 133672960,\n  heapUsed: 103134456,\n  external: 1133734,\n  arrayBuffers: 168378\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801471947900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a186b05-85ec-4570-b463-a94242c2c1a3", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801475895700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f9affb3-08d1-42c4-b307-631b5256fbe2", "name": "runTaskFromQueue task cost before running: 453 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801476080500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb774ebd-f20d-4f35-8f5d-0c29fde0228d", "name": "entry:default@ProcessResource is not up-to-date, since the output file 'D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801476600900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "732c351e-97a5-417a-8a19-15b52836a9f1", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801476625800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bdad6b6-94a6-4e6f-ae8e-9cc9d1990a8c", "name": "entry : default@ProcessResource end {\n  rss: 292175872,\n  heapTotal: 133672960,\n  heapUsed: 103410656,\n  external: 1150118,\n  arrayBuffers: 184762\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801477588300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aab69a1b-e60b-4900-8ad5-bcafeeedb1e0", "name": "Finished :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801471964400, "endTime": 5801481312700}, "additional": {"logType": "info", "children": [], "durationId": "8ef86a4b-d4f8-42b6-91b3-d2c132a84bbf"}}, {"head": {"id": "5bb586ab-6160-4594-9262-169c4fc27416", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801489227700, "endTime": 5801801831200}, "additional": {"children": [], "state": "success", "detailId": "6978b643-ea61-4dd0-b348-a63f760f2d21", "logId": "05ada3f4-2988-483f-a91f-3b98025d6c23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "6978b643-ea61-4dd0-b348-a63f760f2d21", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801486338100}, "additional": {"logType": "detail", "children": [], "durationId": "5bb586ab-6160-4594-9262-169c4fc27416"}}, {"head": {"id": "b034f585-59b5-4627-9915-c286f1ab088d", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801487874100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "226e8de9-8f5f-4e3e-a037-a624f9b3606a", "name": "entry : default@CompileResource start {\n  rss: 295665664,\n  heapTotal: 133672960,\n  heapUsed: 104186296,\n  external: 1174694,\n  arrayBuffers: 209338\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801489211100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "391d4a69-9494-4652-b743-1a55aa039248", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801489319100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4c5e66a-ffb9-4871-b4ce-79c85558851b", "name": "runTaskFromQueue task cost before running: 467 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801489575300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23dff1d0-90b1-4b4e-8623-b4643f95bfc0", "name": "entry:default@CompileResource is not up-to-date, since the input file 'D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801522929500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4acdf51d-6dd9-4198-b062-e34ccfc77e23", "name": "Incremental task entry:default@CompileResource pre-execution cost: 34 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801522984000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51e807c2-5212-4eca-bbc6-2a8fb2b3b48e", "name": "Use tool [D:\\harmonyFor\\openSDK\\11\\toolchains\\restool.exe]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\toolchains\\\\restool.exe',\n  '-l',\n  'D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resConfig.json'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801526769800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14954ad3-cbaf-4e2e-9322-ed726d9ddf9e", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801792779500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "560b468a-6b95-42f2-a027-1ae246f65c20", "name": "entry : default@CompileResource end {\n  rss: 284164096,\n  heapTotal: 133672960,\n  heapUsed: 105166496,\n  external: 1183012,\n  arrayBuffers: 217656\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801801721800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05ada3f4-2988-483f-a91f-3b98025d6c23", "name": "Finished :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801489227700, "endTime": 5801801831200}, "additional": {"logType": "info", "children": [], "durationId": "5bb586ab-6160-4594-9262-169c4fc27416"}}, {"head": {"id": "7759694f-fcc1-4781-898d-fe5c92097f16", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801811358800, "endTime": 5813988835100}, "additional": {"children": ["337594df-800d-4d85-837a-a1f82fa2c741"], "state": "success", "detailId": "4aa9fc49-b444-4a01-817f-83d20cbfc012", "logId": "f594f516-aa3e-4a52-94d9-59320e61ae39"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "4aa9fc49-b444-4a01-817f-83d20cbfc012", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801804930500}, "additional": {"logType": "detail", "children": [], "durationId": "7759694f-fcc1-4781-898d-fe5c92097f16"}}, {"head": {"id": "a68c468e-6d6b-4571-a545-f716c93b26e9", "name": "entry : default@CompileArkTS start {\n  rss: 285323264,\n  heapTotal: 133672960,\n  heapUsed: 105541872,\n  external: 1183012,\n  arrayBuffers: 217656\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801811319700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "842e4ace-0087-4164-8320-23e42f38084f", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801811383600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff7acafb-8d98-4101-bc7c-c9b2411ca3e1", "name": "Obfuscation config only effect in release mode.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801818699800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "189cc1bc-fe45-44b1-8c3e-d289f9e08ab1", "name": "runTaskFromQueue task cost before running: 796 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801818775500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8f16858-38f6-4185-9180-9a5762373805", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801825642700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b830ab6-63fa-48b8-9474-ad0a811c1623", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801825697100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c48c6761-d6a8-44fd-bacd-e12b9f213ebf", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801835319400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ac01be7-05b7-4f83-b818-da58b8ab3b53", "name": "default@CompileArkTS work[6] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801836240600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "337594df-800d-4d85-837a-a1f82fa2c741", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker2", "startTime": 5801838436400, "endTime": 5813988658400}, "additional": {"children": ["6b78f4c2-53c5-45ae-8a11-91dc468447cc", "cd310ee3-52e0-4924-95b6-fa39e2df80bf", "62bf4e5d-f146-43ca-b7ad-59133b61a2cb", "7bc3ccc8-e35d-48c5-9824-a6c35391ec10", "ba5943e6-6985-4828-83ba-c69b1ae55bb2", "58b5c42e-c7a6-408b-ac56-feb28c7df456"], "state": "success", "parent": "7759694f-fcc1-4781-898d-fe5c92097f16", "logId": "5d27f36a-f15b-496d-87b2-dd664ae27806"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "2b72bc6a-c51c-4e2a-b220-fc0793ea6a24", "name": "default@CompileArkTS work[6] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801837319400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4242b11c-48fd-4599-8c07-d9a3e1c6dd34", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801837360700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4bb5f3a-4656-4b4c-a6ed-1fa98a13335b", "name": "A work dispatched to worker[1] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801837373500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2efaa5ee-c1c6-456e-ab11-71833442adfd", "name": "default@CompileArkTS work[6] has been dispatched to worker[2].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801838458300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "668978b1-6f93-4ed3-a0ba-b3fbfde49c3d", "name": "default@CompileArkTS work[6] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801838504800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11e2550c-5c57-4deb-9f69-431f3a6b10a4", "name": "entry : default@CompileArkTS end {\n  rss: 284348416,\n  heapTotal: 133672960,\n  heapUsed: 106553144,\n  external: 1207613,\n  arrayBuffers: 242257\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801838697400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82f371e3-3a01-4e2b-b095-8cb43a23c5d6", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801845977000, "endTime": 5801855105000}, "additional": {"children": [], "state": "success", "detailId": "bc500370-5206-480c-babd-016aa93f9b64", "logId": "c72e9620-0eca-425c-b32f-5cfe096a3794"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "bc500370-5206-480c-babd-016aa93f9b64", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801840274100}, "additional": {"logType": "detail", "children": [], "durationId": "82f371e3-3a01-4e2b-b095-8cb43a23c5d6"}}, {"head": {"id": "3baaafc6-a3be-4180-a4c6-dd482054c8cc", "name": "entry : default@BuildJS start {\n  rss: 289296384,\n  heapTotal: 133672960,\n  heapUsed: 106860904,\n  external: 1207613,\n  arrayBuffers: 242257\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801845947600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fb0a8f5-4344-4656-839f-aee69c23027b", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801845991900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc0ed8b0-ce38-456a-895e-c61743c9f4e8", "name": "entry : default@BuildJS end {\n  rss: 291364864,\n  heapTotal: 133672960,\n  heapUsed: 107393904,\n  external: 1215805,\n  arrayBuffers: 250449\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801855045000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c72e9620-0eca-425c-b32f-5cfe096a3794", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801845977000, "endTime": 5801855105000}, "additional": {"logType": "info", "children": [], "durationId": "82f371e3-3a01-4e2b-b095-8cb43a23c5d6"}}, {"head": {"id": "3295d640-11f5-44c5-95e7-746739ceb267", "name": "runTaskFromQueue task cost before running: 832 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801855381200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11a99d70-d384-42a0-9d8d-ca2f48d1292f", "name": "default@BuildNativeWithCmake work[3] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802424042800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c854f61-abc4-43cf-8a9e-71507e859224", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5801178321300, "endTime": 5802423964700}, "additional": {"logType": "info", "children": [], "durationId": "0efb995c-b75f-455e-afd5-8267a57c2dd3", "parent": "0774e7ce-2339-4d9c-838a-5324c88c961a"}}, {"head": {"id": "27b76910-3885-4717-9ac1-1a0aa5e33fba", "name": "default@BuildNativeWithCmake work[5] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802424365100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "beb9768c-1766-4950-baa8-8f6a24c0291c", "name": "default@BuildNativeWithCmake work[4] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802424939200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad758fb-7468-4624-ba42-6ec696c2e114", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker1", "startTime": 5801181759900, "endTime": 5802424912300}, "additional": {"logType": "info", "children": [], "durationId": "af8e2267-86f7-4ef6-8538-95e1b9e4847b", "parent": "0774e7ce-2339-4d9c-838a-5324c88c961a"}}, {"head": {"id": "e6b74616-1ca1-4c4d-9963-7c0321a6af76", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802425026900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95313f19-b495-4a8f-98ec-4f3013abe0ea", "name": "default@BuildNativeWithCmake work[5] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802590229000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe674455-1a6b-457a-adb1-6b06006f2b2c", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5802424338000, "endTime": 5802590158400}, "additional": {"logType": "info", "children": [], "durationId": "7482009a-1a5c-4ebc-9d65-bdfcbf88cade", "parent": "0774e7ce-2339-4d9c-838a-5324c88c961a"}}, {"head": {"id": "0774e7ce-2339-4d9c-838a-5324c88c961a", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801175727600, "endTime": 5802590301000}, "additional": {"logType": "info", "children": ["1c854f61-abc4-43cf-8a9e-71507e859224", "4ad758fb-7468-4624-ba42-6ec696c2e114", "fe674455-1a6b-457a-adb1-6b06006f2b2c"], "durationId": "f8b1fe66-a848-413e-95bb-8161e50c85d2"}}, {"head": {"id": "ac7b56da-e171-4c5e-b694-2582732d259a", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802593655300, "endTime": 5806259914900}, "additional": {"children": ["38289502-0692-4d91-8947-c53af9220106", "39d76d3f-200b-49ef-931f-8f5bfb7ce8b8", "03faa958-0da7-4ca1-af3a-1da511b53050"], "state": "success", "detailId": "05d91277-2a9a-4395-b070-74e8729aa773", "logId": "4b5472b8-6225-4615-9f07-e9d65842d8a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "05d91277-2a9a-4395-b070-74e8729aa773", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802592601000}, "additional": {"logType": "detail", "children": [], "durationId": "ac7b56da-e171-4c5e-b694-2582732d259a"}}, {"head": {"id": "ce4e6807-559b-4019-aa56-e40ddd6c4b22", "name": "entry : default@BuildNativeWithNinja start {\n  rss: 357826560,\n  heapTotal: 133672960,\n  heapUsed: 107645824,\n  external: 1215805,\n  arrayBuffers: 250449\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802593632500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02b33fd3-474e-4a2b-8e7b-e6835c63e36c", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802593668700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67eaa587-3c82-4ec3-bb39-b62566aea866", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802599981300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d551e058-c23b-4809-841b-f02b610079bd", "name": "Use tool [Ninja]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '-C',\n  'D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\arm64-v8a'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802603695800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42436c2d-ca70-43d3-9632-57291c01e9f6", "name": "default@BuildNativeWithNinja work[7] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802604180300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38289502-0692-4d91-8947-c53af9220106", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5802604727400, "endTime": 5804765829700}, "additional": {"children": [], "state": "success", "parent": "ac7b56da-e171-4c5e-b694-2582732d259a", "logId": "eebeb29a-5f59-492a-a679-c66e1fe9e261"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "96fc142e-529e-44b2-968a-3faf90595dc5", "name": "default@BuildNativeWithNinja work[7] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802604662800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a80ccaa-14cb-4861-8970-591815fb7ae6", "name": "default@BuildNativeWithNinja work[7] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802604737600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cad7b819-a4f6-4699-b1d7-f3c5cfacc876", "name": "default@BuildNativeWithNinja work[7] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802604768100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "334e8133-0b5f-4d1d-bfcb-1605a763e19d", "name": "Use tool [Ninja]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '-C',\n  'D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\armeabi-v7a'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802617590900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76e605eb-e0ff-4928-9ba2-cac0a87fc657", "name": "default@BuildNativeWithNinja work[8] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802618203900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39d76d3f-200b-49ef-931f-8f5bfb7ce8b8", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker1", "startTime": 5802619021200, "endTime": 5804766875600}, "additional": {"children": [], "state": "success", "parent": "ac7b56da-e171-4c5e-b694-2582732d259a", "logId": "696ebf11-f7e1-4e50-803a-43d5ce118682"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "0ca10661-9007-4921-b184-8afa9ba74a67", "name": "default@BuildNativeWithNinja work[8] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802618808300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f960160-019b-4bed-9ca2-639c3f1187b0", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802618823500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7287694a-7e12-4820-b571-2a797d25bf12", "name": "default@BuildNativeWithNinja work[8] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802619038500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48281c13-415e-4ef4-90ef-104a5b44a0b6", "name": "default@BuildNativeWithNinja work[8] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802619065200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7092cbb5-a7f8-4bf4-8f44-c122363f0ddf", "name": "Use tool [Ninja]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '-C',\n  'D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\x86_64'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802629774000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23953587-ea1b-4971-a41c-9b57d91cc301", "name": "default@BuildNativeWithNinja work[9] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802630295100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03faa958-0da7-4ca1-af3a-1da511b53050", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5804766562400, "endTime": 5806259756000}, "additional": {"children": [], "state": "success", "parent": "ac7b56da-e171-4c5e-b694-2582732d259a", "logId": "7ad1a264-42c9-43f2-aace-882cca962f8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "82c9b24f-4869-40b4-b4f5-4384bb3b03eb", "name": "default@BuildNativeWithNinja work[9] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802630734000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8e1e136-9f33-4eb8-ad2d-dca05ecf19ca", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802630759700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "680acbbf-bb9e-4a23-9eac-f8a41edb1b1f", "name": "A work dispatched to worker[1] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802630767100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "498d95d3-bbf7-480f-8448-e9a83<PERSON><PERSON>a9", "name": "A work dispatched to worker[2] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802630772600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2493a59a-a3c0-41f7-9fa4-abca9e4c1365", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802630784600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5effbb13-86f8-4d6f-a75f-8842abfe8b0d", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802630791900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d40a8e4-d09b-4d8c-b756-b3d8720b03f1", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802630798300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b10c0b4-17d9-4b7c-9135-b40226544c32", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802630850200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29d57257-f97b-4e36-b1bd-5aa23c42b8cb", "name": "default@BuildNativeWithNinja work[9] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802630863300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "393677b0-2f7a-4612-bc64-6009ebf5a79a", "name": "entry : default@BuildNativeWithNinja end {\n  rss: 359960576,\n  heapTotal: 133672960,\n  heapUsed: 109051552,\n  external: 1240462,\n  arrayBuffers: 275106\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802639153100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f2beeae-9247-429c-abe8-d601ab393d1f", "name": "default@BuildNativeWithNinja work[7] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5804765925200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eebeb29a-5f59-492a-a679-c66e1fe9e261", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5802604727400, "endTime": 5804765829700}, "additional": {"logType": "info", "children": [], "durationId": "38289502-0692-4d91-8947-c53af9220106", "parent": "4b5472b8-6225-4615-9f07-e9d65842d8a7"}}, {"head": {"id": "34fb821f-2987-48cb-b83a-458825badfa0", "name": "default@BuildNativeWithNinja work[9] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5804766603900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79511de9-87ae-474d-a1a9-1183bac6c9f1", "name": "default@BuildNativeWithNinja work[8] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5804766904400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "696ebf11-f7e1-4e50-803a-43d5ce118682", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker1", "startTime": 5802619021200, "endTime": 5804766875600}, "additional": {"logType": "info", "children": [], "durationId": "39d76d3f-200b-49ef-931f-8f5bfb7ce8b8", "parent": "4b5472b8-6225-4615-9f07-e9d65842d8a7"}}, {"head": {"id": "260f986e-416e-44e0-bd91-5decefb1050f", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5804766962000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e77f461-d201-41e1-af4b-8a4b28fba28b", "name": "default@BuildNativeWithNinja work[9] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806259829200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ad1a264-42c9-43f2-aace-882cca962f8a", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5804766562400, "endTime": 5806259756000}, "additional": {"logType": "info", "children": [], "durationId": "03faa958-0da7-4ca1-af3a-1da511b53050", "parent": "4b5472b8-6225-4615-9f07-e9d65842d8a7"}}, {"head": {"id": "4b5472b8-6225-4615-9f07-e9d65842d8a7", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802593655300, "endTime": 5806259914900}, "additional": {"logType": "info", "children": ["eebeb29a-5f59-492a-a679-c66e1fe9e261", "696ebf11-f7e1-4e50-803a-43d5ce118682", "7ad1a264-42c9-43f2-aace-882cca962f8a"], "durationId": "ac7b56da-e171-4c5e-b694-2582732d259a"}}, {"head": {"id": "969343f9-5490-4dd1-8193-7e411f911299", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806266209200, "endTime": 5806338433300}, "additional": {"children": [], "state": "success", "detailId": "8b1fe8e2-5992-4812-9663-7cdfbe90321e", "logId": "592b5ab3-a64a-468c-a33a-f383a2ca5a33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "8b1fe8e2-5992-4812-9663-7cdfbe90321e", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806262815900}, "additional": {"logType": "detail", "children": [], "durationId": "969343f9-5490-4dd1-8193-7e411f911299"}}, {"head": {"id": "6fbd2f37-b84b-4982-9f53-66db7e722f7f", "name": "entry : default@ProcessLibs start {\n  rss: 468013056,\n  heapTotal: 133672960,\n  heapUsed: 109597088,\n  external: 1240462,\n  arrayBuffers: 275106\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806266180000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10a80e13-320a-4d66-ad7b-5e46baf89fae", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806266219600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa9ee4c6-a9aa-4fa8-b970-fa3e45b559e9", "name": "entry:default@ProcessLibs is not up-to-date, since the output file 'D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806302187100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6de536c-3213-47f7-b18f-6c86cd59affc", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806302237100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1428d79-4eb9-4569-b89d-a2940fdf57a5", "name": "Libs: D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\cmake\\default\\obj\r\nD:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\libs", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806302881000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a8f65b0-6d3c-40bf-8890-851c50520d28", "name": "Collect files: x86_64\\libentry.so,x86_64\\libc++_shared.so,armeabi-v7a\\libentry.so,armeabi-v7a\\libc++_shared.so,arm64-v8a\\libentry.so,arm64-v8a\\libc++_shared.so", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806318580900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "305ff792-3dd2-4e16-899e-331334cfdd48", "name": "Collect files: ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806320220000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2be65a7-dd76-4917-93c6-c085e55fdea7", "name": "entry : default@ProcessLibs end {\n  rss: 469975040,\n  heapTotal: 134459392,\n  heapUsed: 111521920,\n  external: 6305139,\n  arrayBuffers: 5339783\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806338398700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "592b5ab3-a64a-468c-a33a-f383a2ca5a33", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806266209200, "endTime": 5806338433300}, "additional": {"logType": "info", "children": [], "durationId": "969343f9-5490-4dd1-8193-7e411f911299"}}, {"head": {"id": "0e35949d-93a0-4b08-8105-ffe1f6041fee", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806342674100, "endTime": 5806361314300}, "additional": {"children": [], "state": "success", "detailId": "99d235fb-d36c-4125-bbb0-af21f84ad458", "logId": "63b5efb4-4c4c-4224-94bd-b7148a6c51d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "99d235fb-d36c-4125-bbb0-af21f84ad458", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806340381200}, "additional": {"logType": "detail", "children": [], "durationId": "0e35949d-93a0-4b08-8105-ffe1f6041fee"}}, {"head": {"id": "487447f0-bedd-498a-ac8f-5912ab9b07f1", "name": "entry : default@DoNativeStrip start {\n  rss: 470016000,\n  heapTotal: 134459392,\n  heapUsed: 111810040,\n  external: 6305139,\n  arrayBuffers: 5339783\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806342657400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f17d8086-ba2c-43e5-966d-cd4c3e854b74", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806342683200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6718e887-fd1f-4c7a-b6d5-b31d06b51e5a", "name": "runTaskFromQueue task cost before running: 5 s 320 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806343024400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62b0fcdc-cd84-402b-b412-14403545e60e", "name": "entry:default@DoNativeStrip is not up-to-date, since the input file 'D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806345468700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdaf2c8a-8d6a-4583-82a1-1cfd53b410d6", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806345513000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eb35312-ef60-48da-a5a8-4e8af071d59a", "name": "Start executing task ,{} DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806345736800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0c0cbe1-7e50-4a55-b243-4f30768ea786", "name": "do not need strip,now start copy so file in libs", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806346276000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fb8b01d-b8d3-4422-a4f7-2741716a6b4d", "name": "strip task all done", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806361159800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0e12608-ab02-4f37-bb93-b9a05ba78c23", "name": "do not need strip,copied so file in libs", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806361188700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6a3a350-c83b-4454-b2d3-296de38e0c7c", "name": "Task execution end,{} DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806361205000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cb190c8-793b-4dbd-a55e-843dbbd269b0", "name": "entry : default@DoNativeStrip end {\n  rss: 470822912,\n  heapTotal: 134459392,\n  heapUsed: 113138128,\n  external: 6306675,\n  arrayBuffers: 5341319\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806361278100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63b5efb4-4c4c-4224-94bd-b7148a6c51d6", "name": "Finished :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806342674100, "endTime": 5806361314300}, "additional": {"logType": "info", "children": [], "durationId": "0e35949d-93a0-4b08-8105-ffe1f6041fee"}}, {"head": {"id": "a07b6310-a23f-44ba-b4bb-a58376c6bc6b", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806364884300, "endTime": 5806429730600}, "additional": {"children": [], "state": "success", "detailId": "802dc57a-b235-46f1-abee-ec523a1ab14c", "logId": "b81dab7f-cbca-47be-9010-82080fd85004"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "802dc57a-b235-46f1-abee-ec523a1ab14c", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806362901700}, "additional": {"logType": "detail", "children": [], "durationId": "a07b6310-a23f-44ba-b4bb-a58376c6bc6b"}}, {"head": {"id": "8637307f-8e69-40a6-99f7-959005d303cd", "name": "entry : default@CacheNativeLibs start {\n  rss: 470876160,\n  heapTotal: 134459392,\n  heapUsed: 113402504,\n  external: 6314867,\n  arrayBuffers: 5349511\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806364854900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "719f57b6-0900-4d42-bb27-2da65b0b98d1", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806364892700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80136e7e-69b7-4a60-863f-bb78cdea9fc0", "name": "runTaskFromQueue task cost before running: 5 s 342 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806365340200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c18451e-377a-40eb-b340-926678adaafd", "name": "entry:default@CacheNativeLibs is not up-to-date, since the input file 'D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806367796500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37984273-2d23-4123-a4f8-e3af53b14892", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806367832400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dd0afdc-17e7-4ea1-9b16-27426370296f", "name": "cache-native-libs start", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806367929600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65133621-c567-41d7-8245-ca6e67eb6d9a", "name": "cache-native-libs end", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806429602200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "315886b9-5ab2-4fa7-81f0-6f4fa49ec5f8", "name": "entry : default@CacheNativeLibs end {\n  rss: 482795520,\n  heapTotal: 137867264,\n  heapUsed: 104068664,\n  external: 11168478,\n  arrayBuffers: 10203122\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806429694800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b81dab7f-cbca-47be-9010-82080fd85004", "name": "Finished :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806364884300, "endTime": 5806429730600}, "additional": {"logType": "info", "children": [], "durationId": "a07b6310-a23f-44ba-b4bb-a58376c6bc6b"}}, {"head": {"id": "ebc67c8f-2929-4671-b226-2c81be02c3d9", "name": "runTaskFromQueue task cost before running: 5 s 408 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5806430551000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de9b8212-8a82-467b-9df5-b382a7d026e0", "name": "hvigor daemon: Check daemon process existed in registry, daemonStatus=busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5812154664900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58b5c42e-c7a6-408b-ac56-feb28c7df456", "name": "load dependencies", "description": "Load module dependencies.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker2", "startTime": 5802094732600, "endTime": 5804036410800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "337594df-800d-4d85-837a-a1f82fa2c741", "logId": "d802ae84-939f-4f15-85f7-8c67ec7e6e45"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "95f2e3b0-d5ca-4eec-b0e2-fc72eeba8365", "name": "generate config", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5804039710400, "endTime": 5805900933300}, "additional": {"logType": "info", "children": [], "durationId": "6b78f4c2-53c5-45ae-8a11-91dc468447cc", "parent": "5d27f36a-f15b-496d-87b2-dd664ae27806"}}, {"head": {"id": "6b78f4c2-53c5-45ae-8a11-91dc468447cc", "name": "generate config", "description": "", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker2", "startTime": 5804039710400, "endTime": 5805900933300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "337594df-800d-4d85-837a-a1f82fa2c741", "logId": "95f2e3b0-d5ca-4eec-b0e2-fc72eeba8365"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "735724eb-9c70-4955-b273-2fe8c481dfb8", "name": "read cache", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5805901119600, "endTime": 5805944298200}, "additional": {"logType": "info", "children": [], "durationId": "cd310ee3-52e0-4924-95b6-fa39e2df80bf", "parent": "5d27f36a-f15b-496d-87b2-dd664ae27806"}}, {"head": {"id": "cd310ee3-52e0-4924-95b6-fa39e2df80bf", "name": "read cache", "description": "", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker2", "startTime": 5805901119600, "endTime": 5805944298200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "337594df-800d-4d85-837a-a1f82fa2c741", "logId": "735724eb-9c70-4955-b273-2fe8c481dfb8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "6284069a-a818-46ce-b1d3-699d6c6cac28", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5805944363800, "endTime": 5813813927700}, "additional": {"logType": "info", "children": [], "durationId": "62bf4e5d-f146-43ca-b7ad-59133b61a2cb", "parent": "5d27f36a-f15b-496d-87b2-dd664ae27806"}}, {"head": {"id": "62bf4e5d-f146-43ca-b7ad-59133b61a2cb", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker2", "startTime": 5805944363800, "endTime": 5813813927700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "337594df-800d-4d85-837a-a1f82fa2c741", "logId": "6284069a-a818-46ce-b1d3-699d6c6cac28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "7e06d08f-4a53-4f1b-b802-dc1682a87b11", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5813814216700, "endTime": 5813852989900}, "additional": {"logType": "info", "children": [], "durationId": "7bc3ccc8-e35d-48c5-9824-a6c35391ec10", "parent": "5d27f36a-f15b-496d-87b2-dd664ae27806"}}, {"head": {"id": "7bc3ccc8-e35d-48c5-9824-a6c35391ec10", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker2", "startTime": 5813814216700, "endTime": 5813852989900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "337594df-800d-4d85-837a-a1f82fa2c741", "logId": "7e06d08f-4a53-4f1b-b802-dc1682a87b11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "a843bb3f-60f5-4120-8fe0-50a89f01a7b5", "name": "wait async operations finished", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5813853036700, "endTime": 5813981398400}, "additional": {"logType": "info", "children": [], "durationId": "ba5943e6-6985-4828-83ba-c69b1ae55bb2", "parent": "5d27f36a-f15b-496d-87b2-dd664ae27806"}}, {"head": {"id": "ba5943e6-6985-4828-83ba-c69b1ae55bb2", "name": "wait async operations finished", "description": "", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker2", "startTime": 5813853036700, "endTime": 5813981398400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "337594df-800d-4d85-837a-a1f82fa2c741", "logId": "a843bb3f-60f5-4120-8fe0-50a89f01a7b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "d802ae84-939f-4f15-85f7-8c67ec7e6e45", "name": "load dependencies", "description": "Load module dependencies.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5802094732600, "endTime": 5804036410800}, "additional": {"logType": "info", "children": [], "durationId": "58b5c42e-c7a6-408b-ac56-feb28c7df456", "parent": "5d27f36a-f15b-496d-87b2-dd664ae27806"}}, {"head": {"id": "58b5c42e-c7a6-408b-ac56-feb28c7df456", "name": "load dependencies", "description": "Load module dependencies.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker2", "startTime": 5802094732600, "endTime": 5804036410800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "337594df-800d-4d85-837a-a1f82fa2c741", "logId": "d802ae84-939f-4f15-85f7-8c67ec7e6e45"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "f07799ce-61e0-4d41-9dbb-c0782a3723b7", "name": "default@CompileArkTS work[6] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5813988695700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d27f36a-f15b-496d-87b2-dd664ae27806", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker2", "startTime": 5801838436400, "endTime": 5813988658400}, "additional": {"logType": "info", "children": ["95f2e3b0-d5ca-4eec-b0e2-fc72eeba8365", "735724eb-9c70-4955-b273-2fe8c481dfb8", "6284069a-a818-46ce-b1d3-699d6c6cac28", "7e06d08f-4a53-4f1b-b802-dc1682a87b11", "a843bb3f-60f5-4120-8fe0-50a89f01a7b5", "d802ae84-939f-4f15-85f7-8c67ec7e6e45"], "durationId": "337594df-800d-4d85-837a-a1f82fa2c741", "parent": "f594f516-aa3e-4a52-94d9-59320e61ae39"}}, {"head": {"id": "f594f516-aa3e-4a52-94d9-59320e61ae39", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801811358800, "endTime": 5813988835100}, "additional": {"logType": "info", "children": ["5d27f36a-f15b-496d-87b2-dd664ae27806"], "durationId": "7759694f-fcc1-4781-898d-fe5c92097f16"}}, {"head": {"id": "cfa16f09-f575-4d0b-837d-9d5412f4b883", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5814001819700, "endTime": 5815088434900}, "additional": {"children": ["d7689e2d-1e8c-4da8-8fdf-24e7beee259a"], "state": "success", "detailId": "5a567eb1-bd5d-4e91-a8f9-5bc9fde8e4dc", "logId": "b18d754c-99dd-4e11-858f-157f6290c14b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "5a567eb1-bd5d-4e91-a8f9-5bc9fde8e4dc", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5813993345800}, "additional": {"logType": "detail", "children": [], "durationId": "cfa16f09-f575-4d0b-837d-9d5412f4b883"}}, {"head": {"id": "e8dcc426-c78a-4a5e-8f29-73f6f881d623", "name": "entry : default@PackageHap start {\n  rss: 751902720,\n  heapTotal: 136818688,\n  heapUsed: 85467680,\n  external: 1044197,\n  arrayBuffers: 79973\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5814001797200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16ae76f4-064d-4919-a44d-2a63dc659e6e", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5814001830900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dea265c-4b58-4d27-87ad-a6bdacc7d428", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5814007449700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eaf3ea0-95d0-4456-81f5-ec4abd364595", "name": "Incremental task entry:default@PackageHap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5814007496600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a1470d6-f421-4028-8164-db0f074dded7", "name": "Use tool [D:\\harmonyFor\\openSDK\\11\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=utf-8',\n  '-jar',\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5814008849300}, "additional": {"logType": "debug", "children": [], "durationId": "cfa16f09-f575-4d0b-837d-9d5412f4b883"}}, {"head": {"id": "b44f56dc-b785-478f-bfe5-8624f0d4eca3", "name": "default@PackageHap work[10] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5814009257500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7689e2d-1e8c-4da8-8fdf-24e7beee259a", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5814009709300, "endTime": 5815088332700}, "additional": {"children": [], "state": "success", "parent": "cfa16f09-f575-4d0b-837d-9d5412f4b883", "logId": "d9f91130-bb56-4124-b023-898d7d6049d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "a204647a-30ea-4903-b756-1cf16945519f", "name": "default@PackageHap work[10] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5814009644500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b99790dc-3e7a-4425-8025-daf9501399f6", "name": "default@PackageHap work[10] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5814009717900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ce78e3e-c877-4e98-a348-a34409922638", "name": "default@PackageHap work[10] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5814009733200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b92a679e-6fe5-41b6-9cef-fcb4ea9504c8", "name": "entry : default@PackageHap end {\n  rss: 751902720,\n  heapTotal: 136818688,\n  heapUsed: 86106800,\n  external: 1044197,\n  arrayBuffers: 79973\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5814009775100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "594d8bdc-0749-402d-ad9d-70f8fbc8a733", "name": "default@PackageHap work[10] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5815088368000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9f91130-bb56-4124-b023-898d7d6049d9", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5814009709300, "endTime": 5815088332700}, "additional": {"logType": "info", "children": [], "durationId": "d7689e2d-1e8c-4da8-8fdf-24e7beee259a", "parent": "b18d754c-99dd-4e11-858f-157f6290c14b"}}, {"head": {"id": "b18d754c-99dd-4e11-858f-157f6290c14b", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5814001819700, "endTime": 5815088434900}, "additional": {"logType": "info", "children": ["d9f91130-bb56-4124-b023-898d7d6049d9"], "durationId": "cfa16f09-f575-4d0b-837d-9d5412f4b883"}}, {"head": {"id": "abf07cb2-adca-4e79-8743-b7f7b9138893", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5815097210900, "endTime": 5818640868300}, "additional": {"children": [], "state": "success", "detailId": "71404b6b-7988-414a-bf96-47180fd94088", "logId": "8635c454-fa56-4b4a-b349-ba6247c3763a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "71404b6b-7988-414a-bf96-47180fd94088", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5815091036500}, "additional": {"logType": "detail", "children": [], "durationId": "abf07cb2-adca-4e79-8743-b7f7b9138893"}}, {"head": {"id": "f54b4499-c69c-4905-bbd6-825ce5766885", "name": "entry : default@SignHap start {\n  rss: 689479680,\n  heapTotal: 89632768,\n  heapUsed: 84499600,\n  external: 1035806,\n  arrayBuffers: 71582\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5815097192100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10b65289-9c5a-47a4-82e9-1c3a8117f29c", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5815097220200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3323f834-1627-4027-b50d-61fb1fd17cec", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5815097731500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2471d3a6-f610-4c4b-8636-0e72d0fc0b5c", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5815100178200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4801ecd8-7525-422b-b7df-a90e0b97a819", "name": "Incremental task entry:default@SignHap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5815100216000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e873320f-d202-48dd-b8a6-8843b3911607", "name": "Use tool [D:\\harmonyFor\\openSDK\\11\\toolchains\\lib\\hap-sign-tool.jar]\n [\n  'java',\n  '-jar',\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\toolchains\\\\lib\\\\hap-sign-tool.jar',\n  'sign-app',\n  '-mode',\n  'localSign',\n  '-keystoreFile',\n  'C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.p12',\n  '-keystorePwd',\n  '******',\n  '-keyAlias',\n  'debugKey',\n  '-keyPwd',\n  '******',\n  '-signAlg',\n  'SHA256withECDSA',\n  '-profileFile',\n  'C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.p7b',\n  '-appCertFile',\n  'C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.cer',\n  '-inFile',\n  'D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '-outFile',\n  'D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-signed.hap'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5815124465500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd09fdf0-bf80-4f5c-81d2-a617df87c6a5", "name": "06-23 20:57:46 INFO  - Start verify-profile\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5816123513300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e6570f3-dffd-42ee-b991-1204754cfe00", "name": "06-23 20:57:46 INFO  - verify-profile success\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5816589815500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ccd67de-e5e6-4d83-bd75-377c63d243ac", "name": "06-23 20:57:47 INFO  - Start sign-app\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5817591397400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56454c1c-dbad-4b5b-a18b-47e2889e50e5", "name": "06-23 20:57:47 INFO  - certificate in profile: ide_demo_app\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5817896220300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bbae8f6-e6ff-45ce-9cc8-b899dd90c1d6", "name": "06-23 20:57:48 INFO  - Start to sign code.\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818081074700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "113953d8-8599-44cf-aad3-8699bd726bf7", "name": "06-23 20:57:48 INFO  - C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.p12 is exist. Try to load it with given passwd\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818215463700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bdc38e2-7fbd-432d-9937-c7d18e21d8b8", "name": "06-23 20:57:48 INFO  - Create a sign info successfully.\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818447412900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9179637-aeb0-44eb-a41f-2053bf14f669", "name": "06-23 20:57:48 INFO  - Create a sign info successfully.\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818471280500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3043410d-03b6-4018-9c5f-0ed19e7ad3e3", "name": "06-23 20:57:48 INFO  - Create a sign info successfully.\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818481647400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "507d58cd-76cd-4de9-847c-7c432b28e91e", "name": "06-23 20:57:48 INFO  - Create a sign info successfully.\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818497625900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e6041e4-5a14-4b4b-9c52-58ac723035c6", "name": "06-23 20:57:48 INFO  - Create a sign info successfully.\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818507072500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b7b5781-58c7-471d-81ff-0879e180c180", "name": "06-23 20:57:48 INFO  - Create a sign info successfully.\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818523787700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc19af28-f701-4dcc-9117-9d3a2ac9146b", "name": "06-23 20:57:48 INFO  - Create a sign info successfully.\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818532598400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d54b8d8-96a5-4590-bd4d-3acdc655ed2a", "name": "06-23 20:57:48 INFO  - Sign successfully.\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818539547700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ff64761-353c-43e6-8678-07fb1219c221", "name": "06-23 20:57:48 INFO  - Add sign data in sign info list success.\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818574721700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e5ce6da-a12d-42e5-bc36-13bf7e84061f", "name": "06-23 20:57:48 INFO  - Generate signing block success, begin write it to output file\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818579558200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0898e4f7-d41f-4dba-91ae-a5022397138c", "name": "06-23 20:57:48 INFO  - Sign Hap success!\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818581173300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef8354b2-1e6e-4b2d-8efa-c8a11c330485", "name": "06-23 20:57:48 INFO  - sign-app success\r\n", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818581319600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2194f002-1cfe-445b-a615-f77bea615fe1", "name": "entry : default@SignHap end {\n  rss: 656322560,\n  heapTotal: 89108480,\n  heapUsed: 83654336,\n  external: 1022692,\n  arrayBuffers: 58468\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818640801500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8635c454-fa56-4b4a-b349-ba6247c3763a", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5815097210900, "endTime": 5818640868300}, "additional": {"logType": "info", "children": [], "durationId": "abf07cb2-adca-4e79-8743-b7f7b9138893"}}, {"head": {"id": "e2d791e2-a9f9-49bb-b5a5-1fa8c0dd542a", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818642913700, "endTime": 5818643007000}, "additional": {"children": [], "state": "success", "detailId": "196ff070-d28b-4a8d-b407-76538c5d1258", "logId": "56b0c7cb-c8c3-485f-a774-462e4c07dabc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "196ff070-d28b-4a8d-b407-76538c5d1258", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818642818000}, "additional": {"logType": "detail", "children": [], "durationId": "e2d791e2-a9f9-49bb-b5a5-1fa8c0dd542a"}}, {"head": {"id": "8ac883fd-25d0-4f4d-84da-4aab1106e74d", "name": "entry : assembleHap start {\n  rss: 656494592,\n  heapTotal: 89108480,\n  heapUsed: 83809480,\n  external: 1022692,\n  arrayBuffers: 58468\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818642901100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09fa5979-ce7c-4500-bc68-0b4dbe6ede9d", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818642921600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9508fdfc-753b-4edc-a1b0-b0e945d89a9d", "name": "runTaskFromQueue task cost before running: 17 s 620 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818642963500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80f94a08-4bc4-416d-bbc7-a7ccc192ac36", "name": "entry : assembleHap end {\n  rss: 656506880,\n  heapTotal: 89108480,\n  heapUsed: 83821584,\n  external: 1022692,\n  arrayBuffers: 58468\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818642995500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56b0c7cb-c8c3-485f-a774-462e4c07dabc", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818642913700, "endTime": 5818643007000}, "additional": {"logType": "info", "children": [], "durationId": "e2d791e2-a9f9-49bb-b5a5-1fa8c0dd542a"}}, {"head": {"id": "a320edbc-8351-4172-bbf9-cfea54123d77", "name": "BUILD SUCCESSFUL in 17 s 620 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818643157000}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "15845304-7ff5-4df1-b539-84749dcb05ab", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5801023457500, "endTime": 5818643262100}, "additional": {"time": {"year": 2025, "month": 6, "day": 23, "hour": 20, "minute": 57}, "markType": "history", "category": "build", "state": "success"}}, {"head": {"id": "1c63d3d7-71a0-457c-af8e-33e11b9aa966", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818643286700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a99b31b-2b67-4a40-a3c3-c305f3300262", "name": "Update task entry:default@GenerateMetadata input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818645023800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b294f4ab-6e03-4a52-b894-029417f1cdfe", "name": "Update task entry:default@GenerateMetadata output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818646521600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89e1e4c4-2f4a-4f5c-a131-a61fbf446eac", "name": "Incremental task entry:default@GenerateMetadata post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818646872700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8daac97-6ab4-4fac-95f4-e37c56c7812a", "name": "Update task entry:default@CreateBuildProfile input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818646898300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc679765-3358-4929-9bec-7732285efc34", "name": "Update task entry:default@CreateBuildProfile input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818647105400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deb2e336-ce06-4bba-ab60-45b4b60177c1", "name": "Update task entry:default@CreateBuildProfile output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818647650100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79cc2559-5dd9-45f1-ae23-40df179d9c1e", "name": "Incremental task entry:default@CreateBuildProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818647903700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa3d5af5-9992-4a1b-8ea1-3e94872f01c9", "name": "Update task entry:default@GenerateLoaderJson output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader\\default\\loader.json cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818648087900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6714b4c9-637a-40be-9e0d-6a63658c30d7", "name": "Incremental task entry:default@GenerateLoaderJson post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818648311800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f7deef2-cbfc-43ca-a3a8-04dcecd89b24", "name": "Update task entry:default@MergeProfile input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818648334400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b93643e9-e5a3-416a-abf9-8076d23fc714", "name": "Update task entry:default@MergeProfile input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818648508600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "113bf117-1666-4049-b47c-c6e506a1212e", "name": "Update task entry:default@MergeProfile input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818648711100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18a1058e-42fa-404a-9500-6c9fd0e4e525", "name": "Update task entry:default@MergeProfile output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818648886700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "118652e3-8d14-458a-967d-055fbbf2c9d8", "name": "Incremental task entry:default@MergeProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818649092400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "137b1745-3362-43b7-830d-e40292dc9a85", "name": "Update task entry:default@MakePackInfo input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818649869100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bdc4f01-4656-4daa-a026-e63670f02fcd", "name": "Update task entry:default@MakePackInfo input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818650062300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e34d7dd-b793-429a-b16d-f742305b7706", "name": "Update task entry:default@MakePackInfo input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818650233000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87006f3e-415d-4251-86c4-64453492f41b", "name": "Update task entry:default@MakePackInfo output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\pack.info cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818652382000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7576a2fa-bf83-4d26-aef7-d450edc980b5", "name": "Incremental task entry:default@MakePackInfo post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818652669000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c8397bd-89d6-4d7f-8f34-e2386efcc3ee", "name": "Update task entry:default@ProcessProfile input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818652697600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "680e3623-d27e-4098-b513-ec551293838d", "name": "Update task entry:default@ProcessProfile output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818652712000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f79cdf0-5835-4ae1-9f99-b26a050f5b62", "name": "Incremental task entry:default@ProcessProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818652933500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b7ac685-4975-4542-8b75-67b582ee5641", "name": "Update task entry:default@ProcessResource output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818652954900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a1f0710-f3fc-4881-bd3a-35e47ca81513", "name": "Incremental task entry:default@ProcessResource post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818653162200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2925f13-e7d1-45e2-a533-eb8f0770606d", "name": "Update task entry:default@CompileResource input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\resources cache from map.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818653196100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2a3b62c-7c08-456e-a653-7664f09de885", "name": "Update task entry:default@CompileResource input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\AppScope\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818653204800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4caac53-fe38-4d90-8c36-522a6965bad4", "name": "Update task entry:default@CompileResource input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818655859900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3cf8e43-6570-4966-8ad8-2c75acd0a2ae", "name": "Update task entry:default@CompileResource input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818656044000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f656a6d-c742-4d15-8427-c1f3ca1705e5", "name": "Update task entry:default@CompileResource output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818656261200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf79339d-a454-4fe6-8a46-c3af992542d1", "name": "Update task entry:default@CompileResource output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818667674400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "802fd634-647b-4c7a-a8ff-6a02b1d3e6ce", "name": "Update task entry:default@CompileResource output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818667897900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06c85eb2-57e6-4d29-8be1-545296c2de6b", "name": "Incremental task entry:default@CompileResource post-execution cost:16 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818668367000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bae77ac-8efd-4811-9df6-7225a0a17826", "name": "Update task entry:default@CompileArkTS input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader\\default cache from map.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818671396200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "302920d0-d242-4250-8657-d94112f7e78b", "name": "Update task entry:default@CompileArkTS input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818671413800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed48cf04-dcc3-46f1-85e3-6947e7efff8d", "name": "Update task entry:default@CompileArkTS input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818671602100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3a82969-9f34-40c2-a968-219d957ad498", "name": "Update task entry:default@CompileArkTS input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818671790600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1b82ff8-944b-4f1c-ace6-b824db93cfab", "name": "Update task entry:default@CompileArkTS input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818672225800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25a15b88-de50-4625-acd0-76e527296184", "name": "Update task entry:default@CompileArkTS input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818676969700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04ddf468-ce08-468a-8b7a-1995984bc996", "name": "Update task entry:default@CompileArkTS output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818678004900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5264245a-cf57-4146-905e-e369bf5a0242", "name": "Incremental task entry:default@CompileArkTS post-execution cost:11 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818678704800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6827cd2-eba6-4257-adf8-e40c84dbacd4", "name": "Update task entry:default@BuildJS input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818680560900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2befb0dc-a18c-49c4-a9c5-91b680f8feb0", "name": "Update task entry:default@BuildJS input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818681007700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "916bd682-b358-424e-a74c-e9f44c8b8fff", "name": "Update task entry:default@BuildJS input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818681191000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f556842-50bc-41b7-94b6-764061782062", "name": "Update task entry:default@BuildJS input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818681360200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c262386a-90ca-4ad2-9f81-339e501aebe9", "name": "Update task entry:default@BuildJS output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818682245100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b022a16-4092-4652-827f-8b7a352db818", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818682499600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16170208-718c-40d5-8ffb-18ef96a10a56", "name": "Update task entry:default@ProcessLibs output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818682594600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6a1e859-4143-478b-8e1d-cd68039c2b5e", "name": "Incremental task entry:default@ProcessLibs post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818684615000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaee0426-ae94-4271-88c0-dbbecf838bef", "name": "Update task entry:default@DoNativeStrip input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default cache from map.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818684768000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c91071e1-d788-479a-9613-9a28fa689dee", "name": "Update task entry:default@DoNativeStrip output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818684788600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e53eaa88-8fd8-4338-a910-22842f2daf67", "name": "Incremental task entry:default@DoNativeStrip post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818687339900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "474b6a0f-ec51-4d59-8164-a7f82b4f7d35", "name": "Update task entry:default@CacheNativeLibs input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\libs\\default cache from map.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818687622600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0ff8268-f276-4a2d-ad39-b6b06ec5ab45", "name": "Update task entry:default@CacheNativeLibs input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818687637000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40d6542b-b69f-47c6-9fc0-94b4e144cccb", "name": "Update task entry:default@CacheNativeLibs output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\patch\\default\\base_native_libs.json cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818689576800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bd231ca-5989-456a-852a-acde06fc2a8f", "name": "Incremental task entry:default@CacheNativeLibs post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818689816400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb22a23f-7fc9-4dc9-84ba-f277262300f9", "name": "Update task entry:default@PackageHap input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache from map.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818690597900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55e63041-bd5b-4898-a642-7e84e6447f49", "name": "Update task entry:default@PackageHap input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818690618900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f152d54e-fe5b-4e8a-a94c-eebd1f918f41", "name": "Update task entry:default@PackageHap input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818690798200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25f40bf7-5fec-4432-a102-c760834aee83", "name": "Update task entry:default@PackageHap input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818700546100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3692a968-fa07-4d52-ace4-52a94d3faa8c", "name": "Update task entry:default@PackageHap input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818700734400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4401b4fe-6fd9-4d53-b174-e7a5cbb586af", "name": "Update task entry:default@PackageHap input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818700925900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c11df34-d27a-4f36-bceb-a8ee1cbf2ca4", "name": "Update task entry:default@PackageHap input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818701507600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c4f33a2-8d26-45d7-85ae-96b2de028cd0", "name": "Update task entry:default@PackageHap output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818702251100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee95dd12-edf5-40b0-bb17-d233db3c56a3", "name": "Incremental task entry:default@PackageHap post-execution cost:13 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818702507100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cb6b280-6114-4088-b426-5e22127f23ee", "name": "Update task entry:default@SignHap input file:C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818702672100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9768ae2-91aa-4a80-894d-806b30b5dbda", "name": "Update task entry:default@SignHap input file:C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818702934800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7618e7a-6c59-45b3-b2e7-16acf4a8b338", "name": "Update task entry:default@SignHap input file:C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818703141800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c65fd35d-514d-46ab-a156-cbd034ee8958", "name": "Update task entry:default@SignHap input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818703344800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "295c23d7-1628-4338-9541-62b10357f053", "name": "Update task entry:default@SignHap output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818703368000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1a80567-5856-44de-aa23-05c01c3930be", "name": "Incremental task entry:default@SignHap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818703578100}, "additional": {"logType": "debug", "children": []}}], "workLog": [{"type": "work", "time": "[2025-06-23T20:57:43.962]", "workerId": 2, "content": "[2025-06-23T20:57:43.962] > hvigor \u001b[32m\u001b[33mWARN: \u001b[33mArkTS:WARN File: D:/harmonyforwork/aboutCamera/baseCamera/Camera/entry/src/main/ets/views/ModeSwitchPage.ets:213:30\n 'createImageReceiver' has been deprecated.\n\u001b[39m\r\n\u001b[33mWARN: \u001b[33mArkTS:WARN File: D:/harmonyforwork/aboutCamera/baseCamera/Camera/entry/src/main/ets/views/ModeSwitchPage.ets:256:26\n 'close' has been deprecated.\n\u001b[39m\r\n\u001b[33mWARN: \u001b[33mArkTS:WARN File: D:/harmonyforwork/aboutCamera/baseCamera/Camera/entry/src/main/ets/views/ModeSwitchPage.ets:485:30\n 'close' has been deprecated.\n\u001b[39m\r\n\u001b[33mWARN: \u001b[33mArkTS:WARN File: D:/harmonyforwork/aboutCamera/baseCamera/Camera/entry/src/main/ets/common/DisplayCalculator.ts:26:20\n 'Get' has been deprecated.\n\u001b[39m\r\n\u001b[39m\n", "taskPath": "entry", "taskName": "default@CompileArkTS", "taskCompletePath": "entry:default@CompileArkTS"}]}