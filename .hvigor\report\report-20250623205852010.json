{"version": "1.0", "events": [{"head": {"id": "458056ba-805f-4a0a-9856-923b28161b71", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818731963200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e233bf31-4d1c-4cb6-9144-0210ff308a8c", "name": "worker[1] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818742367900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f1b0609-af27-4bc5-bb1b-06540015edbf", "name": "worker[0] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818753278200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7b5fa8b-cd20-4477-bc2c-543f930a37ea", "name": "hvigor disconnect:  client namespace disconnect", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818755252900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43b1ef5e-c48a-43aa-b47c-1b62bf412089", "name": "hvigor daemon: Socket will be closed. socketId=twtd7eTYdvwPAyH2AAAF, connectionCount=0", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818756498300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d11ea9a-c51f-44bf-96e9-7288ce1d6d2d", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":27036,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"fd9e05b4d84c056550a5ca3011a9a345f2863f7c\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1750683468709,\"createdBy\":\"deveco\",\"sessionId\":\"0000005011a4a8137b923b9a1f8574e249fdc2b8abf50e8220c993b1f13dcaa360a4f09cb218793e0881f9579ab0a3fb0e188eade7c4d362e698abddd6a6c737fcaffdebabb3904bba293cb3df5ff7b3376bd0aaadf926390f76bd1444b1c369\"}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818757953500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bf4b15c-f6d1-41ed-9fe2-380fbf631305", "name": "worker[2] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5818986760400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "299a7d18-550e-4eda-b7af-ead6bddc4952", "name": "hvigor daemon: Check daemon process existed in registry, daemonStatus=idle.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5842168516500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18162298-75dc-4af3-b495-fd4a05f9c7fa", "name": "hvigor daemon: Check daemon process existed in registry, daemonStatus=idle.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5872174435300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0af03186-d346-47c0-bf7b-40ab6514092c", "name": "hvigor daemon: Socket is connected. socketId=ZsUfm60TEYTurBhxAAAH, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880112291700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "914a9e19-fded-4b35-a14b-6afe1e4ee5af", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"fef7962a3805bfc5e665909552d6b84411df2805\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\testC\\\\Camera\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45001,\"pid\":9284,\"state\":\"idle\",\"lastUsedTime\":1750407074786,\"info\":null,\"createdBy\":\"deveco\",\"sessionId\":\"00000050aab88ac64b4178893fd796169b640639e1c9b813c06886269a431824f1c6089863679a3ccb64e3a10d6a82cfd481d956f2ce09eff3c9e009bf2456969ec7152898c9fea931f63ba2c72e0a1aee70f1f5847df547a40a94e65b6a302a\"},{\"pid\":27036,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"fd9e05b4d84c056550a5ca3011a9a345f2863f7c\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1750683468740,\"createdBy\":\"deveco\",\"sessionId\":\"0000005011a4a8137b923b9a1f8574e249fdc2b8abf50e8220c993b1f13dcaa360a4f09cb218793e0881f9579ab0a3fb0e188eade7c4d362e698abddd6a6c737fcaffdebabb3904bba293cb3df5ff7b3376bd0aaadf926390f76bd1444b1c369\"}]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880113271000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28f297b0-0fe0-4abb-b240-628f61478807", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":27036,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"fd9e05b4d84c056550a5ca3011a9a345f2863f7c\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1750683468740,\"createdBy\":\"deveco\",\"sessionId\":\"0000005011a4a8137b923b9a1f8574e249fdc2b8abf50e8220c993b1f13dcaa360a4f09cb218793e0881f9579ab0a3fb0e188eade7c4d362e698abddd6a6c737fcaffdebabb3904bba293cb3df5ff7b3376bd0aaadf926390f76bd1444b1c369\"}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880113970300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6dd749a-6f5c-4588-a579-bcf3dbef99ed", "name": "set active socket. socketId=ZsUfm60TEYTurBhxAAAH", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880118113000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "048b0caf-1af0-4375-a69a-bacc658b3d47", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":27036,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"fd9e05b4d84c056550a5ca3011a9a345f2863f7c\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1750683530094,\"createdBy\":\"deveco\",\"sessionId\":\"0000005011a4a8137b923b9a1f8574e249fdc2b8abf50e8220c993b1f13dcaa360a4f09cb218793e0881f9579ab0a3fb0e188eade7c4d362e698abddd6a6c737fcaffdebabb3904bba293cb3df5ff7b3376bd0aaadf926390f76bd1444b1c369\"}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880118799100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a50d3e41-c124-44f6-b3f5-97a118ba9700", "name": "hvigor daemon: Receive data from client. data={\n  mode: 'module',\n  parallel: true,\n  prop: [ 'module=entry@default', 'product=default' ],\n  incremental: true,\n  _: [ 'assembleHap' ],\n  daemon: true\n}.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880120950100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb417bcc-4e77-4dee-8bf0-769f8b1d269e", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880121427900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e909d70-dc87-4097-bdda-1e3a51b7523a", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":27036,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"fd9e05b4d84c056550a5ca3011a9a345f2863f7c\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1750683530099,\"createdBy\":\"deveco\",\"sessionId\":\"0000005011a4a8137b923b9a1f8574e249fdc2b8abf50e8220c993b1f13dcaa360a4f09cb218793e0881f9579ab0a3fb0e188eade7c4d362e698abddd6a6c737fcaffdebabb3904bba293cb3df5ff7b3376bd0aaadf926390f76bd1444b1c369\"}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880122099700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c9e24a1-7368-4cb6-8167-3a2a663223c4", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880125384800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1e90fb7-68f8-4868-9da8-73f02279b031", "name": "Cache service initialization finished in 9 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880134307500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cc34b11-4cff-49a5-aec0-c97c5a59075a", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880142972000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67568164-0892-4589-98d7-e9680623901e", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880152979100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "943b36c2-ff97-45ad-92e9-215c1ab331b1", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880153031800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6df6fc3a-3385-4917-a937-846cd7d1a89d", "name": "hvigorfile, resolve finished D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880162711500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93b3dd5b-303c-46c6-98f6-9edfaa40960c", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880166021800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b186130d-9b99-4c1a-b084-db3a4642f941", "name": "hvigorfile, require result:  { hapTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880174061600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeb77a3d-8227-412b-948e-34cbee0ac1cb", "name": "hvigorfile, binding system plugins { hapTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880174106900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26aed338-9610-4bf6-a7c7-c26304a01226", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880186480700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35bb05f3-37f6-49a2-8512-55a305f14985", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880186513000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e1a469f-b26e-46fa-84bb-11f716a2446b", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880192776400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93ab3490-ea89-4640-9364-b2cf09adf758", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880192829000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb516e25-d291-4732-a6ab-be94c280cc10", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880193120600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e71cec6-03d3-456a-9984-5a4e8c35de78", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880193273000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a6f29c0-0281-40d3-85e3-a3f060fc0f01", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880193304100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c0c0ed7-66c8-4608-a071-9377f40bb3aa", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880193316300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9436c9cf-446e-4f5d-a35c-c791994329b3", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880193352600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f8a3c9c-d994-4ad7-a33e-980041e28a1a", "name": "require SDK: toolchains,ArkTS,native; moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880194823400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b8576b2-2bbe-4b79-a035-e104667e4a9c", "name": "Module entry task initialization takes 7 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880204013800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3af46c16-6d5b-4ed4-96db-9a34ef227869", "name": "hvigorfile, resolve finished D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880204060800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b715e152-1071-4472-b4a8-eb1000985378", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880204117000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d910318a-4f6f-46be-a8dd-042202c97bc3", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880204132700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e06b48f-4bd7-47a7-8846-4df077d36e5d", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880204162100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f94145d4-e10f-480b-bc59-6c8c68a99a6c", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880204167900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2431f76-2b7f-4f66-ba33-94fb1377b320", "name": "Mo<PERSON>le Camera Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880204756700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9196406c-f31b-4da7-9235-7cf1729ff23d", "name": "Module Camera's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880204769600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1c7f03b-a5b4-45b3-b3d0-03950001254b", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880207220400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cd7e84d-09b1-4d08-b4b0-af9b2359cb94", "name": "Sdk init in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880214122400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94ade824-fb57-4fee-8871-778501e12ad4", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880246639200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b26d9a2-5197-4455-a51c-0701f27b8e67", "name": "Project task initialization takes 38 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880252028300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25cd9fcc-f4c7-42c5-a5fa-0bb06bb347fa", "name": "Sdk init in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880258668700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ad68b7f-5b84-4909-99f4-340416388301", "name": "Configuration phase cost:126 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880260224500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4f7d614-b9cf-4776-b944-b30b81c37aa5", "name": "Configuration task cost before running: 137 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880261200100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed9e93e8-f714-421c-bceb-4f7cfdcf2226", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880271898900, "endTime": 5880277989900}, "additional": {"children": [], "state": "success", "detailId": "7143bea6-e403-48e7-935e-45381486e32d", "logId": "bdb6cce4-b3e5-409d-8285-b2b1b1719aca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "7143bea6-e403-48e7-935e-45381486e32d", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880263162800}, "additional": {"logType": "detail", "children": [], "durationId": "ed9e93e8-f714-421c-bceb-4f7cfdcf2226"}}, {"head": {"id": "761dfc69-cd8f-4008-8c0d-5ab9ee85a2c1", "name": "entry : default@PreBuild start {\n  rss: 161849344,\n  heapTotal: 91877376,\n  heapUsed: 87242824,\n  external: 1068752,\n  arrayBuffers: 104528\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880271861700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a273ab3-7c94-438b-bd03-c359e0eeae1e", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880271946900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdf12eeb-c30c-4780-b379-f88b7998fe8c", "name": "Incremental task entry:default@PreBuild pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880277780500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea7a0e0b-edb2-4092-ba32-caa7e85d9808", "name": "entry : default@PreBuild end {\n  rss: 162586624,\n  heapTotal: 91877376,\n  heapUsed: 87418304,\n  external: 1076944,\n  arrayBuffers: 112720\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880277958300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdb6cce4-b3e5-409d-8285-b2b1b1719aca", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880271898900, "endTime": 5880277989900}, "additional": {"logType": "info", "children": [], "durationId": "ed9e93e8-f714-421c-bceb-4f7cfdcf2226"}}, {"head": {"id": "9d2bcdd8-02ea-42c1-868d-d0bccffababf", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880281965000, "endTime": 5880300626800}, "additional": {"children": [], "state": "success", "detailId": "9b307b9f-afdd-434a-8a49-cdf144c626a0", "logId": "82c6da26-522f-4c53-8433-1cfc3194c534"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "9b307b9f-afdd-434a-8a49-cdf144c626a0", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880280454300}, "additional": {"logType": "detail", "children": [], "durationId": "9d2bcdd8-02ea-42c1-868d-d0bccffababf"}}, {"head": {"id": "2ea9ccc9-c627-4104-97c7-fefcfe7958da", "name": "entry : default@GenerateMetadata start {\n  rss: 163418112,\n  heapTotal: 91877376,\n  heapUsed: 87632440,\n  external: 1076944,\n  arrayBuffers: 112720\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880281932700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a350e2c-bc77-4803-864d-b65b82f76d25", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880281985300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc3db8d5-193c-457f-979e-a12309824386", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880300526000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3941e38c-04c6-4a18-ad84-6d507fe4d2fe", "name": "entry : default@GenerateMetadata end {\n  rss: 165376000,\n  heapTotal: 92139520,\n  heapUsed: 87749952,\n  external: 1085136,\n  arrayBuffers: 120912\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880300605600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82c6da26-522f-4c53-8433-1cfc3194c534", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880281965000, "endTime": 5880300626800}, "additional": {"logType": "info", "children": [], "durationId": "9d2bcdd8-02ea-42c1-868d-d0bccffababf"}}, {"head": {"id": "6e3aa888-ae73-447f-96c0-3b0cf1288687", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880309964000, "endTime": 5880314033400}, "additional": {"children": [], "state": "success", "detailId": "83eb0da6-424e-4ec4-a163-18fa0249f0e1", "logId": "317b16ff-b879-40ac-9a5b-5c60315a9341"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "83eb0da6-424e-4ec4-a163-18fa0249f0e1", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880308024500}, "additional": {"logType": "detail", "children": [], "durationId": "6e3aa888-ae73-447f-96c0-3b0cf1288687"}}, {"head": {"id": "6b7f25a2-a2af-4781-9467-b16d4b482060", "name": "entry : default@CreateBuildProfile start {\n  rss: 166154240,\n  heapTotal: 93450240,\n  heapUsed: 87518472,\n  external: 1101520,\n  arrayBuffers: 96336\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880309934700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c026d42c-0d6a-4607-adef-b7a6aed8c4a3", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880309979600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d33cc4b9-5fad-4119-8e4a-96ae78be5276", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880313920400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f24c8a7-1733-4520-b201-a77572b89f7e", "name": "entry : default@CreateBuildProfile end {\n  rss: 166703104,\n  heapTotal: 93450240,\n  heapUsed: 87625608,\n  external: 1101520,\n  arrayBuffers: 96336\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880314011000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "317b16ff-b879-40ac-9a5b-5c60315a9341", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880309964000, "endTime": 5880314033400}, "additional": {"logType": "info", "children": [], "durationId": "6e3aa888-ae73-447f-96c0-3b0cf1288687"}}, {"head": {"id": "be5f8caf-6547-4995-8208-5ada544f3b23", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880316564800, "endTime": 5881432253600}, "additional": {"children": ["ad79a777-0e1a-4e6b-8bc5-53d34018ce13", "275b1a9a-097c-4b66-80d7-5e40c829d4d4", "42fc01fc-8741-486f-a4b5-ba822d5ab206"], "state": "success", "detailId": "51b45df6-5021-4bed-a069-692f00d485c7", "logId": "ec42d02b-e310-4318-9c28-098ea7911b74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "51b45df6-5021-4bed-a069-692f00d485c7", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880315813100}, "additional": {"logType": "detail", "children": [], "durationId": "be5f8caf-6547-4995-8208-5ada544f3b23"}}, {"head": {"id": "def3abb4-4281-4928-84dd-8fdae820812f", "name": "entry : default@BuildNativeWithCmake start {\n  rss: 167178240,\n  heapTotal: 93450240,\n  heapUsed: 87872440,\n  external: 1101520,\n  arrayBuffers: 96336\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880316548900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d30ff8c-d6b4-4ee3-b323-47bd2223613d", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880316573500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "177a15c4-88f1-4eab-976e-38445db18748", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\arm64-v8a',\n  '-DOHOS_ARCH=arm64-v8a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\arm64-v8a',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=arm64-v8a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880318602700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "700a4bf2-0a06-4087-82a4-932e53197ace", "name": "default@BuildNativeWithCmake work[11] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880319524300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad79a777-0e1a-4e6b-8bc5-53d34018ce13", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5880320061700, "endTime": 5881290944400}, "additional": {"children": [], "state": "success", "parent": "be5f8caf-6547-4995-8208-5ada544f3b23", "logId": "14613898-74c7-4d76-9c80-3d3677d9a4e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "294a7ee4-16f1-4d60-b637-8393d70cdfdc", "name": "default@BuildNativeWithCmake work[11] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880319996300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d422bff8-2f6a-4c8f-90ab-6c0d95983021", "name": "default@BuildNativeWithCmake work[11] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880320067900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a67e69b-1e49-43ed-8a29-057941737054", "name": "default@BuildNativeWithCmake work[11] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880320083600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "892787cd-4333-4134-8e49-e7597e527ee5", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\armeabi-v7a',\n  '-DOHOS_ARCH=armeabi-v7a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\armeabi-v7a',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=armeabi-v7a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880321418000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41e69bb4-ab1a-4be2-88ca-6225297a05d2", "name": "default@BuildNativeWithCmake work[12] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880322278700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "275b1a9a-097c-4b66-80d7-5e40c829d4d4", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker1", "startTime": 5880322846500, "endTime": 5881291505800}, "additional": {"children": [], "state": "success", "parent": "be5f8caf-6547-4995-8208-5ada544f3b23", "logId": "6076a7a8-13d5-4910-8a43-93230cccd268"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "dc1335a2-07a3-47af-9fef-56ec30864f91", "name": "default@BuildNativeWithCmake work[12] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880322778400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a61ee0a-a9b6-4929-ad33-1bef3d45aa45", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880322793200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b114e8d-ad71-43b2-bbd5-0e86155a6d4b", "name": "default@BuildNativeWithCmake work[12] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880322851300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15a96369-1e9e-4f3e-9a08-11ccbbf4aed2", "name": "default@BuildNativeWithCmake work[12] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880322865400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c853618f-f094-45bd-afb5-4661cb1d4593", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\x86_64',\n  '-DOHOS_ARCH=x86_64',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\x86_64',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=x86_64',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880324585800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a22e247-8ed9-4533-8c53-e990ab8c9ff6", "name": "default@BuildNativeWithCmake work[13] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880325510200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42fc01fc-8741-486f-a4b5-ba822d5ab206", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5881291120900, "endTime": 5881432165000}, "additional": {"children": [], "state": "success", "parent": "be5f8caf-6547-4995-8208-5ada544f3b23", "logId": "2d1e438a-3059-4b66-8eb7-f16899690578"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "93330da4-8c5e-453d-8eab-739995714db5", "name": "default@BuildNativeWithCmake work[13] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880326460100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2259c58a-75fa-4207-a591-4296f4cff07a", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880326476900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a107e19-1c13-4fd3-a599-7e7cbc47d6b6", "name": "A work dispatched to worker[1] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880326482900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "127db805-611a-4e77-9349-d7154f172a5a", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880326488700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bace65f9-1bc3-4e4e-b49c-7de1719cae71", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880326492600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20c86eca-b267-4794-abff-4a162d2caa8c", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880326496800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad122510-596c-4b4d-ab9a-a7763d1cfefa", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880326500500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3011540a-4959-4ecd-ad83-9679750ef0a4", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880326515800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b42c7c05-03b7-4a6a-b57b-b64c4b7ffac6", "name": "default@BuildNativeWithCmake work[13] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880326519600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c24b1587-8b7d-4620-be48-96594f947e89", "name": "entry : default@BuildNativeWithCmake end {\n  rss: 169115648,\n  heapTotal: 93712384,\n  heapUsed: 87598072,\n  external: 1060560,\n  arrayBuffers: 96336\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880326570800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc2297f7-292e-4ac8-9bc6-e372e03c8446", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880333489100, "endTime": 5880336688900}, "additional": {"children": [], "state": "success", "detailId": "466303eb-7a15-4be5-8bf8-a9767bdd4603", "logId": "1b737e6a-a96b-4249-bc67-b796f8bd2a24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "466303eb-7a15-4be5-8bf8-a9767bdd4603", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880327444000}, "additional": {"logType": "detail", "children": [], "durationId": "bc2297f7-292e-4ac8-9bc6-e372e03c8446"}}, {"head": {"id": "01c48246-26a4-460c-9dd9-4dd09dfb0cd2", "name": "entry : default@GenerateLoaderJson start {\n  rss: 170102784,\n  heapTotal: 93712384,\n  heapUsed: 87949552,\n  external: 1068752,\n  arrayBuffers: 104528\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880333456000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "297ddd57-e6ae-438b-a36a-4395706f067f", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880333508900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d7d9bbf-f52c-4e24-92cd-2ec459986af4", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880336585300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41a7d312-6edd-4d26-8eff-5135d4bb2423", "name": "entry : default@GenerateLoaderJson end {\n  rss: 170147840,\n  heapTotal: 93712384,\n  heapUsed: 88123624,\n  external: 1068752,\n  arrayBuffers: 104528\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880336665800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b737e6a-a96b-4249-bc67-b796f8bd2a24", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880333489100, "endTime": 5880336688900}, "additional": {"logType": "info", "children": [], "durationId": "bc2297f7-292e-4ac8-9bc6-e372e03c8446"}}, {"head": {"id": "d6121061-8450-4e92-80c6-2b1790aed3ea", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880340962200, "endTime": 5880343299200}, "additional": {"children": [], "state": "success", "detailId": "e4dd657d-1a5b-4a15-86cb-2bf412dbf0f6", "logId": "e2d03f29-7ec5-4791-9e1f-9bc5ec1b31f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "e4dd657d-1a5b-4a15-86cb-2bf412dbf0f6", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880338804300}, "additional": {"logType": "detail", "children": [], "durationId": "d6121061-8450-4e92-80c6-2b1790aed3ea"}}, {"head": {"id": "14e02555-20ca-4533-ac79-371ea9abaf2a", "name": "entry : default@MergeProfile start {\n  rss: 170233856,\n  heapTotal: 93712384,\n  heapUsed: 88387016,\n  external: 1068752,\n  arrayBuffers: 104528\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880340931300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec4357ab-c20d-49aa-9b03-a1165a83a930", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880340979400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb3a7168-f66d-43d2-be44-69973d713cc9", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880343186400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "284f0d8c-b90e-4077-b0e7-e111a50e9229", "name": "entry : default@MergeProfile end {\n  rss: 170254336,\n  heapTotal: 93712384,\n  heapUsed: 88462952,\n  external: 1068752,\n  arrayBuffers: 104528\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880343275600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2d03f29-7ec5-4791-9e1f-9bc5ec1b31f4", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880340962200, "endTime": 5880343299200}, "additional": {"logType": "info", "children": [], "durationId": "d6121061-8450-4e92-80c6-2b1790aed3ea"}}, {"head": {"id": "802e110f-15ae-4a77-86f3-6ce7c787b682", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880346610800, "endTime": 5880359799900}, "additional": {"children": [], "state": "success", "detailId": "6dcbf6d4-e3ec-45e9-a2ce-525955a9c777", "logId": "ea6ec090-3db5-44d9-917d-1617045ce79a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "6dcbf6d4-e3ec-45e9-a2ce-525955a9c777", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880345739100}, "additional": {"logType": "detail", "children": [], "durationId": "802e110f-15ae-4a77-86f3-6ce7c787b682"}}, {"head": {"id": "e264e715-aa3c-47b3-9799-7b8ab94acae5", "name": "entry : default@MakePackInfo start {\n  rss: 170491904,\n  heapTotal: 96333824,\n  heapUsed: 87839456,\n  external: 1068752,\n  arrayBuffers: 104528\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880346591400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57767fbd-8253-45cb-8fe2-5b0d527a7a5e", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880346622800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c850c911-7ffb-4af8-a558-623176f82699", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880359724000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c7224d7-5203-48c1-895e-d2897bd54699", "name": "entry : default@MakePackInfo end {\n  rss: 171163648,\n  heapTotal: 96333824,\n  heapUsed: 88434280,\n  external: 1101520,\n  arrayBuffers: 137296\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880359785500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea6ec090-3db5-44d9-917d-1617045ce79a", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880346610800, "endTime": 5880359799900}, "additional": {"logType": "info", "children": [], "durationId": "802e110f-15ae-4a77-86f3-6ce7c787b682"}}, {"head": {"id": "b065121f-d8f7-4e0c-a89d-377dcca505c4", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880367109700, "endTime": 5880368008300}, "additional": {"children": [], "state": "success", "detailId": "accedb8e-4af8-4049-88c1-ed5787678c9c", "logId": "772b4e9e-f59a-4e52-894d-d04e64954ea8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "accedb8e-4af8-4049-88c1-ed5787678c9c", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880365598200}, "additional": {"logType": "detail", "children": [], "durationId": "b065121f-d8f7-4e0c-a89d-377dcca505c4"}}, {"head": {"id": "286aa106-ce6e-4946-a0a8-1e4512719093", "name": "entry : default@ProcessProfile start {\n  rss: 171569152,\n  heapTotal: 96333824,\n  heapUsed: 88839232,\n  external: 1109712,\n  arrayBuffers: 145488\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880367090400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e5420a4-557f-4070-bc61-3897681bff80", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880367118000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "353f3060-34d2-404d-b622-19f9d35ba996", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880367954400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0bb014f-6555-4883-bd6e-41c836c4366c", "name": "entry : default@ProcessProfile end {\n  rss: 171630592,\n  heapTotal: 96333824,\n  heapUsed: 88891976,\n  external: 1109712,\n  arrayBuffers: 145488\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880367994300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "772b4e9e-f59a-4e52-894d-d04e64954ea8", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880367109700, "endTime": 5880368008300}, "additional": {"logType": "info", "children": [], "durationId": "b065121f-d8f7-4e0c-a89d-377dcca505c4"}}, {"head": {"id": "6ed3c607-146b-45b1-bd90-678b09a51cd7", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880370949400, "endTime": 5880383245800}, "additional": {"children": [], "state": "success", "detailId": "9b6466db-406c-4f54-9514-266d5734235d", "logId": "e711d10d-bd19-4489-b226-6291ea64d477"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "9b6466db-406c-4f54-9514-266d5734235d", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880368875500}, "additional": {"logType": "detail", "children": [], "durationId": "6ed3c607-146b-45b1-bd90-678b09a51cd7"}}, {"head": {"id": "5da874f7-9ac6-48d1-8877-90e2a0c124b4", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880370434900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "259a1d66-b947-4b38-a6f3-91c3be4514fe", "name": "entry : default@ProcessResource start {\n  rss: 171892736,\n  heapTotal: 96333824,\n  heapUsed: 89117272,\n  external: 1117904,\n  arrayBuffers: 153680\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880370933200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51bfddef-3709-4f25-ada7-cacd9f59e59c", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880375591600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5546a57-1104-4558-834a-58c2240f2d5b", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880376776900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23ec76e1-f6c1-4045-ac67-6daebc1a3975", "name": "entry : default@ProcessResource end {\n  rss: 172474368,\n  heapTotal: 96333824,\n  heapUsed: 89389448,\n  external: 1126096,\n  arrayBuffers: 161872\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880376925100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e711d10d-bd19-4489-b226-6291ea64d477", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880370949400, "endTime": 5880383245800}, "additional": {"logType": "info", "children": [], "durationId": "6ed3c607-146b-45b1-bd90-678b09a51cd7"}}, {"head": {"id": "20549676-a1da-4690-8588-3c1b0cb75759", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880390370900, "endTime": 5880416764800}, "additional": {"children": [], "state": "success", "detailId": "9d3a36a9-0510-40b3-a98f-78dab50531e2", "logId": "df72e50c-acd0-4851-9549-d8ea1626f6ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "9d3a36a9-0510-40b3-a98f-78dab50531e2", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880387549100}, "additional": {"logType": "detail", "children": [], "durationId": "20549676-a1da-4690-8588-3c1b0cb75759"}}, {"head": {"id": "8ee510d3-0e53-4ee6-b423-596a40494127", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880389037000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af3cf971-b1a3-48da-ad10-0dd602bbe52d", "name": "entry : default@CompileResource start {\n  rss: 173051904,\n  heapTotal: 96333824,\n  heapUsed: 88556552,\n  external: 1085136,\n  arrayBuffers: 120912\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880390352600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31fd5da9-2cd1-4e07-a7d3-e1f6ef9a617f", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880390391500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75a4094a-87f9-4de1-9076-010209abb4e8", "name": "Incremental task entry:default@CompileResource pre-execution cost: 27 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880416673100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "030426af-46f4-4934-a5c4-41dc0c75ebdc", "name": "entry : default@CompileResource end {\n  rss: 175652864,\n  heapTotal: 96595968,\n  heapUsed: 88306160,\n  external: 1085136,\n  arrayBuffers: 104528\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880416730700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df72e50c-acd0-4851-9549-d8ea1626f6ba", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880390370900, "endTime": 5880416764800}, "additional": {"logType": "info", "children": [], "durationId": "20549676-a1da-4690-8588-3c1b0cb75759"}}, {"head": {"id": "17b7da62-7676-49c2-9f2b-37e012e3c365", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880420763900, "endTime": 5880435564400}, "additional": {"children": [], "state": "success", "detailId": "eb625587-2d94-49d8-8e9c-3017cb0ce452", "logId": "b00f3ac7-afd7-40fa-887c-0e3be5563d5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "eb625587-2d94-49d8-8e9c-3017cb0ce452", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880417597300}, "additional": {"logType": "detail", "children": [], "durationId": "17b7da62-7676-49c2-9f2b-37e012e3c365"}}, {"head": {"id": "c2f67adc-6939-405e-822e-f85a0948e8f4", "name": "entry : default@CompileArkTS start {\n  rss: 175726592,\n  heapTotal: 96595968,\n  heapUsed: 88669536,\n  external: 1076944,\n  arrayBuffers: 112720\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880420746800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9174384-0fd4-4b1b-a8a0-49f5d690e8a9", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880420772000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "babb29d1-8c1a-4e45-9f67-d43c7e393131", "name": "Obfuscation config only effect in release mode.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880423362200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67e5758e-5f66-479b-ac6d-40d37607879f", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880435485200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce34e62f-b301-4c1c-9459-3124629d9a25", "name": "entry : default@CompileArkTS end {\n  rss: 178130944,\n  heapTotal: 96595968,\n  heapUsed: 89572248,\n  external: 1085136,\n  arrayBuffers: 120912\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880435539600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b00f3ac7-afd7-40fa-887c-0e3be5563d5e", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880420763900, "endTime": 5880435564400}, "additional": {"logType": "info", "children": [], "durationId": "17b7da62-7676-49c2-9f2b-37e012e3c365"}}, {"head": {"id": "df3f6fec-94ff-43bc-9e75-67cbdd628241", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880444865800, "endTime": 5880450342100}, "additional": {"children": [], "state": "success", "detailId": "194e2727-c8dc-4610-b8be-b779102f568a", "logId": "75ed163b-00ba-4d35-af9f-5a958f90f726"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "194e2727-c8dc-4610-b8be-b779102f568a", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880439788700}, "additional": {"logType": "detail", "children": [], "durationId": "df3f6fec-94ff-43bc-9e75-67cbdd628241"}}, {"head": {"id": "03ec5127-ed38-4c36-8ce2-a497b3fd4afe", "name": "entry : default@BuildJS start {\n  rss: 179458048,\n  heapTotal: 96595968,\n  heapUsed: 88114000,\n  external: 1093328,\n  arrayBuffers: 104528\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880444844400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfe9aa9e-8e27-4b48-9613-4a4e437a3a85", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880444879900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27c10fca-2f8a-4f0d-a00f-8b5b20622bfe", "name": "entry : default@BuildJS end {\n  rss: 180338688,\n  heapTotal: 96595968,\n  heapUsed: 88474776,\n  external: 1076944,\n  arrayBuffers: 112720\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880450318700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75ed163b-00ba-4d35-af9f-5a958f90f726", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880444865800, "endTime": 5880450342100}, "additional": {"logType": "info", "children": [], "durationId": "df3f6fec-94ff-43bc-9e75-67cbdd628241"}}, {"head": {"id": "523d1297-9c4a-4773-b79c-a2942844f182", "name": "runTaskFromQueue task cost before running: 326 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880450491400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edd424de-4879-41ad-b1aa-d76a75fcd6e6", "name": "default@BuildNativeWithCmake work[11] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881290989700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14613898-74c7-4d76-9c80-3d3677d9a4e1", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5880320061700, "endTime": 5881290944400}, "additional": {"logType": "info", "children": [], "durationId": "ad79a777-0e1a-4e6b-8bc5-53d34018ce13", "parent": "ec42d02b-e310-4318-9c28-098ea7911b74"}}, {"head": {"id": "bc16a4c0-e166-4616-9b6a-7a7cfe4a5917", "name": "default@BuildNativeWithCmake work[13] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881291126500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77ee0aec-bf07-41cd-94dc-432b7c86cff2", "name": "default@BuildNativeWithCmake work[12] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881291523200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6076a7a8-13d5-4910-8a43-93230cccd268", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker1", "startTime": 5880322846500, "endTime": 5881291505800}, "additional": {"logType": "info", "children": [], "durationId": "275b1a9a-097c-4b66-80d7-5e40c829d4d4", "parent": "ec42d02b-e310-4318-9c28-098ea7911b74"}}, {"head": {"id": "55776855-021c-462a-9549-a6524e493ea3", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881291587600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83073135-8a12-4758-bb81-ebba41aa91b6", "name": "default@BuildNativeWithCmake work[13] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881432194700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d1e438a-3059-4b66-8eb7-f16899690578", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5881291120900, "endTime": 5881432165000}, "additional": {"logType": "info", "children": [], "durationId": "42fc01fc-8741-486f-a4b5-ba822d5ab206", "parent": "ec42d02b-e310-4318-9c28-098ea7911b74"}}, {"head": {"id": "ec42d02b-e310-4318-9c28-098ea7911b74", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880316564800, "endTime": 5881432253600}, "additional": {"logType": "info", "children": ["14613898-74c7-4d76-9c80-3d3677d9a4e1", "6076a7a8-13d5-4910-8a43-93230cccd268", "2d1e438a-3059-4b66-8eb7-f16899690578"], "durationId": "be5f8caf-6547-4995-8208-5ada544f3b23"}}, {"head": {"id": "e2973b30-b9d4-4ead-9cfc-d5da180891a1", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881434307100, "endTime": 5881930567200}, "additional": {"children": ["08f10157-2990-4440-a02e-67e3dcb9564d", "8a8da505-e285-495d-97c3-b279a8f6cd57", "51ef15ef-fccd-45a0-83a8-f2da60880803"], "state": "success", "detailId": "abcde17a-02d8-4b91-b341-019699565868", "logId": "b09077cf-eecb-4f43-85c0-57e9215a7354"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "abcde17a-02d8-4b91-b341-019699565868", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881433669300}, "additional": {"logType": "detail", "children": [], "durationId": "e2973b30-b9d4-4ead-9cfc-d5da180891a1"}}, {"head": {"id": "fb51c8d9-3fcd-4ae0-b875-dc036c971cef", "name": "entry : default@BuildNativeWithNinja start {\n  rss: 284336128,\n  heapTotal: 96595968,\n  heapUsed: 88743480,\n  external: 1076944,\n  arrayBuffers: 112720\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881434292200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9e292e6-e2a3-4813-a6c7-a628fdd03c59", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881434315500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ff0baf7-7179-4a4a-9209-fb38916543d7", "name": "Use tool [Ninja]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '-C',\n  'D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\arm64-v8a'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881442650500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "659358ea-c909-4e11-a4dd-5110f61a4c4b", "name": "default@BuildNativeWithNinja work[14] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881443230700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08f10157-2990-4440-a02e-67e3dcb9564d", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5881443767600, "endTime": 5881705999200}, "additional": {"children": [], "state": "success", "parent": "e2973b30-b9d4-4ead-9cfc-d5da180891a1", "logId": "e296dfeb-7935-4a21-93d4-0d197474f1fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "048b0d26-4b7d-449d-8b0b-4f08d53323b7", "name": "default@BuildNativeWithNinja work[14] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881443713500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fba9b65d-9374-4c20-a54f-3a0a487f72c8", "name": "default@BuildNativeWithNinja work[14] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881443774000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4515fdcf-7fa6-415a-b180-1b85f0f50787", "name": "default@BuildNativeWithNinja work[14] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881443790400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cff898b-db11-4bc9-8200-721fa6486be0", "name": "Use tool [Ninja]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '-C',\n  'D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\armeabi-v7a'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881453550900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a99f36a-9392-46ef-8281-07d76802c6fa", "name": "default@BuildNativeWithNinja work[15] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881454079300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a8da505-e285-495d-97c3-b279a8f6cd57", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker1", "startTime": 5881454613300, "endTime": 5881711511000}, "additional": {"children": [], "state": "success", "parent": "e2973b30-b9d4-4ead-9cfc-d5da180891a1", "logId": "b7d47301-ff24-48b4-8a9d-9baf319e7abc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "bd61c9bf-c5af-475c-80cc-c6b21954ed7c", "name": "default@BuildNativeWithNinja work[15] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881454541800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "041384be-8060-427c-a9fc-614e8023fc45", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881454556100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fa7cfa2-67a7-4279-ac45-6c911cb29464", "name": "default@BuildNativeWithNinja work[15] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881454617900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a1074be-77aa-4a83-84d8-dd54c71ff6aa", "name": "default@BuildNativeWithNinja work[15] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881454631100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee5bcb84-692e-4b06-a855-861b87333400", "name": "Use tool [Ninja]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '-C',\n  'D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\x86_64'\n]", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881464298800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "020bdc68-2d68-4796-b1fb-67e009169123", "name": "default@BuildNativeWithNinja work[16] is submitted.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881464714300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51ef15ef-fccd-45a0-83a8-f2da60880803", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5881706290000, "endTime": 5881930489900}, "additional": {"children": [], "state": "success", "parent": "e2973b30-b9d4-4ead-9cfc-d5da180891a1", "logId": "9dfb297c-054c-4043-94c1-bf6412420af0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "a842e6eb-90da-4c33-bfe2-18058ab9bc85", "name": "default@BuildNativeWithNinja work[16] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881465881500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0f8cfe6-5af4-499c-b2af-6faefa0aa24c", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881465898100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5683db88-be22-46e2-add8-5d9fd26a6aa0", "name": "A work dispatched to worker[1] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881465903600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da0f31dd-3806-483d-a7b7-e640fc573bdc", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881465908800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b1c6621-77cb-4560-919b-d08fd8edce80", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881465913000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afbd5eed-330b-44bf-8812-3a166d78325a", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881465917200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c318f083-c334-4e4b-956d-92fea3abcb58", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881465920500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4cca0fe-4f3a-404e-b4dd-29ebda1a7d0b", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881465924600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4adad83-fae2-4540-b00d-9a3b909c7234", "name": "default@BuildNativeWithNinja work[16] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881465927700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec7eb0b0-328e-4ec4-83f3-3cee4a6d1432", "name": "entry : default@BuildNativeWithNinja end {\n  rss: 284536832,\n  heapTotal: 96858112,\n  heapUsed: 88650280,\n  external: 1101601,\n  arrayBuffers: 112720\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881473322100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13189563-e640-4797-b1a5-d68f900fcd1c", "name": "default@BuildNativeWithNinja work[14] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881706076200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e296dfeb-7935-4a21-93d4-0d197474f1fc", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5881443767600, "endTime": 5881705999200}, "additional": {"logType": "info", "children": [], "durationId": "08f10157-2990-4440-a02e-67e3dcb9564d", "parent": "b09077cf-eecb-4f43-85c0-57e9215a7354"}}, {"head": {"id": "cd7a996e-0b42-4182-9941-b74a59f0bfde", "name": "default@BuildNativeWithNinja work[16] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881706298800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4401d54-fdfa-472b-bed4-02dbe1b75811", "name": "default@BuildNativeWithNinja work[15] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881711537200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7d47301-ff24-48b4-8a9d-9baf319e7abc", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker1", "startTime": 5881454613300, "endTime": 5881711511000}, "additional": {"logType": "info", "children": [], "durationId": "8a8da505-e285-495d-97c3-b279a8f6cd57", "parent": "b09077cf-eecb-4f43-85c0-57e9215a7354"}}, {"head": {"id": "fea14afe-94da-4693-83c3-e091fcc1a029", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881711601600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a817a0a-be1f-453b-867f-cd1392bfd0c7", "name": "default@BuildNativeWithNinja work[16] done.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881930514300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dfb297c-054c-4043-94c1-bf6412420af0", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Worker0", "startTime": 5881706290000, "endTime": 5881930489900}, "additional": {"logType": "info", "children": [], "durationId": "51ef15ef-fccd-45a0-83a8-f2da60880803", "parent": "b09077cf-eecb-4f43-85c0-57e9215a7354"}}, {"head": {"id": "b09077cf-eecb-4f43-85c0-57e9215a7354", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881434307100, "endTime": 5881930567200}, "additional": {"logType": "info", "children": ["e296dfeb-7935-4a21-93d4-0d197474f1fc", "b7d47301-ff24-48b4-8a9d-9baf319e7abc", "9dfb297c-054c-4043-94c1-bf6412420af0"], "durationId": "e2973b30-b9d4-4ead-9cfc-d5da180891a1"}}, {"head": {"id": "288a1fe3-b460-4c6b-9f74-ded20d13130a", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881934334900, "endTime": 5881960972200}, "additional": {"children": [], "state": "success", "detailId": "5f5a346a-9491-45e1-afef-a43a8575f4dd", "logId": "06739909-59d7-4cad-8f5d-187fd95c9d25"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "5f5a346a-9491-45e1-afef-a43a8575f4dd", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881931887100}, "additional": {"logType": "detail", "children": [], "durationId": "288a1fe3-b460-4c6b-9f74-ded20d13130a"}}, {"head": {"id": "5cd7153c-3210-498b-917e-e0d4413def68", "name": "entry : default@ProcessLibs start {\n  rss: 284635136,\n  heapTotal: 96858112,\n  heapUsed: 88975912,\n  external: 1101601,\n  arrayBuffers: 112720\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881934318100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ff6dd60-da5e-442a-b457-7f13587d75aa", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881934343100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23204c08-2d6a-4a6b-bbae-11db24106d6c", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881960876900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da1f4652-e77d-434f-8a2b-127cd7744cdc", "name": "entry : default@ProcessLibs end {\n  rss: 284688384,\n  heapTotal: 96858112,\n  heapUsed: 89553024,\n  external: 6138468,\n  arrayBuffers: 5174244\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881960946200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06739909-59d7-4cad-8f5d-187fd95c9d25", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881934334900, "endTime": 5881960972200}, "additional": {"logType": "info", "children": [], "durationId": "288a1fe3-b460-4c6b-9f74-ded20d13130a"}}, {"head": {"id": "324ae682-cfc9-4bf2-b100-e9a76f11f74e", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881965177600, "endTime": 5881969468900}, "additional": {"children": [], "state": "success", "detailId": "a6fda1bf-2501-45d3-b4da-3c7f54a80709", "logId": "9994e0b4-eecd-43d5-8829-c24526b2dabf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "a6fda1bf-2501-45d3-b4da-3c7f54a80709", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881963059200}, "additional": {"logType": "detail", "children": [], "durationId": "324ae682-cfc9-4bf2-b100-e9a76f11f74e"}}, {"head": {"id": "46c186dd-e835-422d-8624-15345c2c828b", "name": "entry : default@DoNativeStrip start {\n  rss: 284712960,\n  heapTotal: 96858112,\n  heapUsed: 89838536,\n  external: 6138468,\n  arrayBuffers: 5174244\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881965122200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfaaec87-6346-40a9-a77a-563832f1c72a", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881965185700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05ce67bf-b746-48c3-b37d-983a2993af3b", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881969385300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f62ef3ee-054c-49dc-8c68-4c719c14f984", "name": "entry : default@DoNativeStrip end {\n  rss: 284712960,\n  heapTotal: 96858112,\n  heapUsed: 90100960,\n  external: 6138468,\n  arrayBuffers: 5174244\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881969426200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9994e0b4-eecd-43d5-8829-c24526b2dabf", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881965177600, "endTime": 5881969468900}, "additional": {"logType": "info", "children": [], "durationId": "324ae682-cfc9-4bf2-b100-e9a76f11f74e"}}, {"head": {"id": "0d10dd45-9fb3-4103-899a-51c12ca61324", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881972441300, "endTime": 5881978078200}, "additional": {"children": [], "state": "success", "detailId": "2b9635a6-b433-4ff1-b44b-206d2cf3c2fc", "logId": "25103fea-fb8e-47c3-801f-adf5d792bafe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "2b9635a6-b433-4ff1-b44b-206d2cf3c2fc", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881970621900}, "additional": {"logType": "detail", "children": [], "durationId": "0d10dd45-9fb3-4103-899a-51c12ca61324"}}, {"head": {"id": "32a62d3b-05ad-42f4-9e73-a714b0da4048", "name": "entry : default@CacheNativeLibs start {\n  rss: 284737536,\n  heapTotal: 96858112,\n  heapUsed: 90357280,\n  external: 6138468,\n  arrayBuffers: 5174244\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881972426900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b1c56fe-5581-47c7-9c8e-e3519b9de7b2", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881973430700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bc559a7-db6e-4b3f-b780-750fb8a8995d", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881978023100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91ca9b51-a7d4-421a-be4e-fad08f6764a1", "name": "entry : default@CacheNativeLibs end {\n  rss: 284958720,\n  heapTotal: 96858112,\n  heapUsed: 88952320,\n  external: 6138468,\n  arrayBuffers: 112736\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881978062500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25103fea-fb8e-47c3-801f-adf5d792bafe", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881972441300, "endTime": 5881978078200}, "additional": {"logType": "info", "children": [], "durationId": "0d10dd45-9fb3-4103-899a-51c12ca61324"}}, {"head": {"id": "7f943d0a-4672-4bec-8bb4-0b3102371dcf", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881987072700, "endTime": 5882001765500}, "additional": {"children": [], "state": "success", "detailId": "ed66e22f-da6d-4134-b138-cd59bc23041e", "logId": "73e30ab0-f0bb-416d-a32c-450a1b6d54c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "ed66e22f-da6d-4134-b138-cd59bc23041e", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881979544100}, "additional": {"logType": "detail", "children": [], "durationId": "7f943d0a-4672-4bec-8bb4-0b3102371dcf"}}, {"head": {"id": "6f0415e5-a051-4b7e-885f-40066697ffdf", "name": "entry : default@PackageHap start {\n  rss: 285007872,\n  heapTotal: 96858112,\n  heapUsed: 89636568,\n  external: 1093344,\n  arrayBuffers: 129120\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881987051200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99c9c550-466f-4781-af41-8e67f6a0142b", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881987091100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89802a4e-769f-42a3-8ed5-1ad9aaa066c2", "name": "Incremental task entry:default@PackageHap pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882001699400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ad883cc-ed60-4d4b-9f47-614192f4b701", "name": "entry : default@PackageHap end {\n  rss: 285143040,\n  heapTotal: 97120256,\n  heapUsed: 88863304,\n  external: 1093344,\n  arrayBuffers: 120928\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882001751300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73e30ab0-f0bb-416d-a32c-450a1b6d54c8", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5881987072700, "endTime": 5882001765500}, "additional": {"logType": "info", "children": [], "durationId": "7f943d0a-4672-4bec-8bb4-0b3102371dcf"}}, {"head": {"id": "cd4ff016-be8f-4233-b92f-8a3d980c5355", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882009049600, "endTime": 5882011298900}, "additional": {"children": [], "state": "success", "detailId": "8b4e2c87-3e8f-4c7d-9aba-2c91369001f6", "logId": "e584c7aa-4575-4bf0-8112-034e2f95baed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "8b4e2c87-3e8f-4c7d-9aba-2c91369001f6", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882003327700}, "additional": {"logType": "detail", "children": [], "durationId": "cd4ff016-be8f-4233-b92f-8a3d980c5355"}}, {"head": {"id": "a179d9d5-9215-49fb-a367-1d7180851673", "name": "entry : default@SignHap start {\n  rss: 285175808,\n  heapTotal: 97120256,\n  heapUsed: 89481888,\n  external: 1109728,\n  arrayBuffers: 145504\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882008943200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d06e160a-4514-434a-8831-e862bbd8a822", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882009059700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30add8c6-d614-4276-8b1a-987010d23c41", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882011247200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04ea02ec-9693-4b4a-a23a-44b6b01b9e47", "name": "entry : default@SignHap end {\n  rss: 285188096,\n  heapTotal: 97120256,\n  heapUsed: 89578336,\n  external: 1109728,\n  arrayBuffers: 145504\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882011286900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e584c7aa-4575-4bf0-8112-034e2f95baed", "name": "UP-TO-DATE :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882009049600, "endTime": 5882011298900}, "additional": {"logType": "info", "children": [], "durationId": "cd4ff016-be8f-4233-b92f-8a3d980c5355"}}, {"head": {"id": "0051f21e-8288-489e-80d2-4d7216c096a2", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012397600, "endTime": 5882012435200}, "additional": {"children": [], "state": "success", "detailId": "29d334eb-b3fe-4e40-9856-d45b730e944a", "logId": "ae9d6b05-0103-4e14-b038-bc7827bf256a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "29d334eb-b3fe-4e40-9856-d45b730e944a", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012347300}, "additional": {"logType": "detail", "children": [], "durationId": "0051f21e-8288-489e-80d2-4d7216c096a2"}}, {"head": {"id": "4f24a9b1-da11-4dd5-a437-9c699a20e73a", "name": "entry : assembleHap start {\n  rss: 285216768,\n  heapTotal: 97120256,\n  heapUsed: 89717216,\n  external: 1109728,\n  arrayBuffers: 145504\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012387800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0118bac1-a71d-470a-af75-85211e8d2be7", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012403200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51e279f7-bdce-4f93-bbcf-e3501a4d73b7", "name": "entry : assembleHap end {\n  rss: 285216768,\n  heapTotal: 97120256,\n  heapUsed: 89725752,\n  external: 1109728,\n  arrayBuffers: 145504\n}", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012426100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae9d6b05-0103-4e14-b038-bc7827bf256a", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012397600, "endTime": 5882012435200}, "additional": {"logType": "info", "children": [], "durationId": "0051f21e-8288-489e-80d2-4d7216c096a2"}}, {"head": {"id": "95f4a608-7d90-48bb-b6ac-27b48be13c2b", "name": "BUILD SUCCESSFUL in 1 s 888 ms ", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012544700}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "6d2ecd8c-6fa6-48ea-944f-f6c7c773af86", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5880124576100, "endTime": 5882012604800}, "additional": {"time": {"year": 2025, "month": 6, "day": 23, "hour": 20, "minute": 58}, "markType": "history", "category": "build", "state": "success"}}, {"head": {"id": "adff1d2f-5aec-4286-a1e4-2b77b95c958e", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012622000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71e06db6-ab4a-400f-8401-e343d7a4bb32", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012674100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03afc4cf-8cd4-4a0b-afa4-33085cb19485", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012682900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52b10b4e-c0c3-48b6-b7b8-f220681d80da", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012691000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "323aba1d-c3e0-4f78-8196-a24c1f535812", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012700300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f3b2104-991b-4def-971d-d40ca618b1e8", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012705600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cede9afc-e441-48c1-9d35-7a0e5d1f7e9b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012710700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f86f8513-c765-4b33-bfc3-923734ebadd4", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012715400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52bb8e93-3b72-4c7a-9961-b038ffd3ffc7", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012719200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2d109b8-40f6-4c26-be75-cd02e2c4cb11", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882012724400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8aa90f75-8e37-4cd4-b7f1-236af957aa56", "name": "Update task entry:default@BuildJS input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882014051400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b474b78c-9fd5-44d6-b5ef-ccc8fff8bdeb", "name": "Update task entry:default@BuildJS input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882014423700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92cb0b81-9a4d-4cce-a3b4-539880482868", "name": "Update task entry:default@BuildJS input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882014592100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d030c13-4e04-418e-85da-465e66a4876d", "name": "Update task entry:default@BuildJS input file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882014726000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "182e63f0-3481-48be-bf0f-9c6ef1b189e2", "name": "Update task entry:default@BuildJS output file:D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882021846900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b66de374-5fa8-4e9b-bccd-d9a863fd1a64", "name": "Incremental task entry:default@BuildJS post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882022233000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d98697cd-10fb-44f4-ac3c-0019c80efd87", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882022249500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90d3ae54-b256-48f7-bde6-685ac106589c", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882022257500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e745a800-22a5-4af6-9014-2adff3e33618", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882022263800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ca1c238-3e49-45ce-b367-5a3958e43aef", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882022271600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d84bf24-94c4-4dba-91d7-1040d59ae3e7", "name": "There is no need to refresh cache, since the incremental task entry:default@SignHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 27036, "tid": "Main Thread", "startTime": 5882022278200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}