{"version": "1.0", "events": [{"head": {"id": "0f21b123-4dcf-4fa7-8c69-9e9952e26552", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 228262076200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "196f1782-d785-4f98-b8b9-69a1f06eb912", "name": "hvigor disconnect:  client namespace disconnect", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 228266754300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e96b1493-22e2-43e1-9454-87b2aa0013cf", "name": "hvigor daemon: Socket will be closed. socketId=e9jBe7B5KVCC07mTAAAB, connectionCount=0", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 228268498700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9b7c2e1-6cce-4d04-ad19-c088be6ef947", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":13992,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"374603e2a93290729479560149cd316583e3468d\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45003,\"lastUsedTime\":1750724842237,\"createdBy\":\"deveco\",\"sessionId\":\"00000050f2c7779363caab600ae772bfe5ea473155420b37462c77da6e37569d65f49ffc9742339120e20d49359f3e37764ddb78e5ac1d06af1d64f948bcb003f9e54a6277ebccc66a086991ab13071e7918db00a1fa293ce0030c00ab3832de\"}", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 228270676800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69caff79-9734-4ae5-90cf-486bea0b13e4", "name": "worker[0] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 228276814600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1654e0ef-6e94-43a2-8f93-92fe6c172baf", "name": "worker[1] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 228277039900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcf340ae-a01b-4d15-a166-cc36051c03a8", "name": "hvigor daemon: Socket is connected. socketId=qRu8heZx__vgIKWjAAAD, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231282467700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76260a48-6072-416b-94b3-6b8b8b2f4671", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"f7b69adf690ca42ff32bd80f36ca49655e39023f\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":14980,\"state\":\"broken\",\"lastUsedTime\":1750683812304,\"info\":\"The process with pid 14980 does not exist.\",\"createdBy\":\"deveco\",\"sessionId\":\"000000503ca0be744fa64d26a6effcee87a3c914ab0f7f157770145ea24c01e47dedd0b179847f70a0dcb42805a007c39a2c053e922e7ee561d5d2aa3df51625ead226d395c78bf1151eba98d47485cfebb79dfc81521531bcc82cd37112aa7b\"},{\"keyId\":\"fef7962a3805bfc5e665909552d6b84411df2805\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\testC\\\\Camera\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45001,\"pid\":9284,\"state\":\"idle\",\"lastUsedTime\":1750407074786,\"info\":null,\"createdBy\":\"deveco\",\"sessionId\":\"00000050aab88ac64b4178893fd796169b640639e1c9b813c06886269a431824f1c6089863679a3ccb64e3a10d6a82cfd481d956f2ce09eff3c9e009bf2456969ec7152898c9fea931f63ba2c72e0a1aee70f1f5847df547a40a94e65b6a302a\"},{\"keyId\":\"6fb3fb2c6a3b4bfc1d9fe7ec536c06acc0ff047a\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera_js_listenreceiver\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45002,\"pid\":5744,\"state\":\"broken\",\"lastUsedTime\":1750683722053,\"info\":\"The process with pid 5744 does not exist.\",\"createdBy\":\"deveco\",\"sessionId\":\"000000504dbf560f1b9f00153ab4d972675e40fd84b0caf5e80d9ac798762f629643a0f20b4a79089ee507a41cb94b52f4c8214cc538f0c6be469ab471b0fd25276b279894a9bcd238e90d414f2a53435f0b0f1d8e1560d9e118cdbc98e1a990\"},{\"keyId\":\"fd9e05b4d84c056550a5ca3011a9a345f2863f7c\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":27036,\"state\":\"stopped\",\"lastUsedTime\":1750683532028,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"0000005011a4a8137b923b9a1f8574e249fdc2b8abf50e8220c993b1f13dcaa360a4f09cb218793e0881f9579ab0a3fb0e188eade7c4d362e698abddd6a6c737fcaffdebabb3904bba293cb3df5ff7b3376bd0aaadf926390f76bd1444b1c369\"},{\"pid\":3640,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera_js\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"f874df03c252d9b978c6268998a8fef05e1c88dc\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45000,\"lastUsedTime\":1750724836570,\"createdBy\":\"deveco\",\"sessionId\":\"000000501d8d070b4087b11a023a3e1f9bfa5545a12b1003425527eda24f7b2977b10bd3d9a11a67d84d57dd3c7f853372e95629ac0c9e28517fb6b5195a71a9cff6f10b4d714fa2ac77d304a3af65d10440bc0ff2be25d3718736a703e31303\"},{\"pid\":9452,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\plantDemos\\\\Camera_js_listenreceiver\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"edf6b520205240556a32c664cc0ef8fccb254308\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45002,\"lastUsedTime\":1750724836524,\"createdBy\":\"deveco\",\"sessionId\":\"00000050c59e455540c1f8e0a0bba46988948eab5e9beb01aac9cec966195dce98c11fcddeda1e2c8acf87e0d509a5334446b1277048cc816ca6af753415c839c1895f566ba99530ba83920fff6d9ec7a013cf7a7c8981c31da3b675dba38b54\"},{\"pid\":13992,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"374603e2a93290729479560149cd316583e3468d\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45003,\"lastUsedTime\":1750724842252,\"createdBy\":\"deveco\",\"sessionId\":\"00000050f2c7779363caab600ae772bfe5ea473155420b37462c77da6e37569d65f49ffc9742339120e20d49359f3e37764ddb78e5ac1d06af1d64f948bcb003f9e54a6277ebccc66a086991ab13071e7918db00a1fa293ce0030c00ab3832de\"}]", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231283681600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2a215b1-fd10-4f51-9e60-c6530e98b8d7", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":13992,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"374603e2a93290729479560149cd316583e3468d\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45003,\"lastUsedTime\":1750724842252,\"createdBy\":\"deveco\",\"sessionId\":\"00000050f2c7779363caab600ae772bfe5ea473155420b37462c77da6e37569d65f49ffc9742339120e20d49359f3e37764ddb78e5ac1d06af1d64f948bcb003f9e54a6277ebccc66a086991ab13071e7918db00a1fa293ce0030c00ab3832de\"}", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231284694900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0940345a-333c-4af5-9742-c8bfb12ac86c", "name": "set active socket. socketId=qRu8heZx__vgIKWjAAAD", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231307276500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "227ebd54-9c27-454c-a0bc-47566ca9ee8d", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":13992,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"374603e2a93290729479560149cd316583e3468d\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45003,\"lastUsedTime\":1750724845265,\"createdBy\":\"deveco\",\"sessionId\":\"00000050f2c7779363caab600ae772bfe5ea473155420b37462c77da6e37569d65f49ffc9742339120e20d49359f3e37764ddb78e5ac1d06af1d64f948bcb003f9e54a6277ebccc66a086991ab13071e7918db00a1fa293ce0030c00ab3832de\"}", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231308484800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a613f49b-68bc-419c-b58f-9ccc9d31228d", "name": "hvigor daemon: Receive data from client. data={\n  mode: 'module',\n  parallel: true,\n  prop: [ 'module=entry', 'product=default' ],\n  incremental: true,\n  _: [ 'compileNative' ],\n  daemon: true\n}.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231312068600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "079c1f3e-7ff5-4f5a-aca3-b656c480b4c3", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231312714400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7415c99-3aec-4bca-b1a0-fa9a0b782d6c", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":13992,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"374603e2a93290729479560149cd316583e3468d\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45003,\"lastUsedTime\":1750724845289,\"createdBy\":\"deveco\",\"sessionId\":\"00000050f2c7779363caab600ae772bfe5ea473155420b37462c77da6e37569d65f49ffc9742339120e20d49359f3e37764ddb78e5ac1d06af1d64f948bcb003f9e54a6277ebccc66a086991ab13071e7918db00a1fa293ce0030c00ab3832de\"}", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231313632400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c77e70d8-6ce4-4e18-b795-90f92d170421", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231318904000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fec9bab1-a09c-455a-9297-42d8967d8554", "name": "Cache service initialization finished in 10 ms ", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231328090900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de71dfe3-b8a8-498b-b67b-fc455c9b8a92", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231346447000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31554450-ef0f-4fd4-80f9-b5dcf52f2911", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231355834900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6625ef79-d592-4983-953d-4b5069c837c4", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231355887800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dcd5200-d1fd-457b-ac9d-751d40b2b79a", "name": "hvigorfile, resolve finished D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231364854600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3bd3c43-5376-4b08-b4eb-10a358d1d74d", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231368379000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c549ae2d-c08a-4139-bfa1-a7e91df43b7f", "name": "hvigorfile, require result:  { hapTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231417830200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "462bee60-81eb-4b51-b071-5935c879e1a3", "name": "hvigorfile, binding system plugins { hapTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231417878800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67148a1d-b899-4d6a-908e-b6ce452bab8f", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231431778200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10d73f06-0291-4ab1-9523-fff161365b95", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231431852200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21e9c7e3-4f48-4387-a769-6aa160e46073", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231436756900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef2eaec0-a6e7-4da7-8eda-5026db1ee373", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231436804000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bb5ba6b-5fe9-4406-978a-d9dac45cd796", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231437050000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45249664-1cd9-4cd9-b6ea-e01c14597fdf", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231437469100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cf2d47f-5623-4d86-bbb0-cae775322a00", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231437491800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e5d28ff-223d-4042-ab1b-d659a343bb9e", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231437499800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "660be331-1efa-4957-a9bb-e571b3d27d68", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231437527400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "931e9db9-7063-47b4-95c2-e12c21bcbe05", "name": "require SDK: toolchains,ArkTS,native; moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231438914300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71d2cd75-f73a-4236-ba41-0d7b979e1915", "name": "Module entry task initialization takes 9 ms ", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231451337800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26b7c6c5-ed60-423f-844f-f4c412fe8e63", "name": "hvigorfile, resolve finished D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231451384100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64923c6b-e326-4257-a1ea-8a95fe0efb4a", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231451449000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15871bcc-8d34-4a59-b8b0-0d3d1846fd1b", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231451468000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "961bfd9a-dd68-4d4f-96dc-0cf596842a0d", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231451512900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e908998-b163-41f6-b50e-45c46cfd02cc", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231451522400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc78cdc7-d837-4127-930c-f8b8e3b719b9", "name": "Mo<PERSON>le Camera Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231454251500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffee7187-9448-4f8a-8305-c0ddc3719e86", "name": "Module Camera's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231454279700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77a159e2-92a2-498a-8714-fce92e723613", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231456619200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9aa635d-d9fb-4fa9-834d-d259f7b2fbfd", "name": "Sdk init in 9 ms ", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231475085300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0389b26c-7085-40ca-beaf-bd918114e1f3", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231558621400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80dceea7-2495-4f4c-afbd-9f70d845207d", "name": "Project task initialization takes 207 ms ", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231681665700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32e76f88-effe-414e-8c20-5011f61214a1", "name": "Sdk init in 14 ms ", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231695105500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c04f7a3e-b0dd-4290-9037-be9b00b9d98f", "name": "Configuration phase cost:373 ms ", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231700325900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f86193a-8d2d-490b-a89b-438008c445e5", "name": "Configuration task cost before running: 389 ms ", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231705706700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9069819c-d77e-46de-bb52-d3640e78fc19", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231720368500, "endTime": 231737928000}, "additional": {"children": [], "state": "success", "detailId": "b1499191-ff07-4ec7-8461-23d4131b4f14", "logId": "83763282-7566-454e-8f58-b48163a62ef5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "b1499191-ff07-4ec7-8461-23d4131b4f14", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231708307000}, "additional": {"logType": "detail", "children": [], "durationId": "9069819c-d77e-46de-bb52-d3640e78fc19"}}, {"head": {"id": "a424961d-d722-416b-aa18-876145e24f43", "name": "entry : default@PreBuild start {\n  rss: 187203584,\n  heapTotal: 125861888,\n  heapUsed: 105528224,\n  external: 1691478,\n  arrayBuffers: 726122\n}", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231720326600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3af6cf9d-16cc-4990-aee3-994c9b854a11", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231720389800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37d57758-11a3-44e0-8c03-39b0837032a3", "name": "Incremental task entry:default@PreBuild pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231737659400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f56dfd5-275d-404c-a533-ec22d3cb605d", "name": "entry : default@PreBuild end {\n  rss: 187310080,\n  heapTotal: 125861888,\n  heapUsed: 105713424,\n  external: 1699670,\n  arrayBuffers: 734314\n}", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231737814600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83763282-7566-454e-8f58-b48163a62ef5", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231720368500, "endTime": 231737928000}, "additional": {"logType": "info", "children": [], "durationId": "9069819c-d77e-46de-bb52-d3640e78fc19"}}, {"head": {"id": "74a916fd-5489-40fa-a7c1-e0a141398065", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231745878600, "endTime": 234372042700}, "additional": {"children": ["4cb8690e-65b8-4951-8f7a-bdcf54c08715", "eabb9f15-5be8-4e91-ac25-ef8b4042798e", "07316e65-4c08-4364-88bc-90842e5d1066"], "state": "success", "detailId": "1278c816-9f72-45cc-9c16-b4c746fcec52", "logId": "42504c3a-7b4f-458f-967d-2751a01f7bef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "1278c816-9f72-45cc-9c16-b4c746fcec52", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231744602800}, "additional": {"logType": "detail", "children": [], "durationId": "74a916fd-5489-40fa-a7c1-e0a141398065"}}, {"head": {"id": "34bb2fae-b996-405f-9953-b51c2d775504", "name": "entry : default@BuildNativeWithCmake start {\n  rss: 187760640,\n  heapTotal: 125861888,\n  heapUsed: 105949688,\n  external: 1699670,\n  arrayBuffers: 734314\n}", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231745846600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8846a1e0-6541-4250-8dde-0a0f12e9d5c3", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231745899200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a159606-2f03-466a-a9fa-432177f8f4ac", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\arm64-v8a',\n  '-DOHOS_ARCH=arm64-v8a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\arm64-v8a',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=arm64-v8a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231791931600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95b37dfd-f19e-4dcf-8a9e-1f23a93df896", "name": "default@BuildNativeWithCmake work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231794497500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cb8690e-65b8-4951-8f7a-bdcf54c08715", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 13992, "tid": "Worker0", "startTime": 231796974900, "endTime": 234146869600}, "additional": {"children": [], "state": "success", "parent": "74a916fd-5489-40fa-a7c1-e0a141398065", "logId": "42130c09-ca47-4f7a-95b8-7f37035124f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "f160b650-a143-4410-bc8c-7bd03b204e63", "name": "default@BuildNativeWithCmake work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231795998000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8778901-ac8b-484b-a880-e72e509fe64a", "name": "default@BuildNativeWithCmake work[0] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231797094700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55e3d759-9137-4c27-8921-4bb9e2800223", "name": "default@BuildNativeWithCmake work[0] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231797309800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe5c47c5-f97d-48cf-83b0-f1a52031e81d", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\armeabi-v7a',\n  '-DOHOS_ARCH=armeabi-v7a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\armeabi-v7a',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=armeabi-v7a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231800732100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbeafeac-b862-441b-be60-64b83f74d2d7", "name": "default@BuildNativeWithCmake work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231802593500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eabb9f15-5be8-4e91-ac25-ef8b4042798e", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 13992, "tid": "Worker1", "startTime": 231803461200, "endTime": 234138151300}, "additional": {"children": [], "state": "success", "parent": "74a916fd-5489-40fa-a7c1-e0a141398065", "logId": "680d7e37-8068-4679-b4fb-a8ef82a820fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "865d3386-0ddb-468a-b07f-f918437f672f", "name": "default@BuildNativeWithCmake work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231803325600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24668575-b073-431a-b321-871d75d90a86", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231803353300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fa7bed4-8ca1-4d40-b0fa-0725f30db298", "name": "default@BuildNativeWithCmake work[1] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231803475400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e3cbee9-d8d2-4b17-8291-a544a1154fcc", "name": "default@BuildNativeWithCmake work[1] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231803507400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "769eb944-f273-48ca-b4c9-f3734cf05fc6", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\x86_64',\n  '-DOHOS_ARCH=x86_64',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\x86_64',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=x86_64',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231807429400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7215cdeb-9f69-4d8b-be1b-4647b4ae30a8", "name": "default@BuildNativeWithCmake work[2] is submitted.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231809046000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07316e65-4c08-4364-88bc-90842e5d1066", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 13992, "tid": "Worker1", "startTime": 234138946200, "endTime": 234371915400}, "additional": {"children": [], "state": "success", "parent": "74a916fd-5489-40fa-a7c1-e0a141398065", "logId": "6d3cb963-75ae-4839-8869-49aee09d62c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "35ad3075-2c87-48fd-9e2c-1ea9a0009bac", "name": "default@BuildNativeWithCmake work[2] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231809771400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bff6ecf2-790b-4c8c-8735-95843fcc1e09", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231809799300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d74d6b79-701b-4b4e-b5d2-0481a1091011", "name": "A work dispatched to worker[1] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231809813800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9eaa674-0694-4341-aea1-7f98b1806881", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231809836800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f33f8d1-73ff-4ac3-ac44-032cac0658f3", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231809849700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a4741f8-2d0a-4209-a454-c02dc6167ed0", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231809864500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9a9a059-5d96-4bc0-9b11-5536dbc563b9", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231809879500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b30ee854-6c43-4a96-9630-f284363dddc8", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231809892900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca4baf9a-e68f-4c87-9b82-6adf28acb06e", "name": "default@BuildNativeWithCmake work[2] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231809901200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30644fa0-1b90-44f4-9847-2164885b8cea", "name": "entry : default@BuildNativeWithCmake end {\n  rss: 189612032,\n  heapTotal: 126124032,\n  heapUsed: 106892656,\n  external: 1699670,\n  arrayBuffers: 734314\n}", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231809997000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72f03643-dde8-43fe-9ecb-49a965e472b2", "name": "default@BuildNativeWithCmake work[1] done.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 234138409700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "680d7e37-8068-4679-b4fb-a8ef82a820fa", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 13992, "tid": "Worker1", "startTime": 231803461200, "endTime": 234138151300}, "additional": {"logType": "info", "children": [], "durationId": "eabb9f15-5be8-4e91-ac25-ef8b4042798e", "parent": "42504c3a-7b4f-458f-967d-2751a01f7bef"}}, {"head": {"id": "00d97fd7-860d-4d15-b630-6a2be9bd2b79", "name": "default@BuildNativeWithCmake work[2] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 234138966900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a96e2c68-4f52-4e53-908c-b596e5e7ed81", "name": "default@BuildNativeWithCmake work[0] done.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 234146932600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42130c09-ca47-4f7a-95b8-7f37035124f8", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 13992, "tid": "Worker0", "startTime": 231796974900, "endTime": 234146869600}, "additional": {"logType": "info", "children": [], "durationId": "4cb8690e-65b8-4951-8f7a-bdcf54c08715", "parent": "42504c3a-7b4f-458f-967d-2751a01f7bef"}}, {"head": {"id": "4f0fa665-f72a-45cc-98bd-1ca304d5e90b", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 234147071400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cae7d2d-c4cd-4dc5-acaa-4f1423da79b7", "name": "default@BuildNativeWithCmake work[2] done.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 234371958900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d3cb963-75ae-4839-8869-49aee09d62c7", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 13992, "tid": "Worker1", "startTime": 234138946200, "endTime": 234371915400}, "additional": {"logType": "info", "children": [], "durationId": "07316e65-4c08-4364-88bc-90842e5d1066", "parent": "42504c3a-7b4f-458f-967d-2751a01f7bef"}}, {"head": {"id": "42504c3a-7b4f-458f-967d-2751a01f7bef", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231745878600, "endTime": 234372042700}, "additional": {"logType": "info", "children": ["42130c09-ca47-4f7a-95b8-7f37035124f8", "680d7e37-8068-4679-b4fb-a8ef82a820fa", "6d3cb963-75ae-4839-8869-49aee09d62c7"], "durationId": "74a916fd-5489-40fa-a7c1-e0a141398065"}}, {"head": {"id": "c3379230-e5f2-42e0-b4d2-0956e9e836cd", "name": "entry:compileNative", "description": "Compile the hook task for native resources.", "type": "duration"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 234376994500, "endTime": 234377498800}, "additional": {"children": [], "state": "success", "detailId": "7ddf0828-0c4f-4f11-b41d-aaadf4eed6b0", "logId": "1adb3312-e21e-4cae-9bab-db4890b5ea00"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "7ddf0828-0c4f-4f11-b41d-aaadf4eed6b0", "name": "create entry:compileNative task", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 234376721700}, "additional": {"logType": "detail", "children": [], "durationId": "c3379230-e5f2-42e0-b4d2-0956e9e836cd"}}, {"head": {"id": "4540f928-abb9-4a23-afd3-c5776d94e0cd", "name": "entry : compileNative start {\n  rss: 293318656,\n  heapTotal: 126763008,\n  heapUsed: 94447136,\n  external: 1699670,\n  arrayBuffers: 71704\n}", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 234376951700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bf4fa2d-d3cc-4865-ad23-43238909c0f2", "name": "Executing task :entry:compileNative", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 234377256900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aec2ca07-bf57-4801-9d32-bee3d4f05bcc", "name": "entry : compileNative end {\n  rss: 293326848,\n  heapTotal: 126763008,\n  heapUsed: 94456376,\n  external: 1699670,\n  arrayBuffers: 71704\n}", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 234377454600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1adb3312-e21e-4cae-9bab-db4890b5ea00", "name": "Finished :entry:compileNative", "description": "Compile the hook task for native resources.", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 234376994500, "endTime": 234377498800}, "additional": {"logType": "info", "children": [], "durationId": "c3379230-e5f2-42e0-b4d2-0956e9e836cd"}}, {"head": {"id": "f9665606-24e4-4e81-9577-4281379ad316", "name": "BUILD SUCCESSFUL in 3 s 61 ms ", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 234378156000}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "120c0666-3b32-45a0-803b-eca2b2dfda0b", "name": "compileNative", "description": "", "type": "mark"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 231317445300, "endTime": 234379160000}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 8, "minute": 27}, "markType": "history", "category": "build", "state": "success"}}, {"head": {"id": "08bda91e-d9a5-42b5-b504-a80a55eac05a", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 13992, "tid": "Main Thread", "startTime": 234379502400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}