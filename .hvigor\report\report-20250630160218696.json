{"version": "1.0", "events": [{"head": {"id": "df99df80-4117-4d03-8dd3-de64078f71ea", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4684983959100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d702b757-eb05-468f-bbb8-7fd3203ebdd0", "name": "hvigor disconnect:  client namespace disconnect", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4684987041100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f32af45-db4b-4696-acef-2633851d2e82", "name": "hvigor daemon: Socket will be closed. socketId=paQg_Jr0a1diVEr4AAAB, connectionCount=0", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4684988196000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db65d151-c614-4f3d-8fa5-7812a0b3a9d3", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":28700,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"9d54ae87d0273eeef32dca1ac4d4f2c06d01ad68\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45004,\"lastUsedTime\":1751270534924,\"createdBy\":\"deveco\",\"sessionId\":\"00000050820b4cc894f43db4825031b427182f55f32b111dc34068f985f05b82527d8596cabba3bed4c1164f572065e4e5e03873ee693a9b5ea6b9ae3a118b155b7889da424cc4f0d804b0f5302ca61d29ade67f90cc969996ef49e04b10fc47\"}", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4684989135500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a16186e-55b7-4935-b758-f8516b6a6a35", "name": "worker[0] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4684992854000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ffe7eb4-675f-49d6-bc79-cf06a04b0c7d", "name": "worker[1] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4684993134500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b03e6194-bff5-4631-a460-92feec0fc9f0", "name": "hvigor daemon: Socket is connected. socketId=sZ_hHWa23i8E1XVTAAAD, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686002881600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08dcd1f3-a6c1-4cb1-8684-5ea6054cde65", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"1e7c5d3bb2b9298b64c7ab1c20637b3fc5f294a4\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\listenData\",\"nodeVersion\":\"v16.20.2\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45001,\"pid\":13196,\"state\":\"half_busy\",\"lastUsedTime\":1751267667932,\"info\":null,\"createdBy\":\"deveco\",\"sessionId\":\"00000050b369795f1c1cd041a3eed0f6393fa0197605b1860d2f8e42457790906e2caf83bc605bed3f178ef7436f33a38459aafad55ca9152ed8e1667561cb9bc85df8e743fe8d12ec4182128493c78857036669f86c5edfc6baaaec90aeef1b\"},{\"keyId\":\"1ad51216a89c056f91b7029a6873a771cf631d03\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\mainwork\\\\demo042302\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45002,\"pid\":13344,\"state\":\"stopped\",\"lastUsedTime\":1751268201051,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"000000509f56a5f6731eba5f6609b7e5c205bab15a0e2200f981066b99526830b4685b219f05ba7076ff7c1d0b5fb2626aabc4cd5291aae3eaefdbffd08255a641fbb8e71ae136c2fa5662b76eb205d097c4cb97d073b7c2b8728aeabda5ee50\"},{\"keyId\":\"d93817213720e1274aa00f7cdfed915459b5fe2c\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\mainwork\\\\fileManagerNew\",\"nodeVersion\":\"v16.20.2\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":5200,\"state\":\"stopped\",\"lastUsedTime\":1751267224573,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"00000050f9eb27b81e879d6acc09cf5de1d9fee1ea88938001577ff165e2a7fa856d5402b426a82381c7f98eb3315978afb8f04ced8bbf95ec9965e3687cf4cf6cd3ed556aca26ef3f4099f867020af37bec23504c6314bb1284409e3efa1846\"},{\"pid\":16828,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\deviceDemo\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"a136dcf8d35c238425aeefe74588d626bb9e1bf5\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v16.20.2\",\"port\":45000,\"lastUsedTime\":1751269464229,\"createdBy\":\"deveco\",\"sessionId\":\"00000050ed5f9a07e700c9bd470d9a0979bca1faaec84b1a62f1144f0f6fe779724529f89267c8e10c4a2335a6e8ba7fb627130a68a0e0d4c96274458bd3f4a145f1af96cf6fc93c609a5c17474e11bbb60650948145e216b78c7a1506600a46\"},{\"pid\":18244,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\mainwork\\\\fileManagerNew\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"5d3d2d97117c5868a950ac218390496f3af66ca2\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v16.20.2\",\"port\":45002,\"lastUsedTime\":1751270216245,\"createdBy\":\"deveco\",\"sessionId\":\"000000506100d41530c3c720187f9c86d61cad8af28bab60d4a0ee7cec2f3dff1b6167e6919f80fbfbd65bbf0e1e6648108df5378c2225c24e6e129a1ed2cce9729d300c0d5b2388c19f698b95a71c3ad5a3e71139493937808f865d159cbbbc\"},{\"pid\":19328,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\fileManager\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"e32be9f074620fb7a194b6bb0beb31aea0dd97d7\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45003,\"lastUsedTime\":1751270481642,\"createdBy\":\"deveco\",\"sessionId\":\"00000050a27e030ade1908902b5355740a78da481d47e305ce058f151fbb824270bb26740d15ede53ae92b13944ed88cbf4d57ca3274a5a6a0ad3ce88b9ecfb929335473e2bbcb557b30a79350bf375a74b0ed64c393387ca678070231fc5409\"},{\"pid\":28700,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"9d54ae87d0273eeef32dca1ac4d4f2c06d01ad68\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45004,\"lastUsedTime\":1751270534933,\"createdBy\":\"deveco\",\"sessionId\":\"00000050820b4cc894f43db4825031b427182f55f32b111dc34068f985f05b82527d8596cabba3bed4c1164f572065e4e5e03873ee693a9b5ea6b9ae3a118b155b7889da424cc4f0d804b0f5302ca61d29ade67f90cc969996ef49e04b10fc47\"}]", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686004243800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edfad290-f675-4458-b708-306fbbb6df42", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":28700,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"9d54ae87d0273eeef32dca1ac4d4f2c06d01ad68\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45004,\"lastUsedTime\":1751270534933,\"createdBy\":\"deveco\",\"sessionId\":\"00000050820b4cc894f43db4825031b427182f55f32b111dc34068f985f05b82527d8596cabba3bed4c1164f572065e4e5e03873ee693a9b5ea6b9ae3a118b155b7889da424cc4f0d804b0f5302ca61d29ade67f90cc969996ef49e04b10fc47\"}", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686005382100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0a0b0fa-41a3-44f1-b30e-1725f0184fb4", "name": "set active socket. socketId=sZ_hHWa23i8E1XVTAAAD", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686011008100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "022d60e5-b69f-491c-84cc-e0d6c30838d8", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":28700,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"9d54ae87d0273eeef32dca1ac4d4f2c06d01ad68\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45004,\"lastUsedTime\":1751270535949,\"createdBy\":\"deveco\",\"sessionId\":\"00000050820b4cc894f43db4825031b427182f55f32b111dc34068f985f05b82527d8596cabba3bed4c1164f572065e4e5e03873ee693a9b5ea6b9ae3a118b155b7889da424cc4f0d804b0f5302ca61d29ade67f90cc969996ef49e04b10fc47\"}", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686012043900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "405c04b2-3706-4faf-a4e9-655431e8a8c4", "name": "hvigor daemon: Receive data from client. data={\n  mode: 'module',\n  parallel: true,\n  prop: [ 'module=entry', 'product=default' ],\n  incremental: true,\n  _: [ 'compileNative' ],\n  daemon: true\n}.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686016236500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "897c5bc8-3e05-4093-9696-913e73ee780a", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686016902200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feaf2432-c1dc-40c5-9ad8-3f90dc810e48", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":28700,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"9d54ae87d0273eeef32dca1ac4d4f2c06d01ad68\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45004,\"lastUsedTime\":1751270535957,\"createdBy\":\"deveco\",\"sessionId\":\"00000050820b4cc894f43db4825031b427182f55f32b111dc34068f985f05b82527d8596cabba3bed4c1164f572065e4e5e03873ee693a9b5ea6b9ae3a118b155b7889da424cc4f0d804b0f5302ca61d29ade67f90cc969996ef49e04b10fc47\"}", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686017829900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ba32e81-4aec-43c4-82c4-ff59a8159dbd", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686023228200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84bbb98b-4da7-440a-9d77-c26c4e3421f6", "name": "Cache service initialization finished in 10 ms ", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686032684400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "229c6a69-97c5-4d2f-9075-27c537544f87", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686040565400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40d6789d-a393-4f11-ad28-56c68512d44e", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686049783300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba482d76-f036-4397-8990-840ce3cbdbc6", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686049838600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9167fa26-9f82-45b3-ba66-d812b1d06d0e", "name": "hvigorfile, resolve finished D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686058661300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "621de839-e65e-4113-9b30-edb9c1e02cda", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686062305500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cdb0204-0062-49b7-ae05-79a06ac89d17", "name": "hvigorfile, require result:  { hapTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686081483500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fcc634c-6993-41c7-a296-4f9a242d863b", "name": "hvigorfile, binding system plugins { hapTasks: [Get<PERSON>] }", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686081536100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8185a30-1d5c-46a8-8e3a-8a36e316f77f", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686096689400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80f5bc78-e01e-456e-867e-f12e773fcdd8", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686096725600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62c83112-2e0c-473b-bc22-b4b5201d95b2", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686101480600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c0ff1f1-527a-4165-b1a6-d6399586d02a", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686101526200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6be1296-7d57-4a37-a44b-88a2f5857e9c", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686101798200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e48496c6-3010-4e8c-8a3f-7c997e8173d8", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686102269200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb26d4fb-e259-4153-9376-cd3fdbebf775", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686102293700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6e4d0a5-2659-4b3a-a3a8-fa2ec99df821", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686102302900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13f862a4-9750-4b79-8e1e-25615536e994", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"externalNativeOptions\": {\n    \"path\": \"./src/main/cpp/CMakeLists.txt\",\n    \"arguments\": \"-v\",\n    \"abiFilters\": [\n      \"arm64-v8a\",\n      \"armeabi-v7a\",\n      \"x86_64\"\n    ],\n    \"cppFlags\": \"\"\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686102334700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d671fcdf-a038-4ca9-8b37-c81e6d941f2e", "name": "require SDK: toolchains,ArkTS,native; moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686103795200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdaa5ff0-accd-422d-8cd6-4719a7126238", "name": "Module entry task initialization takes 7 ms ", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686113326800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "689bb7f0-68bd-47b4-8cba-723244775e48", "name": "hvigorfile, resolve finished D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686113377900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6befcd5b-1764-4f6d-8e00-8222b00073a2", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686113449000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a1e53ac-5f83-4d7c-aced-51d1ed18c8f5", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686113470600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e21db8c1-3807-435c-92df-7e3c09e80894", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686113715900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2df82a6-8f82-4124-8e79-c4bd11c84775", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686113742400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ac7c5d1-8922-4b52-889d-03aee7ff2767", "name": "Mo<PERSON>le Camera Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686114757300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5671e1c6-50e4-4d04-a033-a45aceba7337", "name": "Module Camera's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686114810600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85db34b2-794e-4e77-bee7-e445c0cb79aa", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686117778000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44bcac35-62a6-4b09-9a2e-63a77e94320b", "name": "Sdk init in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686128562500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b130fa97-2ec7-49ee-b83b-71b38ab5f430", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686179780200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a676958-2c46-4eaf-99e5-e0e39c36da1d", "name": "Project task initialization takes 58 ms ", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686185836100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d645f44c-1cb1-49da-831c-66260ce07622", "name": "Sdk init in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686191745700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0598478-2865-4bda-b969-e734f5fecd06", "name": "Configuration phase cost:162 ms ", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686194551800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdb57137-2d8b-4bfe-8708-641d5239282b", "name": "Configuration task cost before running: 174 ms ", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686195734700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2d0c1f6-536d-47a7-a9aa-9e52e76aa78b", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686208454500, "endTime": 4686223555500}, "additional": {"children": [], "state": "success", "detailId": "a2430191-5f30-4b7b-ae17-d934acc4f6e3", "logId": "2600a499-dda4-402e-a79c-8efb0edadf22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "a2430191-5f30-4b7b-ae17-d934acc4f6e3", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686199458100}, "additional": {"logType": "detail", "children": [], "durationId": "d2d0c1f6-536d-47a7-a9aa-9e52e76aa78b"}}, {"head": {"id": "ef4160d4-efb1-46c7-a38b-39272a091901", "name": "entry : default@PreBuild start {\n  rss: 191873024,\n  heapTotal: 125861888,\n  heapUsed: 105439440,\n  external: 1699842,\n  arrayBuffers: 734486\n}", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686208399300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e010facf-a3d5-441c-983c-e5ddd8226dde", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686208475900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7b9988c-1dce-49b6-843a-a3b91e03cf64", "name": "Incremental task entry:default@PreBuild pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686223273900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "988c8f0e-dc1e-4773-b686-c5c11de97e2e", "name": "entry : default@PreBuild end {\n  rss: 193474560,\n  heapTotal: 125861888,\n  heapUsed: 105624640,\n  external: 1708034,\n  arrayBuffers: 742678\n}", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686223444300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2600a499-dda4-402e-a79c-8efb0edadf22", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686208454500, "endTime": 4686223555500}, "additional": {"logType": "info", "children": [], "durationId": "d2d0c1f6-536d-47a7-a9aa-9e52e76aa78b"}}, {"head": {"id": "1b22cf60-9bd3-42f4-9928-5db1606fb5ec", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686234021000, "endTime": 4688726836600}, "additional": {"children": ["abecb2f1-a262-49e9-9062-d02d81fa11b4", "45892a20-a2de-41ad-b2bf-3acd782ac5ad", "a3b62a96-6e0b-41c2-9702-6a92ac5daeab"], "state": "success", "detailId": "914a9394-1747-482d-821e-0f83846f2839", "logId": "1a1128e9-35ef-4e7b-9885-39bd9cf0503b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "914a9394-1747-482d-821e-0f83846f2839", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686227344400}, "additional": {"logType": "detail", "children": [], "durationId": "1b22cf60-9bd3-42f4-9928-5db1606fb5ec"}}, {"head": {"id": "583ed5ce-b007-4690-bdc7-35b8fc959200", "name": "entry : default@BuildNativeWithCmake start {\n  rss: 194596864,\n  heapTotal: 125861888,\n  heapUsed: 105860880,\n  external: 1708034,\n  arrayBuffers: 742678\n}", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686233986500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d8fbd8b-4b83-4a46-a676-0a8e27c56739", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686234039500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f605226-ac81-4c9b-9251-7192753d9c4c", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\arm64-v8a',\n  '-DOHOS_ARCH=arm64-v8a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\arm64-v8a',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=arm64-v8a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686242421900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a65069b-e845-4564-927b-54fb7a83d2b6", "name": "default@BuildNativeWithCmake work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686244898100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abecb2f1-a262-49e9-9062-d02d81fa11b4", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 28700, "tid": "Worker0", "startTime": 4686247233200, "endTime": 4688414908600}, "additional": {"children": [], "state": "success", "parent": "1b22cf60-9bd3-42f4-9928-5db1606fb5ec", "logId": "fbf6fc35-c747-4ac0-9e5c-96795d95fb10"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "7244efe8-d425-42e4-a49f-517390b1f79c", "name": "default@BuildNativeWithCmake work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686246221900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1400f155-8bd1-4cd1-aff7-5b12e16b85f7", "name": "default@BuildNativeWithCmake work[0] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686247329100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56f36c61-1caa-4c5b-a7d8-f1346145bb20", "name": "default@BuildNativeWithCmake work[0] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686247531400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f0cf16b-f9a5-4f93-8ecb-c46ac5abc507", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\armeabi-v7a',\n  '-DOHOS_ARCH=armeabi-v7a',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\armeabi-v7a',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=armeabi-v7a',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686251426500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "933e20b9-b8eb-447b-afb2-6b655f25ed00", "name": "default@BuildNativeWithCmake work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686253238300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45892a20-a2de-41ad-b2bf-3acd782ac5ad", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 28700, "tid": "Worker1", "startTime": 4686254288500, "endTime": 4688427095700}, "additional": {"children": [], "state": "success", "parent": "1b22cf60-9bd3-42f4-9928-5db1606fb5ec", "logId": "361202d5-211a-4623-84a6-0e75aaca3bc7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "80e52edb-8634-41f9-b784-283446d53962", "name": "default@BuildNativeWithCmake work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686254154600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bb3b86e-7423-4f20-8dd7-ad91f19a473f", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686254185900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e79963d-d60b-4766-97b4-29331e9c53c1", "name": "default@BuildNativeWithCmake work[1] has been dispatched to worker[1].", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686254306200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de7c4bea-5b76-4284-8f1e-c9581cba28b3", "name": "default@BuildNativeWithCmake work[1] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686254338200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f482f8a7-d27f-49e9-827a-0edcfd30ed22", "name": "Use tool [Cmake]\n [\n  'D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\cmake.exe',\n  '-v',\n  '-HD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\src\\\\main\\\\cpp',\n  '-BD:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\.cxx\\\\default\\\\default\\\\x86_64',\n  '-DOHOS_ARCH=x86_64',\n  '-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\Camera\\\\entry\\\\build\\\\default\\\\intermediates\\\\cmake\\\\default\\\\obj\\\\x86_64',\n  '-DCMAKE_BUILD_TYPE=Debug',\n  '-DOHOS_SDK_NATIVE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native',\n  '-DCMAKE_SYSTEM_NAME=OHOS',\n  '-DCMAKE_OHOS_ARCH_ABI=x86_64',\n  '-DCMAKE_EXPORT_COMPILE_COMMANDS=ON',\n  '-DCMAKE_TOOLCHAIN_FILE=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build\\\\cmake\\\\ohos.toolchain.cmake',\n  '-GNinja',\n  '-DCMAKE_MAKE_PROGRAM=D:\\\\harmonyFor\\\\openSDK\\\\11\\\\native\\\\build-tools\\\\cmake\\\\bin\\\\ninja.exe',\n  '--no-warn-unused-cli'\n]", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686257871500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30b63277-90ed-4aac-a0bc-9627776d1960", "name": "default@BuildNativeWithCmake work[2] is submitted.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686259303100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3b62a96-6e0b-41c2-9702-6a92ac5daeab", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 28700, "tid": "Worker0", "startTime": 4688417216200, "endTime": 4688726700100}, "additional": {"children": [], "state": "success", "parent": "1b22cf60-9bd3-42f4-9928-5db1606fb5ec", "logId": "26eed954-c66e-447e-a7c6-80d86cb0ef00"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "a2530396-1c39-43ee-b8f5-3ac7e6e43032", "name": "default@BuildNativeWithCmake work[2] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686259953800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32ae8e37-b70d-4673-8a3c-348a57277725", "name": "A work dispatched to worker[0] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686259976700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f22a583b-8ed4-4ddb-885f-5a78464a2c6d", "name": "A work dispatched to worker[1] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686259986500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00ffc09e-5a16-44a9-a153-f7b3d9c672a1", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686259995500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e21e59cf-cbd4-4219-b224-2859e5ebf9b4", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686260003100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cef8434-b2ef-46bf-9341-f11ab09eb687", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686260012100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6a07d37-21a5-48d3-9527-a4b27b4ebd42", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686260023900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "872cddcf-d848-44a5-b6cc-5c6e21ee1346", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686260036400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55c30e9f-e856-4c6f-9d71-00856d4ab82c", "name": "default@BuildNativeWithCmake work[2] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686260042500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c4da301-f4a1-43b2-9533-be080b71e35c", "name": "entry : default@BuildNativeWithCmake end {\n  rss: 196472832,\n  heapTotal: 126124032,\n  heapUsed: 106809024,\n  external: 1708034,\n  arrayBuffers: 742678\n}", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686260124200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8ff3dcf-03f6-434b-b8ca-1b7bffcbbcc0", "name": "default@BuildNativeWithCmake work[0] done.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4688415582200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbf6fc35-c747-4ac0-9e5c-96795d95fb10", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 28700, "tid": "Worker0", "startTime": 4686247233200, "endTime": 4688414908600}, "additional": {"logType": "info", "children": [], "durationId": "abecb2f1-a262-49e9-9062-d02d81fa11b4", "parent": "1a1128e9-35ef-4e7b-9885-39bd9cf0503b"}}, {"head": {"id": "09c51c20-ea15-47fd-ac2a-ecf1bea01e30", "name": "default@BuildNativeWithCmake work[2] has been dispatched to worker[0].", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4688417359300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a21e81b8-13a1-4d6b-825f-23fbd49af4b8", "name": "default@BuildNativeWithCmake work[1] done.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4688427170300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "361202d5-211a-4623-84a6-0e75aaca3bc7", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 28700, "tid": "Worker1", "startTime": 4686254288500, "endTime": 4688427095700}, "additional": {"logType": "info", "children": [], "durationId": "45892a20-a2de-41ad-b2bf-3acd782ac5ad", "parent": "1a1128e9-35ef-4e7b-9885-39bd9cf0503b"}}, {"head": {"id": "23d40b9b-9307-431b-9cc9-fa106253ca35", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4688427443300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f40d8ca-1107-458e-b622-5a6768d2620c", "name": "default@BuildNativeWithCmake work[2] done.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4688726750500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26eed954-c66e-447e-a7c6-80d86cb0ef00", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 28700, "tid": "Worker0", "startTime": 4688417216200, "endTime": 4688726700100}, "additional": {"logType": "info", "children": [], "durationId": "a3b62a96-6e0b-41c2-9702-6a92ac5daeab", "parent": "1a1128e9-35ef-4e7b-9885-39bd9cf0503b"}}, {"head": {"id": "1a1128e9-35ef-4e7b-9885-39bd9cf0503b", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686234021000, "endTime": 4688726836600}, "additional": {"logType": "info", "children": ["fbf6fc35-c747-4ac0-9e5c-96795d95fb10", "361202d5-211a-4623-84a6-0e75aaca3bc7", "26eed954-c66e-447e-a7c6-80d86cb0ef00"], "durationId": "1b22cf60-9bd3-42f4-9928-5db1606fb5ec"}}, {"head": {"id": "1e11ac32-d1b1-4e6e-b9d5-9280d5a43da0", "name": "entry:compileNative", "description": "Compile the hook task for native resources.", "type": "duration"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4688731306400, "endTime": 4688731796700}, "additional": {"children": [], "state": "success", "detailId": "3e332a85-39c4-46a0-9c5d-f922e139c5ee", "logId": "2b39edf9-04b0-4f09-918b-e2dae5d58eec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}}}, {"head": {"id": "3e332a85-39c4-46a0-9c5d-f922e139c5ee", "name": "create entry:compileNative task", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4688731079300}, "additional": {"logType": "detail", "children": [], "durationId": "1e11ac32-d1b1-4e6e-b9d5-9280d5a43da0"}}, {"head": {"id": "1a8478b5-ea77-4c3a-89be-a4c25713ca76", "name": "entry : compileNative start {\n  rss: 294776832,\n  heapTotal: 126763008,\n  heapUsed: 94393608,\n  external: 1708034,\n  arrayBuffers: 71704\n}", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4688731269700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b374847-033c-4d16-8549-c45544b56c93", "name": "Executing task :entry:compileNative", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4688731553900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b36704b-ca48-4c7b-8eee-571b012dfe4a", "name": "entry : compileNative end {\n  rss: 294780928,\n  heapTotal: 126763008,\n  heapUsed: 94402848,\n  external: 1708034,\n  arrayBuffers: 71704\n}", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4688731749500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b39edf9-04b0-4f09-918b-e2dae5d58eec", "name": "Finished :entry:compileNative", "description": "Compile the hook task for native resources.", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4688731306400, "endTime": 4688731796700}, "additional": {"logType": "info", "children": [], "durationId": "1e11ac32-d1b1-4e6e-b9d5-9280d5a43da0"}}, {"head": {"id": "0ccf51a4-1f50-49e1-842e-9a864f6d4ee3", "name": "BUILD SUCCESSFUL in 2 s 711 ms ", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4688732358900}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "be7018c5-f8c4-4d79-8f40-90eea7a90152", "name": "compileNative", "description": "", "type": "mark"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4686021984900, "endTime": 4688733287700}, "additional": {"time": {"year": 2025, "month": 6, "day": 30, "hour": 16, "minute": 2}, "markType": "history", "category": "build", "state": "success"}}, {"head": {"id": "7819b535-9dc3-4bf2-84b4-ddf5dab35354", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 28700, "tid": "Main Thread", "startTime": 4688733510200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}