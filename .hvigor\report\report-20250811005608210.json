{"version": "1.0", "events": [{"head": {"id": "b9ba274f-ad94-4092-88f0-73c2c9764659", "name": "hvigor daemon: Set daemon process log level, logLevel=INFO", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968592613300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7b90e21-e2aa-4fb8-905f-0d6e352b47a2", "name": "45000 is occupied by h<PERSON><PERSON> daemon.", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968606994500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "728b832f-2016-40fa-bdd1-d43f80dc41dd", "name": "hvigor daemon: Server start to listen. port=45001", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968669687600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a4724f4-eb71-408a-8a2d-0075530186bb", "name": "hvigor daemon: listening connection. address=127.0.0.1, port=45001.", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968673884900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "402c42f8-66c0-44ba-b75d-c9585fba34aa", "name": "hvigor daemon: Create default daemon info. execArgv=", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968675114000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80fa3809-82f1-4b95-b983-9f2f52fc33df", "name": "hvigor daemon: Socket is connected. socketId=48bOlaMnkdqfIfBIAAAB, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968744478800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6636f265-89fe-41ea-a15a-15bec5820aa9", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"47f7981e370357f2c7aa326d58cfb44e2e833542\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\mainwork\\\\harUse\\\\haruse2\\\\aboutLibrary\\\\fileManagerNew\",\"nodeVersion\":\"v16.20.0\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45003,\"pid\":16120,\"state\":\"idle\",\"lastUsedTime\":1754469218682,\"info\":null,\"createdBy\":\"command\",\"sessionId\":\"0000005041a745303cc9ae5b7e5b7e0f909b9b8808894b781a4eb153cec7c4db88b6f0960ef623ab564e6bdc7362192a200e7fd1efa9279efc180e63763b9bd5f7e75aa99ca450066b47a453fe0bb56da1ca783612eef9b149023bab95e7ec80\"},{\"keyId\":\"b1774fc56248e783066ec75fccbea8bfba5b808c\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\releaseALL\\\\camera_js_confirm\\\\release\\\\0718\\\\streamDemo\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45001,\"pid\":13352,\"state\":\"stopped\",\"lastUsedTime\":1754844082862,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"0000005001b3202f9316a0f86f50ffeafda73f43f7cb6694d458fbf7eff5eb17fe1861e51bc0479a4f8f74cb7fb1b828d1081bce5a78e8a37066183ee8eb10c2f9e1b6f70452f6b008a55c26a456527bee0581ad00bf27c264fc5be4682bdd26\"},{\"keyId\":\"d137b57111b858ceb201567d9fa2127a787615b7\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\mainwork\\\\globalState\\\\baseFMtoHar\\\\FileManager\",\"nodeVersion\":\"v16.20.0\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":13756,\"state\":\"idle\",\"lastUsedTime\":1754466045323,\"info\":null,\"createdBy\":\"command\",\"sessionId\":\"00000050c99395b24d30e58476bd2933e35ab3cef5883cd24b8979b0e6e25282ee32c2c8f439bb9418080b50000d91ac12539c082c58841b48336eb7e736050f512f733470e679ba0812b0e9fdd1b1dfc03cdafc034ba0f21980bf3703f96f41\"},{\"pid\":2476,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\c++\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"45b8b85882f9f1ef396fdc818a245a32817c7c30\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45001,\"lastUsedTime\":1754844962661,\"createdBy\":\"deveco\",\"sessionId\":\"000000506914df782466d6be55b667828cc225b5f766b2c3522794e9a77d67008c825272f8005b414be10e41954a03cef665aa1d23f76106f6774ebd32a990259a66c290fabf7b5a55c347f7f870c28b2e8e462dc5bc345f114173d9d2c761bf\"}]", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968746486400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dcbef67-e689-40c0-b675-7ebcdbba70d6", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2476,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\c++\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"45b8b85882f9f1ef396fdc818a245a32817c7c30\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45001,\"lastUsedTime\":1754844962661,\"createdBy\":\"deveco\",\"sessionId\":\"000000506914df782466d6be55b667828cc225b5f766b2c3522794e9a77d67008c825272f8005b414be10e41954a03cef665aa1d23f76106f6774ebd32a990259a66c290fabf7b5a55c347f7f870c28b2e8e462dc5bc345f114173d9d2c761bf\"}", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968747841100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ebee29e-ea04-4d26-a5e0-b9b67cdb9cdc", "name": "set active socket. socketId=48bOlaMnkdqfIfBIAAAB", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968759686200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b28b3e8-4987-4b74-ab7a-32ea9b023a16", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2476,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\c++\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"45b8b85882f9f1ef396fdc818a245a32817c7c30\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45001,\"lastUsedTime\":1754844962729,\"createdBy\":\"deveco\",\"sessionId\":\"000000506914df782466d6be55b667828cc225b5f766b2c3522794e9a77d67008c825272f8005b414be10e41954a03cef665aa1d23f76106f6774ebd32a990259a66c290fabf7b5a55c347f7f870c28b2e8e462dc5bc345f114173d9d2c761bf\"}", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968761207200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8edbc3c6-6264-4d7a-a152-d03e7e2e2993", "name": "hvigor daemon: Receive data from client. data={\n  prop: [ 'product=default' ],\n  sync: true,\n  parallel: true,\n  incremental: true,\n  daemon: true,\n  _: []\n}.", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968766210200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d7b1609-bde3-4d6e-88a1-1dbf27c362b6", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968766888600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "975979a7-5651-4d29-bc04-f478b515ea17", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2476,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\c++\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"45b8b85882f9f1ef396fdc818a245a32817c7c30\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45001,\"lastUsedTime\":1754844962742,\"createdBy\":\"deveco\",\"sessionId\":\"000000506914df782466d6be55b667828cc225b5f766b2c3522794e9a77d67008c825272f8005b414be10e41954a03cef665aa1d23f76106f6774ebd32a990259a66c290fabf7b5a55c347f7f870c28b2e8e462dc5bc345f114173d9d2c761bf\"}", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968767826200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77009834-d1d3-4bc2-b7d7-204f4be56543", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968780541100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5b5e5a9-31e8-4dc8-bfe4-076b150df308", "name": "Cache service initialization finished in 30 ms ", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968810917700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5b649f6-4da4-425b-be89-6bb985ba67e1", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\baseCamera\\c++\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968853741600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c690ba8f-5461-4740-b695-67a27953d487", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 973831790300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fafa7b6-5e6e-4400-bc09-8c82d98e8032", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 973832186900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "435a8bde-b5e8-4024-849a-03462ab553f9", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 974210201500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "893a7624-f54e-4e7c-beec-5a90015b3815", "name": "Invalid project path.\r\n\t Detail: Please move the project to a valid path", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 974210295400}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "6dfec028-a825-482d-ace3-82f350f82d35", "name": "ERROR: stacktrace = Error: Invalid project path.\r\n\t Detail: Please move the project to a valid path\n    at OhosLogger.errorMessageExit (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor\\src\\base\\log\\hvigor-log.js:1:2347)\n    at OhosLogger._printAllExit (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor-ohos-plugin@3.2.4_@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor-ohos-plugin\\src\\utils\\log\\ohos-logger.js:1:1174)\n    at ProjectInspection.exitOnError (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor-ohos-plugin@3.2.4_@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor-ohos-plugin\\src\\tasks\\inspection\\project-inspection.js:1:672)\n    at AppPlugin.doProjectInspection (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor-ohos-plugin@3.2.4_@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor-ohos-plugin\\src\\plugin\\common\\abstract-project-plugin.js:1:1328)\n    at PluginFactory.getAppPlugin (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor-ohos-plugin@3.2.4_@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor-ohos-plugin\\src\\plugin\\factory\\plugin-factory.js:1:925)\n    at appTasks (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor-ohos-plugin@3.2.4_@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor-ohos-plugin\\index.js:1:572)\n    at bindSystemPlugins (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor\\src\\base\\internal\\lifecycle\\configuration.js:1:2533)\n    at evaluateNodeVigorFile (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor\\src\\base\\internal\\lifecycle\\configuration.js:1:5079)\n    at configuration (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor\\src\\base\\internal\\lifecycle\\configuration.js:1:1277)\n    at start (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor\\src\\base\\boot\\index.js:1:1876)", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 974212192100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "509fba49-e2a0-48d2-b4bb-d69c800943c2", "name": "", "description": "", "type": "mark"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 968772771400, "endTime": 974212729700}, "additional": {"time": {"year": 2025, "month": 8, "day": 11, "hour": 0, "minute": 56}, "markType": "history", "category": "build", "state": "failed"}}, {"head": {"id": "1a6d408c-17cc-4106-9f67-165d957489ed", "name": "BUILD FAILED in 5 s 440 ms ", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 974212840300}, "additional": {"logType": "error", "children": []}}], "workLog": []}