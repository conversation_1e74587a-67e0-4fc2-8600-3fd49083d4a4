{"version": "1.0", "events": [{"head": {"id": "30c8e088-e962-4459-9a7a-153a72c615f5", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 974234731500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47093e99-a156-4496-9baf-14db7619489c", "name": "hvigor disconnect:  client namespace disconnect", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 974238998200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6027cf68-6f25-477b-8188-7388004ccf4a", "name": "hvigor daemon: Socket will be closed. socketId=48bOlaMnkdqfIfBIAAAB, connectionCount=0", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 974241335800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94b7b0eb-f4cb-48e9-8b52-3db5a554e3b1", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2476,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\c++\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"45b8b85882f9f1ef396fdc818a245a32817c7c30\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45001,\"lastUsedTime\":1754844968211,\"createdBy\":\"deveco\",\"sessionId\":\"000000506914df782466d6be55b667828cc225b5f766b2c3522794e9a77d67008c825272f8005b414be10e41954a03cef665aa1d23f76106f6774ebd32a990259a66c290fabf7b5a55c347f7f870c28b2e8e462dc5bc345f114173d9d2c761bf\"}", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 974242539100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "499e7312-5fdd-4613-80e8-f980343a44af", "name": "worker[1] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 974246664800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "514bc110-18bb-4db3-9309-0d3b9f1826fe", "name": "worker[0] exit with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 974246848400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a63c595-8ad3-4bc0-a75b-f7a048db9cb5", "name": "hvigor daemon: Socket is connected. socketId=IunZ-c9cb5C2hlpvAAAD, connectionCount=1", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975244162600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3264e6fd-5196-4c29-bc01-bbda86b686ae", "name": "hvigor daemon: daemonState=idle \n      daemonInfo=[{\"keyId\":\"47f7981e370357f2c7aa326d58cfb44e2e833542\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\mainwork\\\\harUse\\\\haruse2\\\\aboutLibrary\\\\fileManagerNew\",\"nodeVersion\":\"v16.20.0\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45003,\"pid\":16120,\"state\":\"idle\",\"lastUsedTime\":1754469218682,\"info\":null,\"createdBy\":\"command\",\"sessionId\":\"0000005041a745303cc9ae5b7e5b7e0f909b9b8808894b781a4eb153cec7c4db88b6f0960ef623ab564e6bdc7362192a200e7fd1efa9279efc180e63763b9bd5f7e75aa99ca450066b47a453fe0bb56da1ca783612eef9b149023bab95e7ec80\"},{\"keyId\":\"b1774fc56248e783066ec75fccbea8bfba5b808c\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\releaseALL\\\\camera_js_confirm\\\\release\\\\0718\\\\streamDemo\\\\Camera_js\",\"nodeVersion\":\"v18.14.1\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45001,\"pid\":13352,\"state\":\"stopped\",\"lastUsedTime\":1754844082862,\"info\":\"Project closed and this daemon was created by Deveco.\",\"createdBy\":\"deveco\",\"sessionId\":\"0000005001b3202f9316a0f86f50ffeafda73f43f7cb6694d458fbf7eff5eb17fe1861e51bc0479a4f8f74cb7fb1b828d1081bce5a78e8a37066183ee8eb10c2f9e1b6f70452f6b008a55c26a456527bee0581ad00bf27c264fc5be4682bdd26\"},{\"keyId\":\"d137b57111b858ceb201567d9fa2127a787615b7\",\"hvigorVersion\":\"3.2.4\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\mainwork\\\\globalState\\\\baseFMtoHar\\\\FileManager\",\"nodeVersion\":\"v16.20.0\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"address\":\"127.0.0.1\",\"port\":45000,\"pid\":13756,\"state\":\"idle\",\"lastUsedTime\":1754466045323,\"info\":null,\"createdBy\":\"command\",\"sessionId\":\"00000050c99395b24d30e58476bd2933e35ab3cef5883cd24b8979b0e6e25282ee32c2c8f439bb9418080b50000d91ac12539c082c58841b48336eb7e736050f512f733470e679ba0812b0e9fdd1b1dfc03cdafc034ba0f21980bf3703f96f41\"},{\"pid\":2476,\"state\":\"idle\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\c++\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"45b8b85882f9f1ef396fdc818a245a32817c7c30\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45001,\"lastUsedTime\":1754844968223,\"createdBy\":\"deveco\",\"sessionId\":\"000000506914df782466d6be55b667828cc225b5f766b2c3522794e9a77d67008c825272f8005b414be10e41954a03cef665aa1d23f76106f6774ebd32a990259a66c290fabf7b5a55c347f7f870c28b2e8e462dc5bc345f114173d9d2c761bf\"}]", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975245578000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7c08a7d-cf27-4996-a76d-3d22b1e77715", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2476,\"state\":\"half_busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\c++\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"45b8b85882f9f1ef396fdc818a245a32817c7c30\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45001,\"lastUsedTime\":1754844968223,\"createdBy\":\"deveco\",\"sessionId\":\"000000506914df782466d6be55b667828cc225b5f766b2c3522794e9a77d67008c825272f8005b414be10e41954a03cef665aa1d23f76106f6774ebd32a990259a66c290fabf7b5a55c347f7f870c28b2e8e462dc5bc345f114173d9d2c761bf\"}", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975247034700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b543be55-7941-4ac0-9054-755c113adbf1", "name": "set active socket. socketId=IunZ-c9cb5C2hlpvAAAD", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975251522300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b837b1d1-209e-42dc-9a9e-140c8213b8db", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2476,\"state\":\"stopReq\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\c++\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"45b8b85882f9f1ef396fdc818a245a32817c7c30\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45001,\"lastUsedTime\":1754844969228,\"createdBy\":\"deveco\",\"sessionId\":\"000000506914df782466d6be55b667828cc225b5f766b2c3522794e9a77d67008c825272f8005b414be10e41954a03cef665aa1d23f76106f6774ebd32a990259a66c290fabf7b5a55c347f7f870c28b2e8e462dc5bc345f114173d9d2c761bf\"}", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975252216600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ec8d462-4455-48d1-8b24-0b9c05d9550d", "name": "hvigor daemon: Receive data from client. data={\n  mode: 'module',\n  parallel: true,\n  prop: [ 'module=entry', 'product=default' ],\n  incremental: true,\n  _: [ 'compileNative' ],\n  daemon: true\n}.", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975257370800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "447fd27d-88b7-42a7-b99b-7e0c3cf79436", "name": "hvigor daemon: Build start.", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975258264900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64a6f96c-1dc9-43b8-bb69-878a74c2aa5f", "name": "hvigor daemon: Update daemon info in registry. daemonInfo={\"pid\":2476,\"state\":\"busy\",\"address\":\"127.0.0.1\",\"cwdPath\":\"D:\\\\harmonyforwork\\\\aboutCamera\\\\baseCamera\\\\c++\\\\Camera\",\"hvigorVersion\":\"3.2.4\",\"keyId\":\"45b8b85882f9f1ef396fdc818a245a32817c7c30\",\"nodeParams\":\"a3c7cac23ce8db547a7b07254455e2f6\",\"nodeVersion\":\"v18.14.1\",\"port\":45001,\"lastUsedTime\":1754844969233,\"createdBy\":\"deveco\",\"sessionId\":\"000000506914df782466d6be55b667828cc225b5f766b2c3522794e9a77d67008c825272f8005b414be10e41954a03cef665aa1d23f76106f6774ebd32a990259a66c290fabf7b5a55c347f7f870c28b2e8e462dc5bc345f114173d9d2c761bf\"}", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975259334800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c3e25f1-d1e2-4e49-8c77-576594784176", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' },\n  enableSignTask: true\n}", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975264583900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5583b797-7c97-4211-aa06-b8b3bc325e94", "name": "Cache service initialization finished in 12 ms ", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975276037700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49c5b9e4-582b-4fc8-813a-fe35809372ae", "name": "hvigorfile, resolving D:\\harmonyforwork\\aboutCamera\\baseCamera\\c++\\Camera\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975284788100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c471ef80-d5d2-42ac-8b21-55b2908b5f60", "name": "hvigorfile, require result:  { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975304866900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fd39cc4-e7cb-499c-86f7-f4d298bbc50e", "name": "hvigorfile, binding system plugins { appTasks: [Getter] }", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975305081600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e4c2125-d5ad-4b1a-a26b-cefb8d07243b", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975318399400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84c0922a-d92e-4ec5-9488-fb91ed7dd2d6", "name": "Invalid project path.\r\n\t Detail: Please move the project to a valid path", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975318425000}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "26ce3068-7870-4d61-a482-edb729c0b53b", "name": "ERROR: stacktrace = Error: Invalid project path.\r\n\t Detail: Please move the project to a valid path\n    at OhosLogger.errorMessageExit (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor\\src\\base\\log\\hvigor-log.js:1:2347)\n    at OhosLogger._printAllExit (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor-ohos-plugin@3.2.4_@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor-ohos-plugin\\src\\utils\\log\\ohos-logger.js:1:1174)\n    at ProjectInspection.exitOnError (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor-ohos-plugin@3.2.4_@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor-ohos-plugin\\src\\tasks\\inspection\\project-inspection.js:1:672)\n    at AppPlugin.doProjectInspection (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor-ohos-plugin@3.2.4_@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor-ohos-plugin\\src\\plugin\\common\\abstract-project-plugin.js:1:1328)\n    at PluginFactory.getAppPlugin (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor-ohos-plugin@3.2.4_@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor-ohos-plugin\\src\\plugin\\factory\\plugin-factory.js:1:925)\n    at appTasks (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor-ohos-plugin@3.2.4_@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor-ohos-plugin\\index.js:1:572)\n    at bindSystemPlugins (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor\\src\\base\\internal\\lifecycle\\configuration.js:1:2533)\n    at evaluateNodeVigorFile (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor\\src\\base\\internal\\lifecycle\\configuration.js:1:5079)\n    at configuration (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor\\src\\base\\internal\\lifecycle\\configuration.js:1:1277)\n    at start (C:\\Users\\<USER>\\.hvigor\\project_caches\\883ff35a95baac36e903ba0f9d6852bd\\workspace\\node_modules\\.pnpm\\@ohos+hvigor@3.2.4\\node_modules\\@ohos\\hvigor\\src\\base\\boot\\index.js:1:1876)", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975319463400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "545dc1f9-0ad4-4ff8-830f-eab75720d2f6", "name": "compileNative", "description": "", "type": "mark"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975263148100, "endTime": 975319590900}, "additional": {"time": {"year": 2025, "month": 8, "day": 11, "hour": 0, "minute": 56}, "markType": "history", "category": "build", "state": "failed"}}, {"head": {"id": "97475865-358c-4055-a296-cc720bccbb5e", "name": "BUILD FAILED in 57 ms ", "description": "", "type": "log"}, "body": {"pid": 2476, "tid": "Main Thread", "startTime": 975319637700}, "additional": {"logType": "error", "children": []}}], "workLog": []}