[{"file": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\cpp\\main.cpp", "directory": "D:/harmonyforwork/aboutCamera/baseCamera/Camera/entry/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -Dentry_EXPORTS -ID:/harmonyforwork/aboutCamera/baseCamera/Camera/entry/src/main/cpp -ID:/harmonyforwork/aboutCamera/baseCamera/Camera/entry/src/main/cpp/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security   -D__MUSL__ -O0 -g -fno-limit-debug-info  -fPIC   -o CMakeFiles\\entry.dir\\main.cpp.o -c D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\cpp\\main.cpp"}, {"file": "D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\cpp\\camera_manager.cpp", "directory": "D:/harmonyforwork/aboutCamera/baseCamera/Camera/entry/.cxx/default/default/arm64-v8a", "command": "D:\\harmonyFor\\openSDK\\11\\native\\llvm\\bin\\clang++.exe --target=aarch64-linux-ohos --gcc-toolchain=D:/harmonyFor/openSDK/11/native/llvm --sysroot=D:/harmonyFor/openSDK/11/native/sysroot  -Dentry_EXPORTS -ID:/harmonyforwork/aboutCamera/baseCamera/Camera/entry/src/main/cpp -ID:/harmonyforwork/aboutCamera/baseCamera/Camera/entry/src/main/cpp/include  -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security   -D__MUSL__ -O0 -g -fno-limit-debug-info  -fPIC   -o CMakeFiles\\entry.dir\\camera_manager.cpp.o -c D:\\harmonyforwork\\aboutCamera\\baseCamera\\Camera\\entry\\src\\main\\cpp\\camera_manager.cpp"}]