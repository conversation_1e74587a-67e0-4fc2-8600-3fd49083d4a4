/*
 * Copyright (c) 2023-2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the 'License');
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an 'AS IS' BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

{
  "app": {
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compileSdkVersion": 11,
        "compatibleSdkVersion": 11,
        "runtimeOS": "OpenHarmony"
      }
    ],
    "signingConfigs": [
      {
        "name": "default",
        "material": {
          "certpath": "C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.cer",
          "storePassword": "0000001BF6C6DB4400328BFB5E2823AF6A1116CFEFDEA89C3E7FE26AF9EBE9C2DFFC3FD37E218940A8355C",
          "keyAlias": "debugKey",
          "keyPassword": "0000001B2757CA7CCC65F317C7F09EA6979E0FCA80AA2053DD35DEC957F0365A517F577DE6506C8531D412",
          "profile": "C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "C:/Users/<USER>/.ohos/config/openharmony/default_Camera_kNKJQkJpnyotQaZOSfRfyVb37vqWzOqpqdu4rRnOQYg=.p12"
        }
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    }
  ]
}
